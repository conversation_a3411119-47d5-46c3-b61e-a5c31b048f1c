import React from "react";
import type { PieceType, PieceColor } from "../types/chess";
import "./PromotionDialog.css";

interface PromotionDialogProps {
  color: PieceColor;
  onSelect: (pieceType: PieceType) => void;
  onCancel: () => void;
  isVisible: boolean;
}

const PromotionDialog: React.FC<PromotionDialogProps> = ({
  color,
  onSelect,
  onCancel,
  isVisible,
}) => {
  if (!isVisible) return null;

  // Opciones de promoción (no se puede promover a rey o peón)
  const promotionOptions: PieceType[] = ["queen", "rook", "bishop", "knight"];

  // Obtener símbolo Unicode para cada pieza
  const getPieceSymbol = (type: PieceType, color: PieceColor): string => {
    const symbols = {
      white: {
        queen: "♕",
        rook: "♖",
        bishop: "♗",
        knight: "♘",
      },
      black: {
        queen: "♛",
        rook: "♜",
        bishop: "♝",
        knight: "♞",
      },
    };

    return symbols[color][type as keyof (typeof symbols)[typeof color]];
  };

  // Obtener nombre de la pieza en español
  const getPieceName = (type: PieceType): string => {
    const names = {
      queen: "Reina",
      rook: "Torre",
      bishop: "Alfil",
      knight: "Caballo",
    };

    return names[type as keyof typeof names];
  };

  return (
    <div className="promotion-dialog-overlay">
      <div className="promotion-dialog">
        <div className="promotion-dialog__header">
          <h3>Promoción de Peón</h3>
          <p>Elige la pieza a la que quieres promover tu peón:</p>
        </div>

        <div className="promotion-dialog__options">
          {promotionOptions.map((pieceType) => (
            <button
              key={pieceType}
              className={`promotion-option promotion-option--${color}`}
              onClick={() => onSelect(pieceType)}
              title={getPieceName(pieceType)}
            >
              <span className="promotion-option__symbol">
                {getPieceSymbol(pieceType, color)}
              </span>
              <span className="promotion-option__name">
                {getPieceName(pieceType)}
              </span>
            </button>
          ))}
        </div>

        <div className="promotion-dialog__actions">
          <button className="promotion-dialog__cancel" onClick={onCancel}>
            Cancelar
          </button>
        </div>
      </div>
    </div>
  );
};

export default PromotionDialog;
