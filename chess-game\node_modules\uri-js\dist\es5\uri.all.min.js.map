{"version": 3, "sources": ["../../src/util.ts", "../../src/regexps-uri.ts", "../../node_modules/punycode/punycode.es6.js", "../../src/uri.ts", "../../src/schemes/ws.ts", "../../src/schemes/mailto.ts", "../../src/regexps-iri.ts", "../../src/schemes/http.ts", "../../src/schemes/https.ts", "../../src/schemes/wss.ts", "../../src/schemes/urn.ts", "../../src/schemes/urn-uuid.ts", "../../src/index.ts"], "names": ["merge", "sets", "Array", "_len", "_key", "arguments", "length", "slice", "xl", "x", "join", "subexp", "str", "typeOf", "o", "undefined", "Object", "prototype", "toString", "call", "split", "pop", "shift", "toLowerCase", "toUpperCase", "toArray", "obj", "setInterval", "assign", "target", "source", "key", "buildExps", "isIRI", "HEXDIG$$", "PCT_ENCODED$", "SUB_DELIMS$$", "RESERVED$$", "UCSCHAR$$", "DEC_OCTET_RELAXED$", "H16$", "LS32$", "IPV4ADDRESS$", "IPV6ADDRESS1$", "IPV6ADDRESS2$", "IPV6ADDRESS3$", "IPV6ADDRESS4$", "IPV6ADDRESS5$", "IPV6ADDRESS6$", "IPV6ADDRESS7$", "IPV6ADDRESS8$", "IPV6ADDRESS9$", "ZONEID$", "UNRESERVED$$", "RegExp", "IPRIVATE$$", "IPV6ADDRESS$", "error", "type", "RangeError", "errors", "map", "array", "fn", "result", "mapDomain", "string", "parts", "replace", "regexSeparators", "ucs2decode", "output", "counter", "value", "charCodeAt", "extra", "push", "pctEncChar", "chr", "c", "pctDecChars", "newStr", "i", "il", "parseInt", "substr", "String", "fromCharCode", "c2", "c3", "_normalizeComponentEncoding", "components", "protocol", "decodeUnreserved", "decStr", "match", "UNRESERVED", "scheme", "PCT_ENCODED", "NOT_SCHEME", "userinfo", "NOT_USERINFO", "host", "NOT_HOST", "path", "NOT_PATH", "NOT_PATH_NOSCHEME", "query", "NOT_QUERY", "fragment", "NOT_FRAGMENT", "_stripLeadingZeros", "_normalizeIPv4", "matches", "IPV4ADDRESS", "address", "_matches", "_normalizeIPv6", "IPV6ADDRESS", "_matches2", "zone", "reverse", "last", "_address$toLowerCase$2", "first", "firstFields", "lastFields", "isLastFieldIPv4Address", "test", "fieldCount", "lastFieldsStart", "fields", "allZeroFields", "reduce", "acc", "field", "index", "lastLongest", "longestZeroFields", "sort", "a", "b", "newHost", "newFirst", "newLast", "parse", "uriString", "options", "iri", "IRI_PROTOCOL", "URI_PROTOCOL", "reference", "URI_PARSE", "NO_MATCH_IS_UNDEFINED", "port", "isNaN", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "SCHEMES", "unicodeSupport", "domainHost", "punycode", "toASCII", "e", "_recomposeAuthority", "uri<PERSON><PERSON>s", "_", "$1", "$2", "removeDotSegments", "input", "RDS1", "RDS2", "RDS3", "im", "RDS5", "Error", "s", "serialize", "toUnicode", "authority", "char<PERSON>t", "absolutePath", "resolveComponents", "base", "relative", "skipNormalization", "tolerant", "lastIndexOf", "resolve", "baseURI", "relativeURI", "schemelessOptions", "normalize", "uri", "equal", "uriA", "uriB", "escapeComponent", "ESCAPE", "unescapeComponent", "isSecure", "wsComponents", "secure", "maxInt", "regexPunycode", "regexNonASCII", "floor", "Math", "stringFromCharCode", "ucs2encode", "fromCodePoint", "apply", "toConsumableArray", "basicToDigit", "codePoint", "digitToBasic", "digit", "flag", "adapt", "delta", "numPoints", "firstTime", "k", "baseMinusTMin", "decode", "inputLength", "n", "bias", "basic", "j", "oldi", "w", "t", "baseMinusT", "out", "splice", "encode", "_step", "Symbol", "iterator", "_iteratorNormalCompletion", "_iterator", "next", "done", "currentValue", "basicLength", "handledCPCount", "m", "_step2", "_iteratorNormalCompletion2", "_iterator2", "handledCPCountPlusOne", "_step3", "_iteratorNormalCompletion3", "_iterator3", "q", "qMinusT", "handler", "http", "resourceName", "_wsComponents$resourc2", "ws", "O", "VCHAR$$", "NOT_LOCAL_PART", "NOT_HFNAME", "NOT_HFVALUE", "mailtoComponents", "to", "unknownHeaders", "headers", "hfields", "hfield", "toAddrs", "subject", "body", "addr", "toAddr", "atIdx", "localPart", "domain", "name", "URN_PARSE", "urnComponents", "nid", "nss", "urnScheme", "uriComponents", "UUID", "uuidComponents", "uuid", "https", "wss", "mailto", "urn"], "mappings": ";4LAAA,SAAAA,gCAAyBC,EAAzBC,MAAAC,GAAAC,EAAA,EAAAA,EAAAD,EAAAC,MAAAA,GAAAC,UAAAD,MACKH,EAAKK,OAAS,EAAG,GACf,GAAKL,EAAK,GAAGM,MAAM,GAAI,OAEvB,GADCC,GAAKP,EAAKK,OAAS,EAChBG,EAAI,EAAGA,EAAID,IAAMC,IACpBA,GAAKR,EAAKQ,GAAGF,MAAM,GAAI,YAExBC,GAAMP,EAAKO,GAAID,MAAM,GACnBN,EAAKS,KAAK,UAEVT,GAAK,GAId,QAAAU,GAAuBC,SACf,MAAQA,EAAM,IAGtB,QAAAC,GAAuBC,SACfA,KAAMC,UAAY,YAAqB,OAAND,EAAa,OAASE,OAAOC,UAAUC,SAASC,KAAKL,GAAGM,MAAM,KAAKC,MAAMD,MAAM,KAAKE,QAAQC,cAGrI,QAAAC,GAA4BZ,SACpBA,GAAIY,cAGZ,QAAAC,GAAwBC,SAChBA,KAAQX,WAAqB,OAARW,EAAgBA,YAAexB,OAAQwB,EAA6B,gBAAfA,GAAIpB,QAAuBoB,EAAIN,OAASM,EAAIC,aAAeD,EAAIP,MAAQO,GAAOxB,MAAMe,UAAUV,MAAMY,KAAKO,MAI3L,QAAAE,GAAuBC,EAAgBC,MAChCJ,GAAMG,KACRC,MACE,GAAMC,KAAOD,KACbC,GAAOD,EAAOC,SAGbL,GCnCR,QAAAM,GAA0BC,MAMxBC,GAAWlC,EAFD,QAEgB,YAG1BmC,EAAexB,EAAOA,EAAO,UAAYuB,EAAW,IAAMA,EAAWA,EAAW,IAAMA,EAAWA,GAAY,IAAMvB,EAAO,cAAgBuB,EAAW,IAAMA,EAAWA,GAAY,IAAMvB,EAAO,IAAMuB,EAAWA,IAEhNE,EAAe,sCACfC,EAAarC,EAFE,0BAEkBoC,GACjCE,EAAYL,EAAQ,8EAAgF,OACvFA,EAAQ,oBAAsB,OAC5BjC,EAbL,WAEA,QAW6B,iBAAkBsC,GAIzDC,EAAqB5B,EAAOA,EAAO,WAAa,IAAMA,EAAO,eAAsB,IAAMA,EAAO,eAA2B,IAAMA,EAAO,gBAAuB,gBAChJA,EAAO4B,EAAqB,MAAQA,EAAqB,MAAQA,EAAqB,MAAQA,GAC7GC,EAAO7B,EAAOuB,EAAW,SACzBO,EAAQ9B,EAAOA,EAAO6B,EAAO,MAAQA,GAAQ,IAAME,GACnDC,EAAgBhC,EAAmEA,EAAO6B,EAAO,OAAS,MAAQC,KAClG9B,EAAwD,SAAWA,EAAO6B,EAAO,OAAS,MAAQC,KAClG9B,EAAOA,EAAwC6B,GAAQ,UAAY7B,EAAO6B,EAAO,OAAS,MAAQC,KAClG9B,EAAOA,EAAOA,EAAO6B,EAAO,OAAS,QAAUA,GAAQ,UAAY7B,EAAO6B,EAAO,OAAS,MAAQC,KAClG9B,EAAOA,EAAOA,EAAO6B,EAAO,OAAS,QAAUA,GAAQ,UAAY7B,EAAO6B,EAAO,OAAS,MAAQC,KAClG9B,EAAOA,EAAOA,EAAO6B,EAAO,OAAS,QAAUA,GAAQ,UAAmBA,EAAO,MAAiBC,KAClG9B,EAAOA,EAAOA,EAAO6B,EAAO,OAAS,QAAUA,GAAQ,UAA2CC,KAClG9B,EAAOA,EAAOA,EAAO6B,EAAO,OAAS,QAAUA,GAAQ,UAA2CA,KAClG7B,EAAOA,EAAOA,EAAO6B,EAAO,OAAS,QAAUA,GAAQ,aACxD7B,GAAQgC,EAAeC,EAAeC,EAAeC,EAAeC,EAAeC,EAAeC,EAAeC,EAAeC,GAAezC,KAAK,MACnK0C,EAAUzC,EAAOA,EAAO0C,EAAe,IAAMlB,GAAgB,uBAoChD,GAAImB,QAAOtD,EAAM,MAnEpB,WAEA,QAiE6C,eAAgB,kBACxD,GAAIsD,QAAOtD,EAAM,YAAaqD,EAAcjB,GAAe,cAC/D,GAAIkB,QAAOtD,EAAM,kBAAmBqD,EAAcjB,GAAe,cACjE,GAAIkB,QAAOtD,EAAM,kBAAmBqD,EAAcjB,GAAe,uBACxD,GAAIkB,QAAOtD,EAAM,eAAgBqD,EAAcjB,GAAe,eACtE,GAAIkB,QAAOtD,EAAM,SAAUqD,EAAcjB,EAAc,iBAAkBmB,GAAa,kBACnF,GAAID,QAAOtD,EAAM,SAAUqD,EAAcjB,EAAc,kBAAmB,YAChF,GAAIkB,QAAOtD,EAAM,MAAOqD,EAAcjB,GAAe,gBACjD,GAAIkB,QAAOD,EAAc,iBACxB,GAAIC,QAAOtD,EAAM,SAAUqD,EAAchB,GAAa,iBACtD,GAAIiB,QAAOnB,EAAc,iBACzB,GAAImB,QAAO,KAAOZ,EAAe,kBACjC,GAAIY,QAAO,SAAWE,EAAe,IAAM7C,EAAOA,EAAO,eAAiBuB,EAAW,QAAU,IAAMkB,EAAU,KAAO,WC5CtI,QAASK,GAAMC,QACR,IAAIC,YAAWC,EAAOF,IAW7B,QAASG,GAAIC,EAAOC,UACbC,MACF1D,EAASwD,EAAMxD,OACZA,OACCA,GAAUyD,EAAGD,EAAMxD,UAEpB0D,GAaR,QAASC,GAAUC,EAAQH,MACpBI,GAAQD,EAAO9C,MAAM,KACvB4C,EAAS,SACTG,GAAM7D,OAAS,MAGT6D,EAAM,GAAK,MACXA,EAAM,MAGPD,EAAOE,QAAQC,EAAiB,KAGlCL,EADSH,EADDK,EAAO9C,MAAM,KACA2C,GAAIrD,KAAK,KAiBtC,QAAS4D,GAAWJ,UACbK,MACFC,EAAU,EACRlE,EAAS4D,EAAO5D,OACfkE,EAAUlE,GAAQ,IAClBmE,GAAQP,EAAOQ,WAAWF,QAC5BC,GAAS,OAAUA,GAAS,OAAUD,EAAUlE,EAAQ,IAErDqE,GAAQT,EAAOQ,WAAWF,IACR,SAAX,MAARG,KACGC,OAAe,KAARH,IAAkB,KAAe,KAARE,GAAiB,UAIjDC,KAAKH,eAING,KAAKH,SAGPF,GC/BR,QAAAM,GAA2BC,MACpBC,GAAID,EAAIJ,WAAW,SAGrBK,GAAI,GAAQ,KAAOA,EAAE7D,SAAS,IAAIM,cAC7BuD,EAAI,IAAS,IAAMA,EAAE7D,SAAS,IAAIM,cAClCuD,EAAI,KAAU,KAAQA,GAAK,EAAK,KAAK7D,SAAS,IAAIM,cAAgB,KAAY,GAAJuD,EAAU,KAAK7D,SAAS,IAAIM,cACtG,KAAQuD,GAAK,GAAM,KAAK7D,SAAS,IAAIM,cAAgB,KAASuD,GAAK,EAAK,GAAM,KAAK7D,SAAS,IAAIM,cAAgB,KAAY,GAAJuD,EAAU,KAAK7D,SAAS,IAAIM,cAK9J,QAAAwD,GAA4BpE,UACvBqE,GAAS,GACTC,EAAI,EACFC,EAAKvE,EAAIN,OAER4E,EAAIC,GAAI,IACRJ,GAAIK,SAASxE,EAAIyE,OAAOH,EAAI,EAAG,GAAI,OAErCH,EAAI,OACGO,OAAOC,aAAaR,MACzB,MAED,IAAIA,GAAK,KAAOA,EAAI,IAAK,IACxBI,EAAKD,GAAM,EAAG,IACZM,GAAKJ,SAASxE,EAAIyE,OAAOH,EAAI,EAAG,GAAI,OAChCI,OAAOC,cAAmB,GAAJR,IAAW,EAAW,GAALS,WAEvC5E,EAAIyE,OAAOH,EAAG,MAEpB,MAED,IAAIH,GAAK,IAAK,IACbI,EAAKD,GAAM,EAAG,IACZM,GAAKJ,SAASxE,EAAIyE,OAAOH,EAAI,EAAG,GAAI,IACpCO,EAAKL,SAASxE,EAAIyE,OAAOH,EAAI,EAAG,GAAI,OAChCI,OAAOC,cAAmB,GAAJR,IAAW,IAAa,GAALS,IAAY,EAAW,GAALC,WAE3D7E,EAAIyE,OAAOH,EAAG,MAEpB,UAGKtE,EAAIyE,OAAOH,EAAG,MACnB,QAIAD,GAGR,QAAAS,GAAqCC,EAA0BC,WAC/DC,GAA2BjF,MACnBkF,GAASd,EAAYpE,SAClBkF,GAAOC,MAAMH,EAASI,YAAoBF,EAANlF,QAG1C+E,GAAWM,SAAQN,EAAWM,OAASX,OAAOK,EAAWM,QAAQ7B,QAAQwB,EAASM,YAAaL,GAAkBtE,cAAc6C,QAAQwB,EAASO,WAAY,KAC5JR,EAAWS,WAAarF,YAAW4E,EAAWS,SAAWd,OAAOK,EAAWS,UAAUhC,QAAQwB,EAASM,YAAaL,GAAkBzB,QAAQwB,EAASS,aAAcxB,GAAYT,QAAQwB,EAASM,YAAa1E,IAC9MmE,EAAWW,OAASvF,YAAW4E,EAAWW,KAAOhB,OAAOK,EAAWW,MAAMlC,QAAQwB,EAASM,YAAaL,GAAkBtE,cAAc6C,QAAQwB,EAASW,SAAU1B,GAAYT,QAAQwB,EAASM,YAAa1E,IAC5MmE,EAAWa,OAASzF,YAAW4E,EAAWa,KAAOlB,OAAOK,EAAWa,MAAMpC,QAAQwB,EAASM,YAAaL,GAAkBzB,QAASuB,EAAWM,OAASL,EAASa,SAAWb,EAASc,kBAAoB7B,GAAYT,QAAQwB,EAASM,YAAa1E,IACjPmE,EAAWgB,QAAU5F,YAAW4E,EAAWgB,MAAQrB,OAAOK,EAAWgB,OAAOvC,QAAQwB,EAASM,YAAaL,GAAkBzB,QAAQwB,EAASgB,UAAW/B,GAAYT,QAAQwB,EAASM,YAAa1E,IAClMmE,EAAWkB,WAAa9F,YAAW4E,EAAWkB,SAAWvB,OAAOK,EAAWkB,UAAUzC,QAAQwB,EAASM,YAAaL,GAAkBzB,QAAQwB,EAASkB,aAAcjC,GAAYT,QAAQwB,EAASM,YAAa1E,IAE3MmE,EAGR,QAAAoB,GAA4BnG,SACpBA,GAAIwD,QAAQ,UAAW,OAAS,IAGxC,QAAA4C,GAAwBV,EAAaV,MAC9BqB,GAAUX,EAAKP,MAAMH,EAASsB,qBAChBD,EAFrB,GAEUE,EAFVC,EAAA,SAIKD,GACIA,EAAQ/F,MAAM,KAAKyC,IAAIkD,GAAoBrG,KAAK,KAEhD4F,EAIT,QAAAe,GAAwBf,EAAaV,MAC9BqB,GAAUX,EAAKP,MAAMH,EAAS0B,qBACVL,EAF3B,GAEUE,EAFVI,EAAA,GAEmBC,EAFnBD,EAAA,MAIKJ,EAAS,KASP,MARiBA,EAAQ5F,cAAcH,MAAM,MAAMqG,mBAAjDC,EADKC,EAAA,GACCC,EADDD,EAAA,GAENE,EAAcD,EAAQA,EAAMxG,MAAM,KAAKyC,IAAIkD,MAC3Ce,EAAaJ,EAAKtG,MAAM,KAAKyC,IAAIkD,GACjCgB,EAAyBnC,EAASsB,YAAYc,KAAKF,EAAWA,EAAWxH,OAAS,IAClF2H,EAAaF,EAAyB,EAAI,EAC1CG,EAAkBJ,EAAWxH,OAAS2H,EACtCE,EAASjI,MAAc+H,GAEpBxH,EAAI,EAAGA,EAAIwH,IAAcxH,IAC1BA,GAAKoH,EAAYpH,IAAMqH,EAAWI,EAAkBzH,IAAM,EAG9DsH,OACIE,EAAa,GAAKjB,EAAemB,EAAOF,EAAa,GAAIrC,OAG3DwC,GAAgBD,EAAOE,OAA4C,SAACC,EAAKC,EAAOC,OAChFD,GAAmB,MAAVA,EAAe,IACtBE,GAAcH,EAAIA,EAAIhI,OAAS,EACjCmI,IAAeA,EAAYD,MAAQC,EAAYnI,SAAWkI,IACjDlI,WAERsE,MAAO4D,MAAAA,EAAOlI,OAAS,UAGtBgI,QAGFI,EAAoBN,EAAcO,KAAK,SAACC,EAAGC,SAAMA,GAAEvI,OAASsI,EAAEtI,SAAQ,GAExEwI,MAAAA,MACAJ,GAAqBA,EAAkBpI,OAAS,EAAG,IAChDyI,GAAWZ,EAAO5H,MAAM,EAAGmI,EAAkBF,OAC7CQ,EAAUb,EAAO5H,MAAMmI,EAAkBF,MAAQE,EAAkBpI,UAC/DyI,EAASrI,KAAK,KAAO,KAAOsI,EAAQtI,KAAK,YAEzCyH,EAAOzH,KAAK,WAGnB8G,QACQ,IAAMA,GAGXsB,QAEAxC,GAOT,QAAA2C,GAAsBC,MAAkBC,GAAxC9I,UAAAC,OAAA,GAAAD,UAAA,KAAAU,UAAAV,UAAA,MACOsF,KACAC,GAA4B,IAAhBuD,EAAQC,IAAgBC,EAAeC,CAE/B,YAAtBH,EAAQI,YAAwBL,GAAaC,EAAQlD,OAASkD,EAAQlD,OAAS,IAAM,IAAM,KAAOiD,MAEhGjC,GAAUiC,EAAUnD,MAAMyD,MAE5BvC,EAAS,CACRwC,KAEQxD,OAASgB,EAAQ,KACjBb,SAAWa,EAAQ,KACnBX,KAAOW,EAAQ,KACfyC,KAAOtE,SAAS6B,EAAQ,GAAI,MAC5BT,KAAOS,EAAQ,IAAM,KACrBN,MAAQM,EAAQ,KAChBJ,SAAWI,EAAQ,GAG1B0C,MAAMhE,EAAW+D,UACTA,KAAOzC,EAAQ,QAIhBhB,OAASgB,EAAQ,IAAMlG,YACvBqF,UAAwC,IAA5B8C,EAAUU,QAAQ,KAAc3C,EAAQ,GAAKlG,YACzDuF,MAAqC,IAA7B4C,EAAUU,QAAQ,MAAe3C,EAAQ,GAAKlG,YACtD2I,KAAOtE,SAAS6B,EAAQ,GAAI,MAC5BT,KAAOS,EAAQ,IAAM,KACrBN,OAAqC,IAA5BuC,EAAUU,QAAQ,KAAc3C,EAAQ,GAAKlG,YACtD8F,UAAwC,IAA5BqC,EAAUU,QAAQ,KAAc3C,EAAQ,GAAKlG,UAGhE4I,MAAMhE,EAAW+D,UACTA,KAAQR,EAAUnD,MAAM,iCAAmCkB,EAAQ,GAAKlG,YAIjF4E,EAAWW,SAEHA,KAAOe,EAAeL,EAAerB,EAAWW,KAAMV,GAAWA,IAIzED,EAAWM,SAAWlF,WAAa4E,EAAWS,WAAarF,WAAa4E,EAAWW,OAASvF,WAAa4E,EAAW+D,OAAS3I,WAAc4E,EAAWa,MAAQb,EAAWgB,QAAU5F,UAE5K4E,EAAWM,SAAWlF,YACrBwI,UAAY,WACb5D,EAAWkB,WAAa9F,YACvBwI,UAAY,aAEZA,UAAY,QANZA,UAAY,gBAUpBJ,EAAQI,WAAmC,WAAtBJ,EAAQI,WAA0BJ,EAAQI,YAAc5D,EAAW4D,cAChF9F,MAAQkC,EAAWlC,OAAS,gBAAkB0F,EAAQI,UAAY,kBAIxEM,GAAgBC,GAASX,EAAQlD,QAAUN,EAAWM,QAAU,IAAI1E,kBAGrE4H,EAAQY,gBAAoBF,GAAkBA,EAAcE,iBAcpCpE,EAAYC,OAdyC,IAE7ED,EAAWW,OAAS6C,EAAQa,YAAeH,GAAiBA,EAAcG,kBAGjE1D,KAAO2D,EAASC,QAAQvE,EAAWW,KAAKlC,QAAQwB,EAASM,YAAalB,GAAazD,eAC7F,MAAO4I,KACG1G,MAAQkC,EAAWlC,OAAS,kEAAoE0G,IAIjFxE,EAAY2D,GAOrCO,GAAiBA,EAAcZ,SACpBA,MAAMtD,EAAYwD,UAGtB1F,MAAQkC,EAAWlC,OAAS,+BAGjCkC,GAGR,QAAAyE,GAA6BzE,EAA0BwD,MAChDvD,IAA4B,IAAhBuD,EAAQC,IAAgBC,EAAeC,EACnDe,WAEF1E,GAAWS,WAAarF,cACjB6D,KAAKe,EAAWS,YAChBxB,KAAK,MAGZe,EAAWW,OAASvF,aAEb6D,KAAKyC,EAAeL,EAAe1B,OAAOK,EAAWW,MAAOV,GAAWA,GAAUxB,QAAQwB,EAAS0B,YAAa,SAACgD,EAAGC,EAAIC,SAAO,IAAMD,GAAMC,EAAK,MAAQA,EAAK,IAAM,OAG9I,gBAApB7E,GAAW+D,MAAgD,gBAApB/D,GAAW+D,SAClD9E,KAAK,OACLA,KAAKU,OAAOK,EAAW+D,QAG3BW,EAAU/J,OAAS+J,EAAU3J,KAAK,IAAMK,UAShD,QAAA0J,GAAkCC,UAC3BnG,MAECmG,EAAMpK,WACRoK,EAAM3E,MAAM4E,KACPD,EAAMtG,QAAQuG,EAAM,QACtB,IAAID,EAAM3E,MAAM6E,MACdF,EAAMtG,QAAQwG,GAAM,SACtB,IAAIF,EAAM3E,MAAM8E,MACdH,EAAMtG,QAAQyG,GAAM,OACrBxJ,UACD,IAAc,MAAVqJ,GAA2B,OAAVA,IACnB,OACF,IACAI,GAAKJ,EAAM3E,MAAMgF,QACnBD,OAKG,IAAIE,OAAM,uCAJVC,GAAIH,EAAG,KACLJ,EAAMnK,MAAM0K,EAAE3K,UACfsE,KAAKqG,SAOR1G,GAAO7D,KAAK,IAGpB,QAAAwK,GAA0BvF,MAA0BwD,GAApD9I,UAAAC,OAAA,GAAAD,UAAA,KAAAU,UAAAV,UAAA,MACOuF,EAAYuD,EAAQC,IAAMC,EAAeC,EACzCe,KAGAR,EAAgBC,GAASX,EAAQlD,QAAUN,EAAWM,QAAU,IAAI1E,kBAGtEsI,GAAiBA,EAAcqB,WAAWrB,EAAcqB,UAAUvF,EAAYwD,GAE9ExD,EAAWW,QAEVV,EAAS0B,YAAYU,KAAKrC,EAAWW,WAKpC,IAAI6C,EAAQa,YAAeH,GAAiBA,EAAcG,iBAGlD1D,KAAS6C,EAAQC,IAAmGa,EAASkB,UAAUxF,EAAWW,MAA3H2D,EAASC,QAAQvE,EAAWW,KAAKlC,QAAQwB,EAASM,YAAalB,GAAazD,eAC7G,MAAO4I,KACG1G,MAAQkC,EAAWlC,OAAS,+CAAkD0F,EAAQC,IAAgB,UAAV,SAAuB,kBAAoBe,IAMzHxE,EAAYC,GAEd,WAAtBuD,EAAQI,WAA0B5D,EAAWM,WACtCrB,KAAKe,EAAWM,UAChBrB,KAAK,SAGVwG,GAAYhB,EAAoBzE,EAAYwD,MAC9CiC,IAAcrK,YACS,WAAtBoI,EAAQI,aACD3E,KAAK,QAGNA,KAAKwG,GAEXzF,EAAWa,MAAsC,MAA9Bb,EAAWa,KAAK6E,OAAO,MACnCzG,KAAK,MAIbe,EAAWa,OAASzF,UAAW,IAC9BkK,GAAItF,EAAWa,IAEd2C,GAAQmC,cAAkBzB,GAAkBA,EAAcyB,iBAC1Db,EAAkBQ,IAGnBG,IAAcrK,cACbkK,EAAE7G,QAAQ,QAAS,WAGdQ,KAAKqG,SAGZtF,GAAWgB,QAAU5F,cACd6D,KAAK,OACLA,KAAKe,EAAWgB,QAGvBhB,EAAWkB,WAAa9F,cACjB6D,KAAK,OACLA,KAAKe,EAAWkB,WAGpBwD,EAAU3J,KAAK,IAGvB,QAAA6K,GAAkCC,EAAoBC,MAAwBtC,GAA9E9I,UAAAC,OAAA,GAAAD,UAAA,KAAAU,UAAAV,UAAA,MAAuGqL,EAAvGrL,UAAA,GACOwB,WAED6J,OACGzC,EAAMiC,EAAUM,EAAMrC,GAAUA,KAC5BF,EAAMiC,EAAUO,EAAUtC,GAAUA,MAEtCA,OAELA,EAAQwC,UAAYF,EAASxF,UAC1BA,OAASwF,EAASxF,SAElBG,SAAWqF,EAASrF,WACpBE,KAAOmF,EAASnF,OAChBoD,KAAO+B,EAAS/B,OAChBlD,KAAOiE,EAAkBgB,EAASjF,MAAQ,MAC1CG,MAAQ8E,EAAS9E,QAEpB8E,EAASrF,WAAarF,WAAa0K,EAASnF,OAASvF,WAAa0K,EAAS/B,OAAS3I,aAEhFqF,SAAWqF,EAASrF,WACpBE,KAAOmF,EAASnF,OAChBoD,KAAO+B,EAAS/B,OAChBlD,KAAOiE,EAAkBgB,EAASjF,MAAQ,MAC1CG,MAAQ8E,EAAS9E,QAEnB8E,EAASjF,MAQmB,MAA5BiF,EAASjF,KAAK6E,OAAO,KACjB7E,KAAOiE,EAAkBgB,EAASjF,OAEpCgF,EAAKpF,WAAarF,WAAayK,EAAKlF,OAASvF,WAAayK,EAAK9B,OAAS3I,WAAeyK,EAAKhF,KAErFgF,EAAKhF,OAGTA,KAAOgF,EAAKhF,KAAKjG,MAAM,EAAGiL,EAAKhF,KAAKoF,YAAY,KAAO,GAAKH,EAASjF,OAFrEA,KAAOiF,EAASjF,OAFhBA,KAAO,IAAMiF,EAASjF,OAMvBA,KAAOiE,EAAkB5I,EAAO2E,SAEjCG,MAAQ8E,EAAS9E,UAnBjBH,KAAOgF,EAAKhF,KACfiF,EAAS9E,QAAU5F,YACf4F,MAAQ8E,EAAS9E,QAEjBA,MAAQ6E,EAAK7E,SAkBfP,SAAWoF,EAAKpF,WAChBE,KAAOkF,EAAKlF,OACZoD,KAAO8B,EAAK9B,QAEbzD,OAASuF,EAAKvF,UAGfY,SAAW4E,EAAS5E,SAEpBhF,EAGR,QAAAgK,GAAwBC,EAAgBC,EAAoB5C,MACrD6C,GAAoBpK,GAASqE,OAAS,QAAUkD,SAC/C+B,GAAUK,EAAkBtC,EAAM6C,EAASE,GAAoB/C,EAAM8C,EAAaC,GAAoBA,GAAmB,GAAOA,GAKxI,QAAAC,GAA0BC,EAAS/C,SACf,gBAAR+C,KACJhB,EAAUjC,EAAMiD,EAAK/C,GAAUA,GACX,WAAhBtI,EAAOqL,OACXjD,EAAMiC,EAAyBgB,EAAK/C,GAAUA,IAG9C+C,EAKR,QAAAC,GAAsBC,EAAUC,EAAUlD,SACrB,gBAATiD,KACHlB,EAAUjC,EAAMmD,EAAMjD,GAAUA,GACZ,WAAjBtI,EAAOuL,OACVlB,EAAyBkB,EAAMjD,IAGnB,gBAATkD,KACHnB,EAAUjC,EAAMoD,EAAMlD,GAAUA,GACZ,WAAjBtI,EAAOwL,OACVnB,EAAyBmB,EAAMlD,IAGhCiD,IAASC,EAGjB,QAAAC,GAAgC1L,EAAYuI,SACpCvI,IAAOA,EAAIM,WAAWkD,QAAU+E,GAAYA,EAAQC,IAA4BC,EAAakD,OAAnCjD,EAAaiD,OAA+B1H,GAG9G,QAAA2H,GAAkC5L,EAAYuI,SACtCvI,IAAOA,EAAIM,WAAWkD,QAAU+E,GAAYA,EAAQC,IAAiCC,EAAanD,YAAxCoD,EAAapD,YAAyClB,GCniBxH,QAAAyH,GAAkBC,SACqB,iBAAxBA,GAAaC,OAAuBD,EAAaC,OAAuD,QAA9CrH,OAAOoH,EAAazG,QAAQ1E,cCwDrG,QAGAsE,GAA0BjF,MACnBkF,GAASd,EAAYpE,SAClBkF,GAAOC,MAAMC,IAAoBF,EAANlF,EJmBrC,GAAA0I,GAAetH,GAAU,GKrFzBqH,EAAerH,GAAU,2iBJAnB4K,EAAS,WAaTC,EAAgB,QAChBC,EAAgB,aAChBzI,EAAkB,4BAGlBT,YACO,8DACC,iEACI,iBAKZmJ,EAAQC,KAAKD,MACbE,EAAqB3H,OAAOC,aAsG5B2H,EAAa,SAAApJ,SAASwB,QAAO6H,cAAPC,MAAA9H,OAAA+H,EAAwBvJ,KAW9CwJ,EAAe,SAASC,SACzBA,GAAY,GAAO,GACfA,EAAY,GAEhBA,EAAY,GAAO,GACfA,EAAY,GAEhBA,EAAY,GAAO,GACfA,EAAY,GAjJR,IAiKPC,EAAe,SAASC,EAAOC,SAG7BD,GAAQ,GAAK,IAAMA,EAAQ,MAAgB,GAARC,IAAc,IAQnDC,EAAQ,SAASC,EAAOC,EAAWC,MACpCC,GAAI,QACAD,EAAYf,EAAMa,EA1Kd,KA0K8BA,GAAS,KAC1Cb,EAAMa,EAAQC,GACOD,EAAQI,IAA2BD,GAhLrD,KAiLHhB,EAAMa,EA3JMpC,UA6JduB,GAAMgB,EAAI,GAAsBH,GAASA,EAhLpC,MA0LPK,EAAS,SAASvD,MAEjBnG,MACA2J,EAAcxD,EAAMpK,OACtB4E,EAAI,EACJiJ,EA5LY,IA6LZC,EA9Le,GAoMfC,EAAQ3D,EAAMkB,YAlMD,IAmMbyC,GAAQ,MACH,OAGJ,GAAIC,GAAI,EAAGA,EAAID,IAASC,EAExB5D,EAAMhG,WAAW4J,IAAM,OACpB,eAEA1J,KAAK8F,EAAMhG,WAAW4J,QAMzB,GAAI9F,GAAQ6F,EAAQ,EAAIA,EAAQ,EAAI,EAAG7F,EAAQ0F,GAAwC,KAQtF,GADDK,GAAOrJ,EACFsJ,EAAI,EAAGT,EAjOL,IAiOmCA,GAjOnC,GAiO8C,CAEpDvF,GAAS0F,KACN,oBAGDT,GAAQH,EAAa5C,EAAMhG,WAAW8D,OAExCiF,GAzOM,IAyOWA,EAAQV,GAAOH,EAAS1H,GAAKsJ,OAC3C,eAGFf,EAAQe,KACPC,GAAIV,GAAKK,EA7OL,EA6OoBL,GAAKK,EA5OzB,GAAA,GA4O8CL,EAAIK,KAExDX,EAAQgB,WAINC,GApPI,GAoPgBD,CACtBD,GAAIzB,EAAMH,EAAS8B,MAChB,eAGFA,KAIAC,GAAMpK,EAAOjE,OAAS,IACrBqN,EAAMzI,EAAIqJ,EAAMI,EAAa,GAARJ,GAIxBxB,EAAM7H,EAAIyJ,GAAO/B,EAASuB,KACvB,eAGFpB,EAAM7H,EAAIyJ,MACVA,IAGEC,OAAO1J,IAAK,EAAGiJ,SAIhB7I,QAAO6H,cAAPC,MAAA9H,OAAwBf,IAU1BsK,EAAS,SAASnE,MACjBnG,QAGED,EAAWoG,MAGfwD,GAAcxD,EAAMpK,OAGpB6N,EA5RY,IA6RZP,EAAQ,EACRQ,EA/Re,oCAkSnBU,KAA2BpE,EAA3BqE,OAAAC,cAAAC,GAAAH,EAAAI,EAAAC,QAAAC,MAAAH,GAAA,EAAkC,IAAvBI,GAAuBP,EAAArK,KAC7B4K,GAAe,OACXzK,KAAKqI,EAAmBoC,2FAI7BC,GAAc/K,EAAOjE,OACrBiP,EAAiBD,MAMjBA,KACI1K,KA9SS,KAkTV2K,EAAiBrB,GAAa,IAIhCsB,GAAI5C,mCACR6C,KAA2B/E,EAA3BqE,OAAAC,cAAAU,GAAAD,EAAAE,EAAAR,QAAAC,MAAAM,GAAA,EAAkC,IAAvBL,GAAuBI,EAAAhL,KAC7B4K,IAAgBlB,GAAKkB,EAAeG,MACnCH,0FAMAO,GAAwBL,EAAiB,CAC3CC,GAAIrB,EAAIpB,GAAOH,EAASgB,GAASgC,MAC9B,gBAGGJ,EAAIrB,GAAKyB,IACfJ,uCAEJK,KAA2BnF,EAA3BqE,OAAAC,cAAAc,GAAAD,EAAAE,EAAAZ,QAAAC,MAAAU,GAAA,EAAkC,IAAvBT,GAAuBQ,EAAApL,SAC7B4K,EAAelB,KAAOP,EAAQhB,KAC3B,YAEHyC,GAAgBlB,EAAG,KAGjB,GADD6B,GAAIpC,EACCG,EArVA,IAqV8BA,GArV9B,GAqVyC,IAC3CU,GAAIV,GAAKK,EArVP,EAqVsBL,GAAKK,EApV3B,GAAA,GAoVgDL,EAAIK,KACxD4B,EAAIvB,WAGFwB,GAAUD,EAAIvB,EACdC,EA3VE,GA2VkBD,IACnB7J,KACNqI,EAAmBO,EAAaiB,EAAIwB,EAAUvB,EAAY,OAEvD3B,EAAMkD,EAAUvB,KAGd9J,KAAKqI,EAAmBO,EAAawC,EAAG,OACxCrC,EAAMC,EAAOgC,EAAuBL,GAAkBD,KACrD,IACNC,yFAIF3B,IACAO,QAGI5J,GAAO7D,KAAK,KAcdyK,EAAY,SAAST,SACnBzG,GAAUyG,EAAO,SAASxG,SACzB2I,GAAc7E,KAAK9D,GACvB+J,EAAO/J,EAAO3D,MAAM,GAAGgB,eACvB2C,KAeCgG,EAAU,SAASQ,SACjBzG,GAAUyG,EAAO,SAASxG,SACzB4I,GAAc9E,KAAK9D,GACvB,OAAS2K,EAAO3K,GAChBA,KAOC+F,WAMM,qBASA3F,SACA4I,UAEDe,SACAY,UACC3E,YACEiB,GC5VDrB,KA2IPN,EAAY,kIACZC,EAA4C,GAAI1D,MAAM,SAAU,KAAOhF,UAoHvE4J,EAAO,WACPC,GAAO,cACPC,GAAO,gBAEPE,GAAO,yBI1VPmF,WACI,mBAEI,QAEL,SAAUvK,EAA0BwD,SAEtCxD,GAAWW,SACJ7C,MAAQkC,EAAWlC,OAAS,+BAGjCkC,aAGI,SAAUA,EAA0BwD,MACzCwD,GAAqD,UAA5CrH,OAAOK,EAAWM,QAAQ1E,oBAGrCoE,GAAW+D,QAAUiD,EAAS,IAAM,KAA2B,KAApBhH,EAAW+D,SAC9CA,KAAO3I,WAId4E,EAAWa,SACJA,KAAO,KAOZb,IC9BHuK,WACI,mBACIC,GAAKnG,iBACVmG,GAAKlH,gBACDkH,GAAKjF,WJKZgF,WACI,iBAEI,QAEL,SAAUvK,EAA0BwD,MACrCuD,GAAe/G,WAGRgH,OAASF,EAASC,KAGlB0D,cAAgB1D,EAAalG,MAAQ,MAAQkG,EAAa/F,MAAQ,IAAM+F,EAAa/F,MAAQ,MAC7FH,KAAOzF,YACP4F,MAAQ5F,UAEd2L,aAGI,SAAUA,EAA2BvD,MAE5CuD,EAAahD,QAAU+C,EAASC,GAAgB,IAAM,KAA6B,KAAtBA,EAAahD,SAChEA,KAAO3I,WAIc,iBAAxB2L,GAAaC,WACV1G,OAAUyG,EAAaC,OAAS,MAAQ,OACxCA,OAAS5L,WAInB2L,EAAa0D,aAAc,OACR1D,EAAa0D,aAAahP,MAAM,cAA/CoF,EADuB6J,EAAA,GACjB1J,EADiB0J,EAAA,KAEjB7J,KAAQA,GAAiB,MAATA,EAAeA,EAAOzF,YACtC4F,MAAQA,IACRyJ,aAAerP,mBAIhB8F,SAAW9F,UAEjB2L,IKnDHwD,WACI,iBACII,GAAGtG,iBACRsG,GAAGrH,gBACCqH,GAAGpF,WJSVqF,MAIAlN,GAAe,mGACfnB,GAAW,cACXC,GAAexB,EAAOA,EAAO,sBAA6BuB,GAAWA,GAAW,IAAMA,GAAWA,IAAY,IAAMvB,EAAO,0BAAiCuB,GAAWA,IAAY,IAAMvB,EAAO,IAAMuB,GAAWA,KAehNsO,GAAUxQ,EADA,6DACe,aAqBzBgG,GAAa,GAAI1C,QAAOD,GAAc,KACtC6C,GAAc,GAAI5C,QAAOnB,GAAc,KACvCsO,GAAiB,GAAInN,QAAOtD,EAAM,MAzBxB,wDAyBwC,QAAS,QAASwQ,IAAU,KAE9EE,GAAa,GAAIpN,QAAOtD,EAAM,MAAOqD,GAjBrB,uCAiBmD,KACnEsN,GAAcD,GASdR,WACI,eAED,SAAUvK,EAA0BwD,MACrCyH,GAAmBjL,EACnBkL,EAAKD,EAAiBC,GAAMD,EAAiBpK,KAAOoK,EAAiBpK,KAAKpF,MAAM,aACrEoF,KAAOzF,UAEpB6P,EAAiBjK,MAAO,KAKtB,GAJDmK,IAAiB,EACfC,KACAC,EAAUJ,EAAiBjK,MAAMvF,MAAM,KAEpCX,EAAI,EAAGD,EAAKwQ,EAAQ1Q,OAAQG,EAAID,IAAMC,EAAG,IAC3CwQ,GAASD,EAAQvQ,GAAGW,MAAM,YAExB6P,EAAO,QACT,SAEC,GADCC,GAAUD,EAAO,GAAG7P,MAAM,KACvBX,EAAI,EAAGD,EAAK0Q,EAAQ5Q,OAAQG,EAAID,IAAMC,IAC3CmE,KAAKsM,EAAQzQ,cAGb,YACa0Q,QAAU3E,EAAkByE,EAAO,GAAI9H,aAEpD,SACaiI,KAAO5E,EAAkByE,EAAO,GAAI9H,oBAGpC,IACTqD,EAAkByE,EAAO,GAAI9H,IAAYqD,EAAkByE,EAAO,GAAI9H,IAK7E2H,IAAgBF,EAAiBG,QAAUA,KAG/BpK,MAAQ5F,cAEpB,GAAIN,GAAI,EAAGD,EAAKqQ,EAAGvQ,OAAQG,EAAID,IAAMC,EAAG,IACtC4Q,GAAOR,EAAGpQ,GAAGW,MAAM,UAEpB,GAAKoL,EAAkB6E,EAAK,IAE5BlI,EAAQY,iBAQP,GAAKyC,EAAkB6E,EAAK,GAAIlI,GAAS5H,yBALxC,GAAK0I,EAASC,QAAQsC,EAAkB6E,EAAK,GAAIlI,GAAS5H,eAC9D,MAAO4I,KACS1G,MAAQmN,EAAiBnN,OAAS,2EAA6E0G,IAM/H1J,GAAK4Q,EAAK3Q,KAAK,WAGZkQ,cAGI,SAAUA,EAAmCzH,MAClDxD,GAAaiL,EACbC,EAAKpP,EAAQmP,EAAiBC,OAChCA,EAAI,KACF,GAAIpQ,GAAI,EAAGD,EAAKqQ,EAAGvQ,OAAQG,EAAID,IAAMC,EAAG,IACtC6Q,GAAShM,OAAOuL,EAAGpQ,IACnB8Q,EAAQD,EAAO1F,YAAY,KAC3B4F,EAAaF,EAAO/Q,MAAM,EAAGgR,GAAQnN,QAAQ8B,GAAaL,GAAkBzB,QAAQ8B,GAAa1E,GAAa4C,QAAQqM,GAAgB5L,GACxI4M,EAASH,EAAO/Q,MAAMgR,EAAQ,SAItBpI,EAAQC,IAA2Ea,EAASkB,UAAUsG,GAAxFxH,EAASC,QAAQsC,EAAkBiF,EAAQtI,GAAS5H,eAC5E,MAAO4I,KACG1G,MAAQkC,EAAWlC,OAAS,wDAA2D0F,EAAQC,IAAgB,UAAV,SAAuB,kBAAoBe,IAGzJ1J,GAAK+Q,EAAY,IAAMC,IAGhBjL,KAAOqK,EAAGnQ,KAAK,QAGrBqQ,GAAUH,EAAiBG,QAAUH,EAAiBG,WAExDH,GAAiBO,UAASJ,EAAA,QAAqBH,EAAiBO,SAChEP,EAAiBQ,OAAML,EAAA,KAAkBH,EAAiBQ,SAExDjJ,UACD,GAAMuJ,KAAQX,GACdA,EAAQW,KAAUnB,GAAEmB,MAChB9M,KACN8M,EAAKtN,QAAQ8B,GAAaL,GAAkBzB,QAAQ8B,GAAa1E,GAAa4C,QAAQsM,GAAY7L,GAClG,IACAkM,EAAQW,GAAMtN,QAAQ8B,GAAaL,GAAkBzB,QAAQ8B,GAAa1E,GAAa4C,QAAQuM,GAAa9L,UAI3GsD,GAAO7H,WACCqG,MAAQwB,EAAOzH,KAAK,MAGzBiF,IK/JHgM,GAAY,kBAIZzB,WACI,YAED,SAAUvK,EAA0BwD,MACrClC,GAAUtB,EAAWa,MAAQb,EAAWa,KAAKT,MAAM4L,IACrDC,EAAgBjM,KAEhBsB,EAAS,IACNhB,GAASkD,EAAQlD,QAAU2L,EAAc3L,QAAU,MACnD4L,EAAM5K,EAAQ,GAAG1F,cACjBuQ,EAAM7K,EAAQ,GACd8K,EAAe9L,EAAf,KAAyBkD,EAAQ0I,KAAOA,GACxChI,EAAgBC,EAAQiI,KAEhBF,IAAMA,IACNC,IAAMA,IACNtL,KAAOzF,UAEjB8I,MACaA,EAAcZ,MAAM2I,EAAezI,WAGtC1F,MAAQmO,EAAcnO,OAAS,+BAGvCmO,cAGI,SAAUA,EAA6BzI,MAC5ClD,GAASkD,EAAQlD,QAAU2L,EAAc3L,QAAU,MACnD4L,EAAMD,EAAcC,IACpBE,EAAe9L,EAAf,KAAyBkD,EAAQ0I,KAAOA,GACxChI,EAAgBC,EAAQiI,EAE1BlI,OACaA,EAAcqB,UAAU0G,EAAezI,OAGlD6I,GAAgBJ,EAChBE,EAAMF,EAAcE,aACZtL,MAAUqL,GAAO1I,EAAQ0I,KAAvC,IAA8CC,EAEvCE,ICxDHC,GAAO,2DAIP/B,WACI,iBAED,SAAU0B,EAA6BzI,MACxC+I,GAAiBN,WACRO,KAAOD,EAAeJ,MACtBA,IAAM/Q,UAEhBoI,EAAQwC,UAAcuG,EAAeC,MAASD,EAAeC,KAAKpM,MAAMkM,QAC7DxO,MAAQyO,EAAezO,OAAS,sBAGzCyO,aAGI,SAAUA,EAA+B/I,MAC9CyI,GAAgBM,WAERJ,KAAOI,EAAeC,MAAQ,IAAI5Q,cACzCqQ,GC5BT9H,GAAQqG,GAAKlK,QAAUkK,GAEvBrG,EACQsI,GAAMnM,QAAUmM,GAExBtI,EACQwG,GAAGrK,QAAUqK,GAErBxG,EACQuI,GAAIpM,QAAUoM,GAEtBvI,EACQwI,GAAOrM,QAAUqM,GAEzBxI,EACQyI,GAAItM,QAAUsM,GAEtBzI,EACQqI,GAAKlM,QAAUkM", "file": "dist/es5/uri.all.min.js", "sourcesContent": ["export function merge(...sets:Array<string>):string {\n\tif (sets.length > 1) {\n\t\tsets[0] = sets[0].slice(0, -1);\n\t\tconst xl = sets.length - 1;\n\t\tfor (let x = 1; x < xl; ++x) {\n\t\t\tsets[x] = sets[x].slice(1, -1);\n\t\t}\n\t\tsets[xl] = sets[xl].slice(1);\n\t\treturn sets.join('');\n\t} else {\n\t\treturn sets[0];\n\t}\n}\n\nexport function subexp(str:string):string {\n\treturn \"(?:\" + str + \")\";\n}\n\nexport function typeOf(o:any):string {\n\treturn o === undefined ? \"undefined\" : (o === null ? \"null\" : Object.prototype.toString.call(o).split(\" \").pop().split(\"]\").shift().toLowerCase());\n}\n\nexport function toUpperCase(str:string):string {\n\treturn str.toUpperCase();\n}\n\nexport function toArray(obj:any):Array<any> {\n\treturn obj !== undefined && obj !== null ? (obj instanceof Array ? obj : (typeof obj.length !== \"number\" || obj.split || obj.setInterval || obj.call ? [obj] : Array.prototype.slice.call(obj))) : [];\n}\n\n\nexport function assign(target: object, source: any): any {\n\tconst obj = target as any;\n\tif (source) {\n\t\tfor (const key in source) {\n\t\t\tobj[key] = source[key];\n\t\t}\n\t}\n\treturn obj;\n}", "import { URIRegExps } from \"./uri\";\nimport { merge, subexp } from \"./util\";\n\nexport function buildExps(isIRI:boolean):URIRegExps {\n\tconst\n\t\tALPHA$$ = \"[A-Za-z]\",\n\t\tCR$ = \"[\\\\x0D]\",\n\t\tDIGIT$$ = \"[0-9]\",\n\t\tDQUOTE$$ = \"[\\\\x22]\",\n\t\tHEXDIG$$ = merge(DIGIT$$, \"[A-Fa-f]\"),  //case-insensitive\n\t\tLF$$ = \"[\\\\x0A]\",\n\t\tSP$$ = \"[\\\\x20]\",\n\t\tPCT_ENCODED$ = subexp(subexp(\"%[EFef]\" + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$) + \"|\" + subexp(\"%[89A-Fa-f]\" + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$) + \"|\" + subexp(\"%\" + HEXDIG$$ + HEXDIG$$)),  //expanded\n\t\tGEN_DELIMS$$ = \"[\\\\:\\\\/\\\\?\\\\#\\\\[\\\\]\\\\@]\",\n\t\tSUB_DELIMS$$ = \"[\\\\!\\\\$\\\\&\\\\'\\\\(\\\\)\\\\*\\\\+\\\\,\\\\;\\\\=]\",\n\t\tRESERVED$$ = merge(GEN_DELIMS$$, SUB_DELIMS$$),\n\t\tUCSCHAR$$ = isIRI ? \"[\\\\xA0-\\\\u200D\\\\u2010-\\\\u2029\\\\u202F-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFEF]\" : \"[]\",  //subset, excludes bidi control characters\n\t\tIPRIVATE$$ = isIRI ? \"[\\\\uE000-\\\\uF8FF]\" : \"[]\",  //subset\n\t\tUNRESERVED$$ = merge(ALPHA$$, DIGIT$$, \"[\\\\-\\\\.\\\\_\\\\~]\", UCSCHAR$$),\n\t\tSCHEME$ = subexp(ALPHA$$ + merge(ALPHA$$, DIGIT$$, \"[\\\\+\\\\-\\\\.]\") + \"*\"),\n\t\tUSERINFO$ = subexp(subexp(PCT_ENCODED$ + \"|\" + merge(UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:]\")) + \"*\"),\n\t\tDEC_OCTET$ = subexp(subexp(\"25[0-5]\") + \"|\" + subexp(\"2[0-4]\" + DIGIT$$) + \"|\" + subexp(\"1\" + DIGIT$$ + DIGIT$$) + \"|\" + subexp(\"[1-9]\" + DIGIT$$) + \"|\" + DIGIT$$),\n\t\tDEC_OCTET_RELAXED$ = subexp(subexp(\"25[0-5]\") + \"|\" + subexp(\"2[0-4]\" + DIGIT$$) + \"|\" + subexp(\"1\" + DIGIT$$ + DIGIT$$) + \"|\" + subexp(\"0?[1-9]\" + DIGIT$$) + \"|0?0?\" + DIGIT$$),  //relaxed parsing rules\n\t\tIPV4ADDRESS$ = subexp(DEC_OCTET_RELAXED$ + \"\\\\.\" + DEC_OCTET_RELAXED$ + \"\\\\.\" + DEC_OCTET_RELAXED$ + \"\\\\.\" + DEC_OCTET_RELAXED$),\n\t\tH16$ = subexp(HEXDIG$$ + \"{1,4}\"),\n\t\tLS32$ = subexp(subexp(H16$ + \"\\\\:\" + H16$) + \"|\" + IPV4ADDRESS$),\n\t\tIPV6ADDRESS1$ = subexp(                                                            subexp(H16$ + \"\\\\:\") + \"{6}\" + LS32$), //                           6( h16 \":\" ) ls32\n\t\tIPV6ADDRESS2$ = subexp(                                                 \"\\\\:\\\\:\" + subexp(H16$ + \"\\\\:\") + \"{5}\" + LS32$), //                      \"::\" 5( h16 \":\" ) ls32\n\t\tIPV6ADDRESS3$ = subexp(subexp(                                 H16$) + \"?\\\\:\\\\:\" + subexp(H16$ + \"\\\\:\") + \"{4}\" + LS32$), //[               h16 ] \"::\" 4( h16 \":\" ) ls32\n\t\tIPV6ADDRESS4$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,1}\" + H16$) + \"?\\\\:\\\\:\" + subexp(H16$ + \"\\\\:\") + \"{3}\" + LS32$), //[ *1( h16 \":\" ) h16 ] \"::\" 3( h16 \":\" ) ls32\n\t\tIPV6ADDRESS5$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,2}\" + H16$) + \"?\\\\:\\\\:\" + subexp(H16$ + \"\\\\:\") + \"{2}\" + LS32$), //[ *2( h16 \":\" ) h16 ] \"::\" 2( h16 \":\" ) ls32\n\t\tIPV6ADDRESS6$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,3}\" + H16$) + \"?\\\\:\\\\:\" +        H16$ + \"\\\\:\"          + LS32$), //[ *3( h16 \":\" ) h16 ] \"::\"    h16 \":\"   ls32\n\t\tIPV6ADDRESS7$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,4}\" + H16$) + \"?\\\\:\\\\:\"                                + LS32$), //[ *4( h16 \":\" ) h16 ] \"::\"              ls32\n\t\tIPV6ADDRESS8$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,5}\" + H16$) + \"?\\\\:\\\\:\"                                + H16$ ), //[ *5( h16 \":\" ) h16 ] \"::\"              h16\n\t\tIPV6ADDRESS9$ = subexp(subexp(subexp(H16$ + \"\\\\:\") + \"{0,6}\" + H16$) + \"?\\\\:\\\\:\"                                       ), //[ *6( h16 \":\" ) h16 ] \"::\"\n\t\tIPV6ADDRESS$ = subexp([IPV6ADDRESS1$, IPV6ADDRESS2$, IPV6ADDRESS3$, IPV6ADDRESS4$, IPV6ADDRESS5$, IPV6ADDRESS6$, IPV6ADDRESS7$, IPV6ADDRESS8$, IPV6ADDRESS9$].join(\"|\")),\n\t\tZONEID$ = subexp(subexp(UNRESERVED$$ + \"|\" + PCT_ENCODED$) + \"+\"),  //RFC 6874\n\t\tIPV6ADDRZ$ = subexp(IPV6ADDRESS$ + \"\\\\%25\" + ZONEID$),  //RFC 6874\n\t\tIPV6ADDRZ_RELAXED$ = subexp(IPV6ADDRESS$ + subexp(\"\\\\%25|\\\\%(?!\" + HEXDIG$$ + \"{2})\") + ZONEID$),  //RFC 6874, with relaxed parsing rules\n\t\tIPVFUTURE$ = subexp(\"[vV]\" + HEXDIG$$ + \"+\\\\.\" + merge(UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:]\") + \"+\"),\n\t\tIP_LITERAL$ = subexp(\"\\\\[\" + subexp(IPV6ADDRZ_RELAXED$ + \"|\" + IPV6ADDRESS$ + \"|\" + IPVFUTURE$) + \"\\\\]\"),  //RFC 6874\n\t\tREG_NAME$ = subexp(subexp(PCT_ENCODED$ + \"|\" + merge(UNRESERVED$$, SUB_DELIMS$$)) + \"*\"),\n\t\tHOST$ = subexp(IP_LITERAL$ + \"|\" + IPV4ADDRESS$ + \"(?!\" + REG_NAME$ + \")\" + \"|\" + REG_NAME$),\n\t\tPORT$ = subexp(DIGIT$$ + \"*\"),\n\t\tAUTHORITY$ = subexp(subexp(USERINFO$ + \"@\") + \"?\" + HOST$ + subexp(\"\\\\:\" + PORT$) + \"?\"),\n\t\tPCHAR$ = subexp(PCT_ENCODED$ + \"|\" + merge(UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:\\\\@]\")),\n\t\tSEGMENT$ = subexp(PCHAR$ + \"*\"),\n\t\tSEGMENT_NZ$ = subexp(PCHAR$ + \"+\"),\n\t\tSEGMENT_NZ_NC$ = subexp(subexp(PCT_ENCODED$ + \"|\" + merge(UNRESERVED$$, SUB_DELIMS$$, \"[\\\\@]\")) + \"+\"),\n\t\tPATH_ABEMPTY$ = subexp(subexp(\"\\\\/\" + SEGMENT$) + \"*\"),\n\t\tPATH_ABSOLUTE$ = subexp(\"\\\\/\" + subexp(SEGMENT_NZ$ + PATH_ABEMPTY$) + \"?\"),  //simplified\n\t\tPATH_NOSCHEME$ = subexp(SEGMENT_NZ_NC$ + PATH_ABEMPTY$),  //simplified\n\t\tPATH_ROOTLESS$ = subexp(SEGMENT_NZ$ + PATH_ABEMPTY$),  //simplified\n\t\tPATH_EMPTY$ = \"(?!\" + PCHAR$ + \")\",\n\t\tPATH$ = subexp(PATH_ABEMPTY$ + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_NOSCHEME$ + \"|\" + PATH_ROOTLESS$ + \"|\" + PATH_EMPTY$),\n\t\tQUERY$ = subexp(subexp(PCHAR$ + \"|\" + merge(\"[\\\\/\\\\?]\", IPRIVATE$$)) + \"*\"),\n\t\tFRAGMENT$ = subexp(subexp(PCHAR$ + \"|[\\\\/\\\\?]\") + \"*\"),\n\t\tHIER_PART$ = subexp(subexp(\"\\\\/\\\\/\" + AUTHORITY$ + PATH_ABEMPTY$) + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_ROOTLESS$ + \"|\" + PATH_EMPTY$),\n\t\tURI$ = subexp(SCHEME$ + \"\\\\:\" + HIER_PART$ + subexp(\"\\\\?\" + QUERY$) + \"?\" + subexp(\"\\\\#\" + FRAGMENT$) + \"?\"),\n\t\tRELATIVE_PART$ = subexp(subexp(\"\\\\/\\\\/\" + AUTHORITY$ + PATH_ABEMPTY$) + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_NOSCHEME$ + \"|\" + PATH_EMPTY$),\n\t\tRELATIVE$ = subexp(RELATIVE_PART$ + subexp(\"\\\\?\" + QUERY$) + \"?\" + subexp(\"\\\\#\" + FRAGMENT$) + \"?\"),\n\t\tURI_REFERENCE$ = subexp(URI$ + \"|\" + RELATIVE$),\n\t\tABSOLUTE_URI$ = subexp(SCHEME$ + \"\\\\:\" + HIER_PART$ + subexp(\"\\\\?\" + QUERY$) + \"?\"),\n\n\t\tGENERIC_REF$ = \"^(\" + SCHEME$ + \")\\\\:\" + subexp(subexp(\"\\\\/\\\\/(\" + subexp(\"(\" + USERINFO$ + \")@\") + \"?(\" + HOST$ + \")\" + subexp(\"\\\\:(\" + PORT$ + \")\") + \"?)\") + \"?(\" + PATH_ABEMPTY$ + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_ROOTLESS$ + \"|\" + PATH_EMPTY$ + \")\") + subexp(\"\\\\?(\" + QUERY$ + \")\") + \"?\" + subexp(\"\\\\#(\" + FRAGMENT$ + \")\") + \"?$\",\n\t\tRELATIVE_REF$ = \"^(){0}\" + subexp(subexp(\"\\\\/\\\\/(\" + subexp(\"(\" + USERINFO$ + \")@\") + \"?(\" + HOST$ + \")\" + subexp(\"\\\\:(\" + PORT$ + \")\") + \"?)\") + \"?(\" + PATH_ABEMPTY$ + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_NOSCHEME$ + \"|\" + PATH_EMPTY$ + \")\") + subexp(\"\\\\?(\" + QUERY$ + \")\") + \"?\" + subexp(\"\\\\#(\" + FRAGMENT$ + \")\") + \"?$\",\n\t\tABSOLUTE_REF$ = \"^(\" + SCHEME$ + \")\\\\:\" + subexp(subexp(\"\\\\/\\\\/(\" + subexp(\"(\" + USERINFO$ + \")@\") + \"?(\" + HOST$ + \")\" + subexp(\"\\\\:(\" + PORT$ + \")\") + \"?)\") + \"?(\" + PATH_ABEMPTY$ + \"|\" + PATH_ABSOLUTE$ + \"|\" + PATH_ROOTLESS$ + \"|\" + PATH_EMPTY$ + \")\") + subexp(\"\\\\?(\" + QUERY$ + \")\") + \"?$\",\n\t\tSAMEDOC_REF$ = \"^\" + subexp(\"\\\\#(\" + FRAGMENT$ + \")\") + \"?$\",\n\t\tAUTHORITY_REF$ = \"^\" + subexp(\"(\" + USERINFO$ + \")@\") + \"?(\" + HOST$ + \")\" + subexp(\"\\\\:(\" + PORT$ + \")\") + \"?$\"\n\t;\n\n\treturn {\n\t\tNOT_SCHEME : new RegExp(merge(\"[^]\", ALPHA$$, DIGIT$$, \"[\\\\+\\\\-\\\\.]\"), \"g\"),\n\t\tNOT_USERINFO : new RegExp(merge(\"[^\\\\%\\\\:]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n\t\tNOT_HOST : new RegExp(merge(\"[^\\\\%\\\\[\\\\]\\\\:]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n\t\tNOT_PATH : new RegExp(merge(\"[^\\\\%\\\\/\\\\:\\\\@]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n\t\tNOT_PATH_NOSCHEME : new RegExp(merge(\"[^\\\\%\\\\/\\\\@]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n\t\tNOT_QUERY : new RegExp(merge(\"[^\\\\%]\", UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:\\\\@\\\\/\\\\?]\", IPRIVATE$$), \"g\"),\n\t\tNOT_FRAGMENT : new RegExp(merge(\"[^\\\\%]\", UNRESERVED$$, SUB_DELIMS$$, \"[\\\\:\\\\@\\\\/\\\\?]\"), \"g\"),\n\t\tESCAPE : new RegExp(merge(\"[^]\", UNRESERVED$$, SUB_DELIMS$$), \"g\"),\n\t\tUNRESERVED : new RegExp(UNRESERVED$$, \"g\"),\n\t\tOTHER_CHARS : new RegExp(merge(\"[^\\\\%]\", UNRESERVED$$, RESERVED$$), \"g\"),\n\t\tPCT_ENCODED : new RegExp(PCT_ENCODED$, \"g\"),\n\t\tIPV4ADDRESS : new RegExp(\"^(\" + IPV4ADDRESS$ + \")$\"),\n\t\tIPV6ADDRESS : new RegExp(\"^\\\\[?(\" + IPV6ADDRESS$ + \")\" + subexp(subexp(\"\\\\%25|\\\\%(?!\" + HEXDIG$$ + \"{2})\") + \"(\" + ZONEID$ + \")\") + \"?\\\\]?$\")  //RFC 6874, with relaxed parsing rules\n\t};\n}\n\nexport default buildExps(false);\n", "'use strict';\n\n/** Highest positive signed 32-bit float value */\nconst maxInt = 2147483647; // aka. 0x7FFFFFFF or 2^31-1\n\n/** Bootstring parameters */\nconst base = 36;\nconst tMin = 1;\nconst tMax = 26;\nconst skew = 38;\nconst damp = 700;\nconst initialBias = 72;\nconst initialN = 128; // 0x80\nconst delimiter = '-'; // '\\x2D'\n\n/** Regular expressions */\nconst regexPunycode = /^xn--/;\nconst regexNonASCII = /[^\\0-\\x7E]/; // non-ASCII chars\nconst regexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g; // RFC 3490 separators\n\n/** Error messages */\nconst errors = {\n\t'overflow': 'Overflow: input needs wider integers to process',\n\t'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n\t'invalid-input': 'Invalid input'\n};\n\n/** Convenience shortcuts */\nconst baseMinusTMin = base - tMin;\nconst floor = Math.floor;\nconst stringFromCharCode = String.fromCharCode;\n\n/*--------------------------------------------------------------------------*/\n\n/**\n * A generic error utility function.\n * @private\n * @param {String} type The error type.\n * @returns {Error} Throws a `RangeError` with the applicable error message.\n */\nfunction error(type) {\n\tthrow new RangeError(errors[type]);\n}\n\n/**\n * A generic `Array#map` utility function.\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} callback The function that gets called for every array\n * item.\n * @returns {Array} A new array of values returned by the callback function.\n */\nfunction map(array, fn) {\n\tconst result = [];\n\tlet length = array.length;\n\twhile (length--) {\n\t\tresult[length] = fn(array[length]);\n\t}\n\treturn result;\n}\n\n/**\n * A simple `Array#map`-like wrapper to work with domain name strings or email\n * addresses.\n * @private\n * @param {String} domain The domain name or email address.\n * @param {Function} callback The function that gets called for every\n * character.\n * @returns {Array} A new string of characters returned by the callback\n * function.\n */\nfunction mapDomain(string, fn) {\n\tconst parts = string.split('@');\n\tlet result = '';\n\tif (parts.length > 1) {\n\t\t// In email addresses, only the domain name should be punycoded. Leave\n\t\t// the local part (i.e. everything up to `@`) intact.\n\t\tresult = parts[0] + '@';\n\t\tstring = parts[1];\n\t}\n\t// Avoid `split(regex)` for IE8 compatibility. See #17.\n\tstring = string.replace(regexSeparators, '\\x2E');\n\tconst labels = string.split('.');\n\tconst encoded = map(labels, fn).join('.');\n\treturn result + encoded;\n}\n\n/**\n * Creates an array containing the numeric code points of each Unicode\n * character in the string. While JavaScript uses UCS-2 internally,\n * this function will convert a pair of surrogate halves (each of which\n * UCS-2 exposes as separate characters) into a single code point,\n * matching UTF-16.\n * @see `punycode.ucs2.encode`\n * @see <https://mathiasbynens.be/notes/javascript-encoding>\n * @memberOf punycode.ucs2\n * @name decode\n * @param {String} string The Unicode input string (UCS-2).\n * @returns {Array} The new array of code points.\n */\nfunction ucs2decode(string) {\n\tconst output = [];\n\tlet counter = 0;\n\tconst length = string.length;\n\twhile (counter < length) {\n\t\tconst value = string.charCodeAt(counter++);\n\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t// It's a high surrogate, and there is a next character.\n\t\t\tconst extra = string.charCodeAt(counter++);\n\t\t\tif ((extra & 0xFC00) == 0xDC00) { // Low surrogate.\n\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t} else {\n\t\t\t\t// It's an unmatched surrogate; only append this code unit, in case the\n\t\t\t\t// next code unit is the high surrogate of a surrogate pair.\n\t\t\t\toutput.push(value);\n\t\t\t\tcounter--;\n\t\t\t}\n\t\t} else {\n\t\t\toutput.push(value);\n\t\t}\n\t}\n\treturn output;\n}\n\n/**\n * Creates a string based on an array of numeric code points.\n * @see `punycode.ucs2.decode`\n * @memberOf punycode.ucs2\n * @name encode\n * @param {Array} codePoints The array of numeric code points.\n * @returns {String} The new Unicode string (UCS-2).\n */\nconst ucs2encode = array => String.fromCodePoint(...array);\n\n/**\n * Converts a basic code point into a digit/integer.\n * @see `digitToBasic()`\n * @private\n * @param {Number} codePoint The basic numeric code point value.\n * @returns {Number} The numeric value of a basic code point (for use in\n * representing integers) in the range `0` to `base - 1`, or `base` if\n * the code point does not represent a value.\n */\nconst basicToDigit = function(codePoint) {\n\tif (codePoint - 0x30 < 0x0A) {\n\t\treturn codePoint - 0x16;\n\t}\n\tif (codePoint - 0x41 < 0x1A) {\n\t\treturn codePoint - 0x41;\n\t}\n\tif (codePoint - 0x61 < 0x1A) {\n\t\treturn codePoint - 0x61;\n\t}\n\treturn base;\n};\n\n/**\n * Converts a digit/integer into a basic code point.\n * @see `basicToDigit()`\n * @private\n * @param {Number} digit The numeric value of a basic code point.\n * @returns {Number} The basic code point whose value (when used for\n * representing integers) is `digit`, which needs to be in the range\n * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n * used; else, the lowercase form is used. The behavior is undefined\n * if `flag` is non-zero and `digit` has no uppercase form.\n */\nconst digitToBasic = function(digit, flag) {\n\t//  0..25 map to ASCII a..z or A..Z\n\t// 26..35 map to ASCII 0..9\n\treturn digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n};\n\n/**\n * Bias adaptation function as per section 3.4 of RFC 3492.\n * https://tools.ietf.org/html/rfc3492#section-3.4\n * @private\n */\nconst adapt = function(delta, numPoints, firstTime) {\n\tlet k = 0;\n\tdelta = firstTime ? floor(delta / damp) : delta >> 1;\n\tdelta += floor(delta / numPoints);\n\tfor (/* no initialization */; delta > baseMinusTMin * tMax >> 1; k += base) {\n\t\tdelta = floor(delta / baseMinusTMin);\n\t}\n\treturn floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n};\n\n/**\n * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n * symbols.\n * @memberOf punycode\n * @param {String} input The Punycode string of ASCII-only symbols.\n * @returns {String} The resulting string of Unicode symbols.\n */\nconst decode = function(input) {\n\t// Don't use UCS-2.\n\tconst output = [];\n\tconst inputLength = input.length;\n\tlet i = 0;\n\tlet n = initialN;\n\tlet bias = initialBias;\n\n\t// Handle the basic code points: let `basic` be the number of input code\n\t// points before the last delimiter, or `0` if there is none, then copy\n\t// the first basic code points to the output.\n\n\tlet basic = input.lastIndexOf(delimiter);\n\tif (basic < 0) {\n\t\tbasic = 0;\n\t}\n\n\tfor (let j = 0; j < basic; ++j) {\n\t\t// if it's not a basic code point\n\t\tif (input.charCodeAt(j) >= 0x80) {\n\t\t\terror('not-basic');\n\t\t}\n\t\toutput.push(input.charCodeAt(j));\n\t}\n\n\t// Main decoding loop: start just after the last delimiter if any basic code\n\t// points were copied; start at the beginning otherwise.\n\n\tfor (let index = basic > 0 ? basic + 1 : 0; index < inputLength; /* no final expression */) {\n\n\t\t// `index` is the index of the next character to be consumed.\n\t\t// Decode a generalized variable-length integer into `delta`,\n\t\t// which gets added to `i`. The overflow checking is easier\n\t\t// if we increase `i` as we go, then subtract off its starting\n\t\t// value at the end to obtain `delta`.\n\t\tlet oldi = i;\n\t\tfor (let w = 1, k = base; /* no condition */; k += base) {\n\n\t\t\tif (index >= inputLength) {\n\t\t\t\terror('invalid-input');\n\t\t\t}\n\n\t\t\tconst digit = basicToDigit(input.charCodeAt(index++));\n\n\t\t\tif (digit >= base || digit > floor((maxInt - i) / w)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\ti += digit * w;\n\t\t\tconst t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\n\t\t\tif (digit < t) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tconst baseMinusT = base - t;\n\t\t\tif (w > floor(maxInt / baseMinusT)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\tw *= baseMinusT;\n\n\t\t}\n\n\t\tconst out = output.length + 1;\n\t\tbias = adapt(i - oldi, out, oldi == 0);\n\n\t\t// `i` was supposed to wrap around from `out` to `0`,\n\t\t// incrementing `n` each time, so we'll fix that now:\n\t\tif (floor(i / out) > maxInt - n) {\n\t\t\terror('overflow');\n\t\t}\n\n\t\tn += floor(i / out);\n\t\ti %= out;\n\n\t\t// Insert `n` at position `i` of the output.\n\t\toutput.splice(i++, 0, n);\n\n\t}\n\n\treturn String.fromCodePoint(...output);\n};\n\n/**\n * Converts a string of Unicode symbols (e.g. a domain name label) to a\n * Punycode string of ASCII-only symbols.\n * @memberOf punycode\n * @param {String} input The string of Unicode symbols.\n * @returns {String} The resulting Punycode string of ASCII-only symbols.\n */\nconst encode = function(input) {\n\tconst output = [];\n\n\t// Convert the input in UCS-2 to an array of Unicode code points.\n\tinput = ucs2decode(input);\n\n\t// Cache the length.\n\tlet inputLength = input.length;\n\n\t// Initialize the state.\n\tlet n = initialN;\n\tlet delta = 0;\n\tlet bias = initialBias;\n\n\t// Handle the basic code points.\n\tfor (const currentValue of input) {\n\t\tif (currentValue < 0x80) {\n\t\t\toutput.push(stringFromCharCode(currentValue));\n\t\t}\n\t}\n\n\tlet basicLength = output.length;\n\tlet handledCPCount = basicLength;\n\n\t// `handledCPCount` is the number of code points that have been handled;\n\t// `basicLength` is the number of basic code points.\n\n\t// Finish the basic string with a delimiter unless it's empty.\n\tif (basicLength) {\n\t\toutput.push(delimiter);\n\t}\n\n\t// Main encoding loop:\n\twhile (handledCPCount < inputLength) {\n\n\t\t// All non-basic code points < n have been handled already. Find the next\n\t\t// larger one:\n\t\tlet m = maxInt;\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue >= n && currentValue < m) {\n\t\t\t\tm = currentValue;\n\t\t\t}\n\t\t}\n\n\t\t// Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n\t\t// but guard against overflow.\n\t\tconst handledCPCountPlusOne = handledCPCount + 1;\n\t\tif (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n\t\t\terror('overflow');\n\t\t}\n\n\t\tdelta += (m - n) * handledCPCountPlusOne;\n\t\tn = m;\n\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue < n && ++delta > maxInt) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\t\t\tif (currentValue == n) {\n\t\t\t\t// Represent delta as a generalized variable-length integer.\n\t\t\t\tlet q = delta;\n\t\t\t\tfor (let k = base; /* no condition */; k += base) {\n\t\t\t\t\tconst t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\t\t\t\t\tif (q < t) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tconst qMinusT = q - t;\n\t\t\t\t\tconst baseMinusT = base - t;\n\t\t\t\t\toutput.push(\n\t\t\t\t\t\tstringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0))\n\t\t\t\t\t);\n\t\t\t\t\tq = floor(qMinusT / baseMinusT);\n\t\t\t\t}\n\n\t\t\t\toutput.push(stringFromCharCode(digitToBasic(q, 0)));\n\t\t\t\tbias = adapt(delta, handledCPCountPlusOne, handledCPCount == basicLength);\n\t\t\t\tdelta = 0;\n\t\t\t\t++handledCPCount;\n\t\t\t}\n\t\t}\n\n\t\t++delta;\n\t\t++n;\n\n\t}\n\treturn output.join('');\n};\n\n/**\n * Converts a Punycode string representing a domain name or an email address\n * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n * it doesn't matter if you call it on a string that has already been\n * converted to Unicode.\n * @memberOf punycode\n * @param {String} input The Punycoded domain name or email address to\n * convert to Unicode.\n * @returns {String} The Unicode representation of the given Punycode\n * string.\n */\nconst toUnicode = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexPunycode.test(string)\n\t\t\t? decode(string.slice(4).toLowerCase())\n\t\t\t: string;\n\t});\n};\n\n/**\n * Converts a Unicode string representing a domain name or an email address to\n * Punycode. Only the non-ASCII parts of the domain name will be converted,\n * i.e. it doesn't matter if you call it with a domain that's already in\n * ASCII.\n * @memberOf punycode\n * @param {String} input The domain name or email address to convert, as a\n * Unicode string.\n * @returns {String} The Punycode representation of the given domain name or\n * email address.\n */\nconst toASCII = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexNonASCII.test(string)\n\t\t\t? 'xn--' + encode(string)\n\t\t\t: string;\n\t});\n};\n\n/*--------------------------------------------------------------------------*/\n\n/** Define the public API */\nconst punycode = {\n\t/**\n\t * A string representing the current Punycode.js version number.\n\t * @memberOf punycode\n\t * @type String\n\t */\n\t'version': '2.1.0',\n\t/**\n\t * An object of methods to convert from JavaScript's internal character\n\t * representation (UCS-2) to Unicode code points, and back.\n\t * @see <https://mathiasbynens.be/notes/javascript-encoding>\n\t * @memberOf punycode\n\t * @type Object\n\t */\n\t'ucs2': {\n\t\t'decode': ucs2decode,\n\t\t'encode': ucs2encode\n\t},\n\t'decode': decode,\n\t'encode': encode,\n\t'toASCII': toASCII,\n\t'toUnicode': toUnicode\n};\n\nexport default punycode;\n", "/**\n * URI.js\n *\n * @fileoverview An RFC 3986 compliant, scheme extendable URI parsing/validating/resolving library for JavaScript.\n * <AUTHOR> href=\"mailto:<EMAIL>\"><PERSON></a>\n * @see http://github.com/garycourt/uri-js\n */\n\n/**\n * Copyright 2011 Gary Court. All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without modification, are\n * permitted provided that the following conditions are met:\n *\n *    1. Redistributions of source code must retain the above copyright notice, this list of\n *       conditions and the following disclaimer.\n *\n *    2. Redistributions in binary form must reproduce the above copyright notice, this list\n *       of conditions and the following disclaimer in the documentation and/or other materials\n *       provided with the distribution.\n *\n * THIS SOFTWARE IS PROVIDED BY GARY COURT ``AS IS'' AND ANY EXPRESS OR IMPLIED\n * WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND\n * FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL GARY COURT OR\n * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR\n * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON\n * ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING\n * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF\n * ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.\n *\n * The views and conclusions contained in the software and documentation are those of the\n * authors and should not be interpreted as representing official policies, either expressed\n * or implied, of Gary Court.\n */\n\nimport URI_PROTOCOL from \"./regexps-uri\";\nimport IRI_PROTOCOL from \"./regexps-iri\";\nimport punycode from \"punycode\";\nimport { toUpperCase, typeOf, assign } from \"./util\";\n\nexport interface URIComponents {\n\tscheme?:string;\n\tuserinfo?:string;\n\thost?:string;\n\tport?:number|string;\n\tpath?:string;\n\tquery?:string;\n\tfragment?:string;\n\treference?:string;\n\terror?:string;\n}\n\nexport interface URIOptions {\n\tscheme?:string;\n\treference?:string;\n\ttolerant?:boolean;\n\tabsolutePath?:boolean;\n\tiri?:boolean;\n\tunicodeSupport?:boolean;\n\tdomainHost?:boolean;\n}\n\nexport interface URISchemeHandler<Components extends URIComponents = URIComponents, Options extends URIOptions = URIOptions, ParentComponents extends URIComponents = URIComponents> {\n\tscheme:string;\n\tparse(components:ParentComponents, options:Options):Components;\n\tserialize(components:Components, options:Options):ParentComponents;\n\tunicodeSupport?:boolean;\n\tdomainHost?:boolean;\n\tabsolutePath?:boolean;\n}\n\nexport interface URIRegExps {\n\tNOT_SCHEME : RegExp,\n\tNOT_USERINFO : RegExp,\n\tNOT_HOST : RegExp,\n\tNOT_PATH : RegExp,\n\tNOT_PATH_NOSCHEME : RegExp,\n\tNOT_QUERY : RegExp,\n\tNOT_FRAGMENT : RegExp,\n\tESCAPE : RegExp,\n\tUNRESERVED : RegExp,\n\tOTHER_CHARS : RegExp,\n\tPCT_ENCODED : RegExp,\n\tIPV4ADDRESS : RegExp,\n\tIPV6ADDRESS : RegExp,\n}\n\nexport const SCHEMES:{[scheme:string]:URISchemeHandler} = {};\n\nexport function pctEncChar(chr:string):string {\n\tconst c = chr.charCodeAt(0);\n\tlet e:string;\n\n\tif (c < 16) e = \"%0\" + c.toString(16).toUpperCase();\n\telse if (c < 128) e = \"%\" + c.toString(16).toUpperCase();\n\telse if (c < 2048) e = \"%\" + ((c >> 6) | 192).toString(16).toUpperCase() + \"%\" + ((c & 63) | 128).toString(16).toUpperCase();\n\telse e = \"%\" + ((c >> 12) | 224).toString(16).toUpperCase() + \"%\" + (((c >> 6) & 63) | 128).toString(16).toUpperCase() + \"%\" + ((c & 63) | 128).toString(16).toUpperCase();\n\n\treturn e;\n}\n\nexport function pctDecChars(str:string):string {\n\tlet newStr = \"\";\n\tlet i = 0;\n\tconst il = str.length;\n\n\twhile (i < il) {\n\t\tconst c = parseInt(str.substr(i + 1, 2), 16);\n\n\t\tif (c < 128) {\n\t\t\tnewStr += String.fromCharCode(c);\n\t\t\ti += 3;\n\t\t}\n\t\telse if (c >= 194 && c < 224) {\n\t\t\tif ((il - i) >= 6) {\n\t\t\t\tconst c2 = parseInt(str.substr(i + 4, 2), 16);\n\t\t\t\tnewStr += String.fromCharCode(((c & 31) << 6) | (c2 & 63));\n\t\t\t} else {\n\t\t\t\tnewStr += str.substr(i, 6);\n\t\t\t}\n\t\t\ti += 6;\n\t\t}\n\t\telse if (c >= 224) {\n\t\t\tif ((il - i) >= 9) {\n\t\t\t\tconst c2 = parseInt(str.substr(i + 4, 2), 16);\n\t\t\t\tconst c3 = parseInt(str.substr(i + 7, 2), 16);\n\t\t\t\tnewStr += String.fromCharCode(((c & 15) << 12) | ((c2 & 63) << 6) | (c3 & 63));\n\t\t\t} else {\n\t\t\t\tnewStr += str.substr(i, 9);\n\t\t\t}\n\t\t\ti += 9;\n\t\t}\n\t\telse {\n\t\t\tnewStr += str.substr(i, 3);\n\t\t\ti += 3;\n\t\t}\n\t}\n\n\treturn newStr;\n}\n\nfunction _normalizeComponentEncoding(components:URIComponents, protocol:URIRegExps) {\n\tfunction decodeUnreserved(str:string):string {\n\t\tconst decStr = pctDecChars(str);\n\t\treturn (!decStr.match(protocol.UNRESERVED) ? str : decStr);\n\t}\n\n\tif (components.scheme) components.scheme = String(components.scheme).replace(protocol.PCT_ENCODED, decodeUnreserved).toLowerCase().replace(protocol.NOT_SCHEME, \"\");\n\tif (components.userinfo !== undefined) components.userinfo = String(components.userinfo).replace(protocol.PCT_ENCODED, decodeUnreserved).replace(protocol.NOT_USERINFO, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n\tif (components.host !== undefined) components.host = String(components.host).replace(protocol.PCT_ENCODED, decodeUnreserved).toLowerCase().replace(protocol.NOT_HOST, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n\tif (components.path !== undefined) components.path = String(components.path).replace(protocol.PCT_ENCODED, decodeUnreserved).replace((components.scheme ? protocol.NOT_PATH : protocol.NOT_PATH_NOSCHEME), pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n\tif (components.query !== undefined) components.query = String(components.query).replace(protocol.PCT_ENCODED, decodeUnreserved).replace(protocol.NOT_QUERY, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n\tif (components.fragment !== undefined) components.fragment = String(components.fragment).replace(protocol.PCT_ENCODED, decodeUnreserved).replace(protocol.NOT_FRAGMENT, pctEncChar).replace(protocol.PCT_ENCODED, toUpperCase);\n\n\treturn components;\n};\n\nfunction _stripLeadingZeros(str:string):string {\n\treturn str.replace(/^0*(.*)/, \"$1\") || \"0\";\n}\n\nfunction _normalizeIPv4(host:string, protocol:URIRegExps):string {\n\tconst matches = host.match(protocol.IPV4ADDRESS) || [];\n\tconst [, address] = matches;\n\t\n\tif (address) {\n\t\treturn address.split(\".\").map(_stripLeadingZeros).join(\".\");\n\t} else {\n\t\treturn host;\n\t}\n}\n\nfunction _normalizeIPv6(host:string, protocol:URIRegExps):string {\n\tconst matches = host.match(protocol.IPV6ADDRESS) || [];\n\tconst [, address, zone] = matches;\n\n\tif (address) {\n\t\tconst [last, first] = address.toLowerCase().split('::').reverse();\n\t\tconst firstFields = first ? first.split(\":\").map(_stripLeadingZeros) : [];\n\t\tconst lastFields = last.split(\":\").map(_stripLeadingZeros);\n\t\tconst isLastFieldIPv4Address = protocol.IPV4ADDRESS.test(lastFields[lastFields.length - 1]);\n\t\tconst fieldCount = isLastFieldIPv4Address ? 7 : 8;\n\t\tconst lastFieldsStart = lastFields.length - fieldCount;\n\t\tconst fields = Array<string>(fieldCount);\n\n\t\tfor (let x = 0; x < fieldCount; ++x) {\n\t\t\tfields[x] = firstFields[x] || lastFields[lastFieldsStart + x] || '';\n\t\t}\n\n\t\tif (isLastFieldIPv4Address) {\n\t\t\tfields[fieldCount - 1] = _normalizeIPv4(fields[fieldCount - 1], protocol);\n\t\t}\n\n\t\tconst allZeroFields = fields.reduce<Array<{index:number,length:number}>>((acc, field, index) => {\n\t\t\tif (!field || field === \"0\") {\n\t\t\t\tconst lastLongest = acc[acc.length - 1];\n\t\t\t\tif (lastLongest && lastLongest.index + lastLongest.length === index) {\n\t\t\t\t\tlastLongest.length++;\n\t\t\t\t} else {\n\t\t\t\t\tacc.push({ index, length : 1 });\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn acc;\n\t\t}, []);\n\n\t\tconst longestZeroFields = allZeroFields.sort((a, b) => b.length - a.length)[0];\n\n\t\tlet newHost:string;\n\t\tif (longestZeroFields && longestZeroFields.length > 1) {\n\t\t\tconst newFirst = fields.slice(0, longestZeroFields.index) ;\n\t\t\tconst newLast = fields.slice(longestZeroFields.index + longestZeroFields.length);\n\t\t\tnewHost = newFirst.join(\":\") + \"::\" + newLast.join(\":\");\n\t\t} else {\n\t\t\tnewHost = fields.join(\":\");\n\t\t}\n\n\t\tif (zone) {\n\t\t\tnewHost += \"%\" + zone;\n\t\t}\n\n\t\treturn newHost;\n\t} else {\n\t\treturn host;\n\t}\n}\n\nconst URI_PARSE = /^(?:([^:\\/?#]+):)?(?:\\/\\/((?:([^\\/?#@]*)@)?(\\[[^\\/?#\\]]+\\]|[^\\/?#:]*)(?:\\:(\\d*))?))?([^?#]*)(?:\\?([^#]*))?(?:#((?:.|\\n|\\r)*))?/i;\nconst NO_MATCH_IS_UNDEFINED = (<RegExpMatchArray>(\"\").match(/(){0}/))[1] === undefined;\n\nexport function parse(uriString:string, options:URIOptions = {}):URIComponents {\n\tconst components:URIComponents = {};\n\tconst protocol = (options.iri !== false ? IRI_PROTOCOL : URI_PROTOCOL);\n\n\tif (options.reference === \"suffix\") uriString = (options.scheme ? options.scheme + \":\" : \"\") + \"//\" + uriString;\n\n\tconst matches = uriString.match(URI_PARSE);\n\n\tif (matches) {\n\t\tif (NO_MATCH_IS_UNDEFINED) {\n\t\t\t//store each component\n\t\t\tcomponents.scheme = matches[1];\n\t\t\tcomponents.userinfo = matches[3];\n\t\t\tcomponents.host = matches[4];\n\t\t\tcomponents.port = parseInt(matches[5], 10);\n\t\t\tcomponents.path = matches[6] || \"\";\n\t\t\tcomponents.query = matches[7];\n\t\t\tcomponents.fragment = matches[8];\n\n\t\t\t//fix port number\n\t\t\tif (isNaN(components.port)) {\n\t\t\t\tcomponents.port = matches[5];\n\t\t\t}\n\t\t} else {  //IE FIX for improper RegExp matching\n\t\t\t//store each component\n\t\t\tcomponents.scheme = matches[1] || undefined;\n\t\t\tcomponents.userinfo = (uriString.indexOf(\"@\") !== -1 ? matches[3] : undefined);\n\t\t\tcomponents.host = (uriString.indexOf(\"//\") !== -1 ? matches[4] : undefined);\n\t\t\tcomponents.port = parseInt(matches[5], 10);\n\t\t\tcomponents.path = matches[6] || \"\";\n\t\t\tcomponents.query = (uriString.indexOf(\"?\") !== -1 ? matches[7] : undefined);\n\t\t\tcomponents.fragment = (uriString.indexOf(\"#\") !== -1 ? matches[8] : undefined);\n\n\t\t\t//fix port number\n\t\t\tif (isNaN(components.port)) {\n\t\t\t\tcomponents.port = (uriString.match(/\\/\\/(?:.|\\n)*\\:(?:\\/|\\?|\\#|$)/) ? matches[4] : undefined);\n\t\t\t}\n\t\t}\n\n\t\tif (components.host) {\n\t\t\t//normalize IP hosts\n\t\t\tcomponents.host = _normalizeIPv6(_normalizeIPv4(components.host, protocol), protocol);\n\t\t}\n\n\t\t//determine reference type\n\t\tif (components.scheme === undefined && components.userinfo === undefined && components.host === undefined && components.port === undefined && !components.path && components.query === undefined) {\n\t\t\tcomponents.reference = \"same-document\";\n\t\t} else if (components.scheme === undefined) {\n\t\t\tcomponents.reference = \"relative\";\n\t\t} else if (components.fragment === undefined) {\n\t\t\tcomponents.reference = \"absolute\";\n\t\t} else {\n\t\t\tcomponents.reference = \"uri\";\n\t\t}\n\n\t\t//check for reference errors\n\t\tif (options.reference && options.reference !== \"suffix\" && options.reference !== components.reference) {\n\t\t\tcomponents.error = components.error || \"URI is not a \" + options.reference + \" reference.\";\n\t\t}\n\n\t\t//find scheme handler\n\t\tconst schemeHandler = SCHEMES[(options.scheme || components.scheme || \"\").toLowerCase()];\n\n\t\t//check if scheme can't handle IRIs\n\t\tif (!options.unicodeSupport && (!schemeHandler || !schemeHandler.unicodeSupport)) {\n\t\t\t//if host component is a domain name\n\t\t\tif (components.host && (options.domainHost || (schemeHandler && schemeHandler.domainHost))) {\n\t\t\t\t//convert Unicode IDN -> ASCII IDN\n\t\t\t\ttry {\n\t\t\t\t\tcomponents.host = punycode.toASCII(components.host.replace(protocol.PCT_ENCODED, pctDecChars).toLowerCase());\n\t\t\t\t} catch (e) {\n\t\t\t\t\tcomponents.error = components.error || \"Host's domain name can not be converted to ASCII via punycode: \" + e;\n\t\t\t\t}\n\t\t\t}\n\t\t\t//convert IRI -> URI\n\t\t\t_normalizeComponentEncoding(components, URI_PROTOCOL);\n\t\t} else {\n\t\t\t//normalize encodings\n\t\t\t_normalizeComponentEncoding(components, protocol);\n\t\t}\n\n\t\t//perform scheme specific parsing\n\t\tif (schemeHandler && schemeHandler.parse) {\n\t\t\tschemeHandler.parse(components, options);\n\t\t}\n\t} else {\n\t\tcomponents.error = components.error || \"URI can not be parsed.\";\n\t}\n\n\treturn components;\n};\n\nfunction _recomposeAuthority(components:URIComponents, options:URIOptions):string|undefined {\n\tconst protocol = (options.iri !== false ? IRI_PROTOCOL : URI_PROTOCOL);\n\tconst uriTokens:Array<string> = [];\n\n\tif (components.userinfo !== undefined) {\n\t\turiTokens.push(components.userinfo);\n\t\turiTokens.push(\"@\");\n\t}\n\n\tif (components.host !== undefined) {\n\t\t//normalize IP hosts, add brackets and escape zone separator for IPv6\n\t\turiTokens.push(_normalizeIPv6(_normalizeIPv4(String(components.host), protocol), protocol).replace(protocol.IPV6ADDRESS, (_, $1, $2) => \"[\" + $1 + ($2 ? \"%25\" + $2 : \"\") + \"]\"));\n\t}\n\n\tif (typeof components.port === \"number\" || typeof components.port === \"string\") {\n\t\turiTokens.push(\":\");\n\t\turiTokens.push(String(components.port));\n\t}\n\n\treturn uriTokens.length ? uriTokens.join(\"\") : undefined;\n};\n\nconst RDS1 = /^\\.\\.?\\//;\nconst RDS2 = /^\\/\\.(\\/|$)/;\nconst RDS3 = /^\\/\\.\\.(\\/|$)/;\nconst RDS4 = /^\\.\\.?$/;\nconst RDS5 = /^\\/?(?:.|\\n)*?(?=\\/|$)/;\n\nexport function removeDotSegments(input:string):string {\n\tconst output:Array<string> = [];\n\n\twhile (input.length) {\n\t\tif (input.match(RDS1)) {\n\t\t\tinput = input.replace(RDS1, \"\");\n\t\t} else if (input.match(RDS2)) {\n\t\t\tinput = input.replace(RDS2, \"/\");\n\t\t} else if (input.match(RDS3)) {\n\t\t\tinput = input.replace(RDS3, \"/\");\n\t\t\toutput.pop();\n\t\t} else if (input === \".\" || input === \"..\") {\n\t\t\tinput = \"\";\n\t\t} else {\n\t\t\tconst im = input.match(RDS5);\n\t\t\tif (im) {\n\t\t\t\tconst s = im[0];\n\t\t\t\tinput = input.slice(s.length);\n\t\t\t\toutput.push(s);\n\t\t\t} else {\n\t\t\t\tthrow new Error(\"Unexpected dot segment condition\");\n\t\t\t}\n\t\t}\n\t}\n\n\treturn output.join(\"\");\n};\n\nexport function serialize(components:URIComponents, options:URIOptions = {}):string {\n\tconst protocol = (options.iri ? IRI_PROTOCOL : URI_PROTOCOL);\n\tconst uriTokens:Array<string> = [];\n\n\t//find scheme handler\n\tconst schemeHandler = SCHEMES[(options.scheme || components.scheme || \"\").toLowerCase()];\n\n\t//perform scheme specific serialization\n\tif (schemeHandler && schemeHandler.serialize) schemeHandler.serialize(components, options);\n\n\tif (components.host) {\n\t\t//if host component is an IPv6 address\n\t\tif (protocol.IPV6ADDRESS.test(components.host)) {\n\t\t\t//TODO: normalize IPv6 address as per RFC 5952\n\t\t}\n\n\t\t//if host component is a domain name\n\t\telse if (options.domainHost || (schemeHandler && schemeHandler.domainHost)) {\n\t\t\t//convert IDN via punycode\n\t\t\ttry {\n\t\t\t\tcomponents.host = (!options.iri ? punycode.toASCII(components.host.replace(protocol.PCT_ENCODED, pctDecChars).toLowerCase()) : punycode.toUnicode(components.host));\n\t\t\t} catch (e) {\n\t\t\t\tcomponents.error = components.error || \"Host's domain name can not be converted to \" + (!options.iri ? \"ASCII\" : \"Unicode\") + \" via punycode: \" + e;\n\t\t\t}\n\t\t}\n\t}\n\n\t//normalize encoding\n\t_normalizeComponentEncoding(components, protocol);\n\n\tif (options.reference !== \"suffix\" && components.scheme) {\n\t\turiTokens.push(components.scheme);\n\t\turiTokens.push(\":\");\n\t}\n\n\tconst authority = _recomposeAuthority(components, options);\n\tif (authority !== undefined) {\n\t\tif (options.reference !== \"suffix\") {\n\t\t\turiTokens.push(\"//\");\n\t\t}\n\n\t\turiTokens.push(authority);\n\n\t\tif (components.path && components.path.charAt(0) !== \"/\") {\n\t\t\turiTokens.push(\"/\");\n\t\t}\n\t}\n\n\tif (components.path !== undefined) {\n\t\tlet s = components.path;\n\n\t\tif (!options.absolutePath && (!schemeHandler || !schemeHandler.absolutePath)) {\n\t\t\ts = removeDotSegments(s);\n\t\t}\n\n\t\tif (authority === undefined) {\n\t\t\ts = s.replace(/^\\/\\//, \"/%2F\");  //don't allow the path to start with \"//\"\n\t\t}\n\n\t\turiTokens.push(s);\n\t}\n\n\tif (components.query !== undefined) {\n\t\turiTokens.push(\"?\");\n\t\turiTokens.push(components.query);\n\t}\n\n\tif (components.fragment !== undefined) {\n\t\turiTokens.push(\"#\");\n\t\turiTokens.push(components.fragment);\n\t}\n\n\treturn uriTokens.join(\"\");  //merge tokens into a string\n};\n\nexport function resolveComponents(base:URIComponents, relative:URIComponents, options:URIOptions = {}, skipNormalization?:boolean):URIComponents {\n\tconst target:URIComponents = {};\n\n\tif (!skipNormalization) {\n\t\tbase = parse(serialize(base, options), options);  //normalize base components\n\t\trelative = parse(serialize(relative, options), options);  //normalize relative components\n\t}\n\toptions = options || {};\n\n\tif (!options.tolerant && relative.scheme) {\n\t\ttarget.scheme = relative.scheme;\n\t\t//target.authority = relative.authority;\n\t\ttarget.userinfo = relative.userinfo;\n\t\ttarget.host = relative.host;\n\t\ttarget.port = relative.port;\n\t\ttarget.path = removeDotSegments(relative.path || \"\");\n\t\ttarget.query = relative.query;\n\t} else {\n\t\tif (relative.userinfo !== undefined || relative.host !== undefined || relative.port !== undefined) {\n\t\t\t//target.authority = relative.authority;\n\t\t\ttarget.userinfo = relative.userinfo;\n\t\t\ttarget.host = relative.host;\n\t\t\ttarget.port = relative.port;\n\t\t\ttarget.path = removeDotSegments(relative.path || \"\");\n\t\t\ttarget.query = relative.query;\n\t\t} else {\n\t\t\tif (!relative.path) {\n\t\t\t\ttarget.path = base.path;\n\t\t\t\tif (relative.query !== undefined) {\n\t\t\t\t\ttarget.query = relative.query;\n\t\t\t\t} else {\n\t\t\t\t\ttarget.query = base.query;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (relative.path.charAt(0) === \"/\") {\n\t\t\t\t\ttarget.path = removeDotSegments(relative.path);\n\t\t\t\t} else {\n\t\t\t\t\tif ((base.userinfo !== undefined || base.host !== undefined || base.port !== undefined) && !base.path) {\n\t\t\t\t\t\ttarget.path = \"/\" + relative.path;\n\t\t\t\t\t} else if (!base.path) {\n\t\t\t\t\t\ttarget.path = relative.path;\n\t\t\t\t\t} else {\n\t\t\t\t\t\ttarget.path = base.path.slice(0, base.path.lastIndexOf(\"/\") + 1) + relative.path;\n\t\t\t\t\t}\n\t\t\t\t\ttarget.path = removeDotSegments(target.path);\n\t\t\t\t}\n\t\t\t\ttarget.query = relative.query;\n\t\t\t}\n\t\t\t//target.authority = base.authority;\n\t\t\ttarget.userinfo = base.userinfo;\n\t\t\ttarget.host = base.host;\n\t\t\ttarget.port = base.port;\n\t\t}\n\t\ttarget.scheme = base.scheme;\n\t}\n\n\ttarget.fragment = relative.fragment;\n\n\treturn target;\n};\n\nexport function resolve(baseURI:string, relativeURI:string, options?:URIOptions):string {\n\tconst schemelessOptions = assign({ scheme : 'null' }, options);\n\treturn serialize(resolveComponents(parse(baseURI, schemelessOptions), parse(relativeURI, schemelessOptions), schemelessOptions, true), schemelessOptions);\n};\n\nexport function normalize(uri:string, options?:URIOptions):string;\nexport function normalize(uri:URIComponents, options?:URIOptions):URIComponents;\nexport function normalize(uri:any, options?:URIOptions):any {\n\tif (typeof uri === \"string\") {\n\t\turi = serialize(parse(uri, options), options);\n\t} else if (typeOf(uri) === \"object\") {\n\t\turi = parse(serialize(<URIComponents>uri, options), options);\n\t}\n\n\treturn uri;\n};\n\nexport function equal(uriA:string, uriB:string, options?: URIOptions):boolean;\nexport function equal(uriA:URIComponents, uriB:URIComponents, options?:URIOptions):boolean;\nexport function equal(uriA:any, uriB:any, options?:URIOptions):boolean {\n\tif (typeof uriA === \"string\") {\n\t\turiA = serialize(parse(uriA, options), options);\n\t} else if (typeOf(uriA) === \"object\") {\n\t\turiA = serialize(<URIComponents>uriA, options);\n\t}\n\n\tif (typeof uriB === \"string\") {\n\t\turiB = serialize(parse(uriB, options), options);\n\t} else if (typeOf(uriB) === \"object\") {\n\t\turiB = serialize(<URIComponents>uriB, options);\n\t}\n\n\treturn uriA === uriB;\n};\n\nexport function escapeComponent(str:string, options?:URIOptions):string {\n\treturn str && str.toString().replace((!options || !options.iri ? URI_PROTOCOL.ESCAPE : IRI_PROTOCOL.ESCAPE), pctEncChar);\n};\n\nexport function unescapeComponent(str:string, options?:URIOptions):string {\n\treturn str && str.toString().replace((!options || !options.iri ? URI_PROTOCOL.PCT_ENCODED : IRI_PROTOCOL.PCT_ENCODED), pctDecChars);\n};\n", "import { URISchemeHand<PERSON>, URIComponents, URIOptions } from \"../uri\";\n\nexport interface WSComponents extends URIComponents {\n\tresourceName?: string;\n\tsecure?: boolean;\n}\n\nfunction isSecure(wsComponents:WSComponents):boolean {\n\treturn typeof wsComponents.secure === 'boolean' ? wsComponents.secure : String(wsComponents.scheme).toLowerCase() === \"wss\";\n}\n\n//RFC 6455\nconst handler:URISchemeHandler = {\n\tscheme : \"ws\",\n\n\tdomainHost : true,\n\n\tparse : function (components:URIComponents, options:URIOptions):WSComponents {\n\t\tconst wsComponents = components as WSComponents;\n\n\t\t//indicate if the secure flag is set\n\t\twsComponents.secure = isSecure(wsComponents);\n\n\t\t//construct resouce name\n\t\twsComponents.resourceName = (wsComponents.path || '/') + (wsComponents.query ? '?' + wsComponents.query : '');\n\t\twsComponents.path = undefined;\n\t\twsComponents.query = undefined;\n\n\t\treturn wsComponents;\n\t},\n\n\tserialize : function (wsComponents:WSComponents, options:URIOptions):URIComponents {\n\t\t//normalize the default port\n\t\tif (wsComponents.port === (isSecure(wsComponents) ? 443 : 80) || wsComponents.port === \"\") {\n\t\t\twsComponents.port = undefined;\n\t\t}\n\n\t\t//ensure scheme matches secure flag\n\t\tif (typeof wsComponents.secure === 'boolean') {\n\t\t\twsComponents.scheme = (wsComponents.secure ? 'wss' : 'ws');\n\t\t\twsComponents.secure = undefined;\n\t\t}\n\n\t\t//reconstruct path from resource name\n\t\tif (wsComponents.resourceName) {\n\t\t\tconst [path, query] = wsComponents.resourceName.split('?');\n\t\t\twsComponents.path = (path && path !== '/' ? path : undefined);\n\t\t\twsComponents.query = query;\n\t\t\twsComponents.resourceName = undefined;\n\t\t}\n\n\t\t//forbid fragment component\n\t\twsComponents.fragment = undefined;\n\n\t\treturn wsComponents;\n\t}\n};\n\nexport default handler;", "import { URI<PERSON>cheme<PERSON><PERSON><PERSON>, URIComponents, URIOptions } from \"../uri\";\nimport { pctEnc<PERSON>har, pctDec<PERSON>hars, unescapeComponent } from \"../uri\";\nimport punycode from \"punycode\";\nimport { merge, subexp, toUpperCase, toArray } from \"../util\";\n\nexport interface MailtoHeaders {\n\t[hfname:string]:string\n}\n\nexport interface MailtoComponents extends URIComponents {\n\tto:Array<string>,\n\theaders?:MailtoHeaders,\n\tsubject?:string,\n\tbody?:string\n}\n\nconst O:MailtoHeaders = {};\nconst isIRI = true;\n\n//RFC 3986\nconst UNRESERVED$$ = \"[A-Za-z0-9\\\\-\\\\.\\\\_\\\\~\" + (isIRI ? \"\\\\xA0-\\\\u200D\\\\u2010-\\\\u2029\\\\u202F-\\\\uD7FF\\\\uF900-\\\\uFDCF\\\\uFDF0-\\\\uFFEF\" : \"\") + \"]\";\nconst HEXDIG$$ = \"[0-9A-Fa-f]\";  //case-insensitive\nconst PCT_ENCODED$ = subexp(subexp(\"%[EFef]\" + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$) + \"|\" + subexp(\"%[89A-Fa-f]\" + HEXDIG$$ + \"%\" + HEXDIG$$ + HEXDIG$$) + \"|\" + subexp(\"%\" + HEXDIG$$ + HEXDIG$$));  //expanded\n\n//RFC 5322, except these symbols as per RFC 6068: @ : / ? # [ ] & ; =\n//const ATEXT$$ = \"[A-Za-z0-9\\\\!\\\\#\\\\$\\\\%\\\\&\\\\'\\\\*\\\\+\\\\-\\\\/\\\\=\\\\?\\\\^\\\\_\\\\`\\\\{\\\\|\\\\}\\\\~]\";\n//const WSP$$ = \"[\\\\x20\\\\x09]\";\n//const OBS_QTEXT$$ = \"[\\\\x01-\\\\x08\\\\x0B\\\\x0C\\\\x0E-\\\\x1F\\\\x7F]\";  //(%d1-8 / %d11-12 / %d14-31 / %d127)\n//const QTEXT$$ = merge(\"[\\\\x21\\\\x23-\\\\x5B\\\\x5D-\\\\x7E]\", OBS_QTEXT$$);  //%d33 / %d35-91 / %d93-126 / obs-qtext\n//const VCHAR$$ = \"[\\\\x21-\\\\x7E]\";\n//const WSP$$ = \"[\\\\x20\\\\x09]\";\n//const OBS_QP$ = subexp(\"\\\\\\\\\" + merge(\"[\\\\x00\\\\x0D\\\\x0A]\", OBS_QTEXT$$));  //%d0 / CR / LF / obs-qtext\n//const FWS$ = subexp(subexp(WSP$$ + \"*\" + \"\\\\x0D\\\\x0A\") + \"?\" + WSP$$ + \"+\");\n//const QUOTED_PAIR$ = subexp(subexp(\"\\\\\\\\\" + subexp(VCHAR$$ + \"|\" + WSP$$)) + \"|\" + OBS_QP$);\n//const QUOTED_STRING$ = subexp('\\\\\"' + subexp(FWS$ + \"?\" + QCONTENT$) + \"*\" + FWS$ + \"?\" + '\\\\\"');\nconst ATEXT$$ = \"[A-Za-z0-9\\\\!\\\\$\\\\%\\\\'\\\\*\\\\+\\\\-\\\\^\\\\_\\\\`\\\\{\\\\|\\\\}\\\\~]\";\nconst QTEXT$$ = \"[\\\\!\\\\$\\\\%\\\\'\\\\(\\\\)\\\\*\\\\+\\\\,\\\\-\\\\.0-9\\\\<\\\\>A-Z\\\\x5E-\\\\x7E]\";\nconst VCHAR$$ = merge(QTEXT$$, \"[\\\\\\\"\\\\\\\\]\");\nconst DOT_ATOM_TEXT$ = subexp(ATEXT$$ + \"+\" + subexp(\"\\\\.\" + ATEXT$$ + \"+\") + \"*\");\nconst QUOTED_PAIR$ = subexp(\"\\\\\\\\\" + VCHAR$$);\nconst QCONTENT$ = subexp(QTEXT$$ + \"|\" + QUOTED_PAIR$);\nconst QUOTED_STRING$ = subexp('\\\\\"' + QCONTENT$ + \"*\" + '\\\\\"');\n\n//RFC 6068\nconst DTEXT_NO_OBS$$ = \"[\\\\x21-\\\\x5A\\\\x5E-\\\\x7E]\";  //%d33-90 / %d94-126\nconst SOME_DELIMS$$ = \"[\\\\!\\\\$\\\\'\\\\(\\\\)\\\\*\\\\+\\\\,\\\\;\\\\:\\\\@]\";\nconst QCHAR$ = subexp(UNRESERVED$$ + \"|\" + PCT_ENCODED$ + \"|\" + SOME_DELIMS$$);\nconst DOMAIN$ = subexp(DOT_ATOM_TEXT$ + \"|\" + \"\\\\[\" + DTEXT_NO_OBS$$ + \"*\" + \"\\\\]\");\nconst LOCAL_PART$ = subexp(DOT_ATOM_TEXT$ + \"|\" + QUOTED_STRING$);\nconst ADDR_SPEC$ = subexp(LOCAL_PART$ + \"\\\\@\" + DOMAIN$);\nconst TO$ = subexp(ADDR_SPEC$ + subexp(\"\\\\,\" + ADDR_SPEC$) + \"*\");\nconst HFNAME$ = subexp(QCHAR$ + \"*\");\nconst HFVALUE$ = HFNAME$;\nconst HFIELD$ = subexp(HFNAME$ + \"\\\\=\" + HFVALUE$);\nconst HFIELDS2$ = subexp(HFIELD$ + subexp(\"\\\\&\" + HFIELD$) + \"*\");\nconst HFIELDS$ = subexp(\"\\\\?\" + HFIELDS2$);\nconst MAILTO_URI = new RegExp(\"^mailto\\\\:\" + TO$ + \"?\" + HFIELDS$ + \"?$\");\n\nconst UNRESERVED = new RegExp(UNRESERVED$$, \"g\");\nconst PCT_ENCODED = new RegExp(PCT_ENCODED$, \"g\");\nconst NOT_LOCAL_PART = new RegExp(merge(\"[^]\", ATEXT$$, \"[\\\\.]\", '[\\\\\"]', VCHAR$$), \"g\");\nconst NOT_DOMAIN = new RegExp(merge(\"[^]\", ATEXT$$, \"[\\\\.]\", \"[\\\\[]\", DTEXT_NO_OBS$$, \"[\\\\]]\"), \"g\");\nconst NOT_HFNAME = new RegExp(merge(\"[^]\", UNRESERVED$$, SOME_DELIMS$$), \"g\");\nconst NOT_HFVALUE = NOT_HFNAME;\nconst TO = new RegExp(\"^\" + TO$ + \"$\");\nconst HFIELDS = new RegExp(\"^\" + HFIELDS2$ + \"$\");\n\nfunction decodeUnreserved(str:string):string {\n\tconst decStr = pctDecChars(str);\n\treturn (!decStr.match(UNRESERVED) ? str : decStr);\n}\n\nconst handler:URISchemeHandler<MailtoComponents> =  {\n\tscheme : \"mailto\",\n\n\tparse : function (components:URIComponents, options:URIOptions):MailtoComponents {\n\t\tconst mailtoComponents = components as MailtoComponents;\n\t\tconst to = mailtoComponents.to = (mailtoComponents.path ? mailtoComponents.path.split(\",\") : []);\n\t\tmailtoComponents.path = undefined;\n\n\t\tif (mailtoComponents.query) {\n\t\t\tlet unknownHeaders = false\n\t\t\tconst headers:MailtoHeaders = {};\n\t\t\tconst hfields = mailtoComponents.query.split(\"&\");\n\n\t\t\tfor (let x = 0, xl = hfields.length; x < xl; ++x) {\n\t\t\t\tconst hfield = hfields[x].split(\"=\");\n\n\t\t\t\tswitch (hfield[0]) {\n\t\t\t\t\tcase \"to\":\n\t\t\t\t\t\tconst toAddrs = hfield[1].split(\",\");\n\t\t\t\t\t\tfor (let x = 0, xl = toAddrs.length; x < xl; ++x) {\n\t\t\t\t\t\t\tto.push(toAddrs[x]);\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"subject\":\n\t\t\t\t\t\tmailtoComponents.subject = unescapeComponent(hfield[1], options);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase \"body\":\n\t\t\t\t\t\tmailtoComponents.body = unescapeComponent(hfield[1], options);\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tdefault:\n\t\t\t\t\t\tunknownHeaders = true;\n\t\t\t\t\t\theaders[unescapeComponent(hfield[0], options)] = unescapeComponent(hfield[1], options);\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (unknownHeaders) mailtoComponents.headers = headers;\n\t\t}\n\n\t\tmailtoComponents.query = undefined;\n\n\t\tfor (let x = 0, xl = to.length; x < xl; ++x) {\n\t\t\tconst addr = to[x].split(\"@\");\n\n\t\t\taddr[0] = unescapeComponent(addr[0]);\n\n\t\t\tif (!options.unicodeSupport) {\n\t\t\t\t//convert Unicode IDN -> ASCII IDN\n\t\t\t\ttry {\n\t\t\t\t\taddr[1] = punycode.toASCII(unescapeComponent(addr[1], options).toLowerCase());\n\t\t\t\t} catch (e) {\n\t\t\t\t\tmailtoComponents.error = mailtoComponents.error || \"Email address's domain name can not be converted to ASCII via punycode: \" + e;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\taddr[1] = unescapeComponent(addr[1], options).toLowerCase();\n\t\t\t}\n\n\t\t\tto[x] = addr.join(\"@\");\n\t\t}\n\n\t\treturn mailtoComponents;\n\t},\n\n\tserialize : function (mailtoComponents:MailtoComponents, options:URIOptions):URIComponents {\n\t\tconst components = mailtoComponents as URIComponents;\n\t\tconst to = toArray(mailtoComponents.to);\n\t\tif (to) {\n\t\t\tfor (let x = 0, xl = to.length; x < xl; ++x) {\n\t\t\t\tconst toAddr = String(to[x]);\n\t\t\t\tconst atIdx = toAddr.lastIndexOf(\"@\");\n\t\t\t\tconst localPart = (toAddr.slice(0, atIdx)).replace(PCT_ENCODED, decodeUnreserved).replace(PCT_ENCODED, toUpperCase).replace(NOT_LOCAL_PART, pctEncChar);\n\t\t\t\tlet domain = toAddr.slice(atIdx + 1);\n\n\t\t\t\t//convert IDN via punycode\n\t\t\t\ttry {\n\t\t\t\t\tdomain = (!options.iri ? punycode.toASCII(unescapeComponent(domain, options).toLowerCase()) : punycode.toUnicode(domain));\n\t\t\t\t} catch (e) {\n\t\t\t\t\tcomponents.error = components.error || \"Email address's domain name can not be converted to \" + (!options.iri ? \"ASCII\" : \"Unicode\") + \" via punycode: \" + e;\n\t\t\t\t}\n\n\t\t\t\tto[x] = localPart + \"@\" + domain;\n\t\t\t}\n\n\t\t\tcomponents.path = to.join(\",\");\n\t\t}\n\n\t\tconst headers = mailtoComponents.headers = mailtoComponents.headers || {};\n\n\t\tif (mailtoComponents.subject) headers[\"subject\"] = mailtoComponents.subject;\n\t\tif (mailtoComponents.body) headers[\"body\"] = mailtoComponents.body;\n\n\t\tconst fields = [];\n\t\tfor (const name in headers) {\n\t\t\tif (headers[name] !== O[name]) {\n\t\t\t\tfields.push(\n\t\t\t\t\tname.replace(PCT_ENCODED, decodeUnreserved).replace(PCT_ENCODED, toUpperCase).replace(NOT_HFNAME, pctEncChar) +\n\t\t\t\t\t\"=\" +\n\t\t\t\t\theaders[name].replace(PCT_ENCODED, decodeUnreserved).replace(PCT_ENCODED, toUpperCase).replace(NOT_HFVALUE, pctEncChar)\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t\tif (fields.length) {\n\t\t\tcomponents.query = fields.join(\"&\");\n\t\t}\n\n\t\treturn components;\n\t}\n}\n\nexport default handler;", "import { URIRegExps } from \"./uri\";\nimport { buildExps } from \"./regexps-uri\";\n\nexport default buildExps(true);\n", "import { URISchemeHandler, URIComponents, URIOptions } from \"../uri\";\n\nconst handler:URISchemeHandler = {\n\tscheme : \"http\",\n\n\tdomainHost : true,\n\n\tparse : function (components:URIComponents, options:URIOptions):URIComponents {\n\t\t//report missing host\n\t\tif (!components.host) {\n\t\t\tcomponents.error = components.error || \"HTTP URIs must have a host.\";\n\t\t}\n\n\t\treturn components;\n\t},\n\n\tserialize : function (components:URIComponents, options:URIOptions):URIComponents {\n\t\tconst secure = String(components.scheme).toLowerCase() === \"https\";\n\n\t\t//normalize the default port\n\t\tif (components.port === (secure ? 443 : 80) || components.port === \"\") {\n\t\t\tcomponents.port = undefined;\n\t\t}\n\t\t\n\t\t//normalize the empty path\n\t\tif (!components.path) {\n\t\t\tcomponents.path = \"/\";\n\t\t}\n\n\t\t//NOTE: We do not parse query strings for HTTP URIs\n\t\t//as WWW Form Url Encoded query strings are part of the HTML4+ spec,\n\t\t//and not the HTTP spec.\n\n\t\treturn components;\n\t}\n};\n\nexport default handler;", "import { URISchemeHandler, URIComponents, URIOptions } from \"../uri\";\nimport http from \"./http\";\n\nconst handler:URISchemeHandler = {\n\tscheme : \"https\",\n\tdomainHost : http.domainHost,\n\tparse : http.parse,\n\tserialize : http.serialize\n}\n\nexport default handler;", "import { URISchemeHandler, URIComponents, URIOptions } from \"../uri\";\nimport ws from \"./ws\";\n\nconst handler:URISchemeHandler = {\n\tscheme : \"wss\",\n\tdomainHost : ws.domainHost,\n\tparse : ws.parse,\n\tserialize : ws.serialize\n}\n\nexport default handler;", "import { URIScheme<PERSON><PERSON><PERSON>, URICom<PERSON>, URIOptions } from \"../uri\";\nimport { pct<PERSON><PERSON><PERSON><PERSON>, SCHEMES } from \"../uri\";\n\nexport interface URNComponents extends URIComponents {\n\tnid?:string;\n\tnss?:string;\n}\n\nexport interface URNOptions extends URIOptions {\n\tnid?:string;\n}\n\nconst NID$ = \"(?:[0-9A-Za-z][0-9A-Za-z\\\\-]{1,31})\";\nconst PCT_ENCODED$ = \"(?:\\\\%[0-9A-Fa-f]{2})\";\nconst TRANS$$ = \"[0-9A-Za-z\\\\(\\\\)\\\\+\\\\,\\\\-\\\\.\\\\:\\\\=\\\\@\\\\;\\\\$\\\\_\\\\!\\\\*\\\\'\\\\/\\\\?\\\\#]\";\nconst NSS$ = \"(?:(?:\" + PCT_ENCODED$ + \"|\" + TRANS$$ + \")+)\";\nconst URN_SCHEME = new RegExp(\"^urn\\\\:(\" + NID$ + \")$\");\nconst URN_PATH = new RegExp(\"^(\" + NID$ + \")\\\\:(\" + NSS$ + \")$\");\nconst URN_PARSE = /^([^\\:]+)\\:(.*)/;\nconst URN_EXCLUDED = /[\\x00-\\x20\\\\\\\"\\&\\<\\>\\[\\]\\^\\`\\{\\|\\}\\~\\x7F-\\xFF]/g;\n\n//RFC 2141\nconst handler:URISchemeHandler<URNComponents,URNOptions> = {\n\tscheme : \"urn\",\n\n\tparse : function (components:URIComponents, options:URNOptions):URNComponents {\n\t\tconst matches = components.path && components.path.match(URN_PARSE);\n\t\tlet urnComponents = components as URNComponents;\n\n\t\tif (matches) {\n\t\t\tconst scheme = options.scheme || urnComponents.scheme || \"urn\";\n\t\t\tconst nid = matches[1].toLowerCase();\n\t\t\tconst nss = matches[2];\n\t\t\tconst urnScheme = `${scheme}:${options.nid || nid}`;\n\t\t\tconst schemeHandler = SCHEMES[urnScheme];\n\n\t\t\turnComponents.nid = nid;\n\t\t\turnComponents.nss = nss;\n\t\t\turnComponents.path = undefined;\n\n\t\t\tif (schemeHandler) {\n\t\t\t\turnComponents = schemeHandler.parse(urnComponents, options) as URNComponents;\n\t\t\t}\n\t\t} else {\n\t\t\turnComponents.error = urnComponents.error || \"URN can not be parsed.\";\n\t\t}\n\n\t\treturn urnComponents;\n\t},\n\n\tserialize : function (urnComponents:URNComponents, options:URNOptions):URIComponents {\n\t\tconst scheme = options.scheme || urnComponents.scheme || \"urn\";\n\t\tconst nid = urnComponents.nid;\n\t\tconst urnScheme = `${scheme}:${options.nid || nid}`;\n\t\tconst schemeHandler = SCHEMES[urnScheme];\n\n\t\tif (schemeHandler) {\n\t\t\turnComponents = schemeHandler.serialize(urnComponents, options) as URNComponents;\n\t\t}\n\n\t\tconst uriComponents = urnComponents as URIComponents;\n\t\tconst nss = urnComponents.nss;\n\t\turiComponents.path = `${nid || options.nid}:${nss}`;\n\n\t\treturn uriComponents;\n\t},\n};\n\nexport default handler;", "import { URISchemeHandler, URIComponents, URIOptions } from \"../uri\";\nimport { URNComponents } from \"./urn\";\nimport { SCHEMES } from \"../uri\";\n\nexport interface UUIDComponents extends URNComponents {\n\tuuid?: string;\n}\n\nconst UUID = /^[0-9A-Fa-f]{8}(?:\\-[0-9A-Fa-f]{4}){3}\\-[0-9A-Fa-f]{12}$/;\nconst UUID_PARSE = /^[0-9A-Fa-f\\-]{36}/;\n\n//RFC 4122\nconst handler:URISchemeHandler<UUIDComponents, URIOptions, URNComponents> = {\n\tscheme : \"urn:uuid\",\n\n\tparse : function (urnComponents:URNComponents, options:URIOptions):UUIDComponents {\n\t\tconst uuidComponents = urnComponents as UUIDComponents;\n\t\tuuidComponents.uuid = uuidComponents.nss;\n\t\tuuidComponents.nss = undefined;\n\n\t\tif (!options.tolerant && (!uuidComponents.uuid || !uuidComponents.uuid.match(UUID))) {\n\t\t\tuuidComponents.error = uuidComponents.error || \"UUID is not valid.\";\n\t\t}\n\n\t\treturn uuidComponents;\n\t},\n\n\tserialize : function (uuidComponents:UUIDComponents, options:URIOptions):URNComponents {\n\t\tconst urnComponents = uuidComponents as URNComponents;\n\t\t//normalize UUID\n\t\turnComponents.nss = (uuidComponents.uuid || \"\").toLowerCase();\n\t\treturn urnComponents;\n\t},\n};\n\nexport default handler;", "import { SCHEMES } from \"./uri\";\n\nimport http from \"./schemes/http\";\nSCHEMES[http.scheme] = http;\n\nimport https from \"./schemes/https\";\nSCHEMES[https.scheme] = https;\n\nimport ws from \"./schemes/ws\";\nSCHEMES[ws.scheme] = ws;\n\nimport wss from \"./schemes/wss\";\nSCHEMES[wss.scheme] = wss;\n\nimport mailto from \"./schemes/mailto\";\nSCHEMES[mailto.scheme] = mailto;\n\nimport urn from \"./schemes/urn\";\nSCHEMES[urn.scheme] = urn;\n\nimport uuid from \"./schemes/urn-uuid\";\nSCHEMES[uuid.scheme] = uuid;\n\nexport * from \"./uri\";\n"]}