{"name": "@dnd-kit/utilities", "version": "3.2.2", "description": "Internal utilities to bee shared between `@dnd-kit` packages", "author": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/clauderic/dnd-kit.git", "directory": "packages/utilities"}, "scripts": {"start": "tsdx watch --tsconfig tsconfig.build.json --verbose --noClean", "build": "tsdx build --tsconfig tsconfig.build.json", "test": "tsdx test", "lint": "tsdx lint", "prepublish": "npm run build"}, "main": "dist/index.js", "module": "dist/utilities.esm.js", "typings": "dist/index.d.ts", "files": ["README.md", "CHANGELOG.md", "LICENSE", "dist"], "dependencies": {"tslib": "^2.0.0"}, "peerDependencies": {"react": ">=16.8.0"}, "publishConfig": {"access": "public"}}