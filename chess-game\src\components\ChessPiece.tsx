import React from "react";
import { useDraggable } from "@dnd-kit/core";
import type { ChessPiece } from "../types/chess";
import "./ChessPiece.css";

interface ChessPieceProps {
  piece: ChessPiece;
  isDragging?: boolean;
  isInteractive?: boolean;
}

const ChessPieceComponent: React.FC<ChessPieceProps> = ({
  piece,
  isDragging = false,
  isInteractive = true,
}) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging: isDraggingFromHook,
  } = useDraggable({
    id: piece.id,
    disabled: !isInteractive,
  });

  // Obtener el símbolo Unicode para la pieza
  const getPieceSymbol = (piece: ChessPiece): string => {
    const symbols = {
      white: {
        king: "♔",
        queen: "♕",
        rook: "♖",
        bishop: "♗",
        knight: "♘",
        pawn: "♙",
      },
      black: {
        king: "♚",
        queen: "♛",
        rook: "♜",
        bishop: "♝",
        knight: "♞",
        pawn: "♟",
      },
    };

    return symbols[piece.color][piece.type];
  };

  // Obtener el nombre de la pieza para accesibilidad
  const getPieceName = (piece: ChessPiece): string => {
    const names = {
      king: "Rey",
      queen: "Reina",
      rook: "Torre",
      bishop: "Alfil",
      knight: "Caballo",
      pawn: "Peón",
    };

    const colorName = piece.color === "white" ? "Blanco" : "Negro";
    return `${names[piece.type]} ${colorName}`;
  };

  // Generar clases CSS para la pieza
  const getPieceClasses = () => {
    const classes = ["chess-piece"];

    classes.push(`chess-piece--${piece.color}`);
    classes.push(`chess-piece--${piece.type}`);

    if (isDragging || isDraggingFromHook) {
      classes.push("chess-piece--dragging");
    }

    if (isInteractive) {
      classes.push("chess-piece--interactive");
    }

    return classes.join(" ");
  };

  // Aplicar transformación de arrastre
  const style = transform
    ? {
        transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
        zIndex: 1000,
      }
    : undefined;

  return (
    <div
      ref={setNodeRef}
      className={getPieceClasses()}
      style={style}
      {...listeners}
      {...attributes}
      title={getPieceName(piece)}
      role="button"
      tabIndex={isInteractive ? 0 : -1}
      aria-label={getPieceName(piece)}
    >
      <span className="chess-piece__symbol">{getPieceSymbol(piece)}</span>
    </div>
  );
};

export default ChessPieceComponent;
