"use strict";Object.defineProperty(exports,"__esModule",{value:!0});var e,t=require("react"),n=(e=t)&&"object"==typeof e&&"default"in e?e.default:e;const i={display:"none"};exports.HiddenText=function(e){let{id:t,value:r}=e;return n.createElement("div",{id:t,style:i},r)},exports.LiveRegion=function(e){let{id:t,announcement:i,ariaLiveType:r="assertive"}=e;return n.createElement("div",{id:t,style:{position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"},role:"status","aria-live":r,"aria-atomic":!0},i)},exports.useAnnouncement=function(){const[e,n]=t.useState("");return{announce:t.useCallback(e=>{null!=e&&n(e)},[]),announcement:e}};
//# sourceMappingURL=accessibility.cjs.production.min.js.map
