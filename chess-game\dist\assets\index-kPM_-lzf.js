(function(){const c=document.createElement("link").relList;if(c&&c.supports&&c.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))s(d);new MutationObserver(d=>{for(const h of d)if(h.type==="childList")for(const p of h.addedNodes)p.tagName==="LINK"&&p.rel==="modulepreload"&&s(p)}).observe(document,{childList:!0,subtree:!0});function o(d){const h={};return d.integrity&&(h.integrity=d.integrity),d.referrerPolicy&&(h.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?h.credentials="include":d.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function s(d){if(d.ep)return;d.ep=!0;const h=o(d);fetch(d.href,h)}})();function Xy(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var mo={exports:{}},su={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var xh;function Qy(){if(xh)return su;xh=1;var i=Symbol.for("react.transitional.element"),c=Symbol.for("react.fragment");function o(s,d,h){var p=null;if(h!==void 0&&(p=""+h),d.key!==void 0&&(p=""+d.key),"key"in d){h={};for(var y in d)y!=="key"&&(h[y]=d[y])}else h=d;return d=h.ref,{$$typeof:i,type:s,key:p,ref:d!==void 0?d:null,props:h}}return su.Fragment=c,su.jsx=o,su.jsxs=o,su}var Uh;function Ly(){return Uh||(Uh=1,mo.exports=Qy()),mo.exports}var $=Ly(),yo={exports:{}},at={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hh;function Zy(){if(Hh)return at;Hh=1;var i=Symbol.for("react.transitional.element"),c=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),p=Symbol.for("react.context"),y=Symbol.for("react.forward_ref"),m=Symbol.for("react.suspense"),g=Symbol.for("react.memo"),R=Symbol.for("react.lazy"),M=Symbol.for("react.activity"),q=Symbol.iterator;function H(S){return S===null||typeof S!="object"?null:(S=q&&S[q]||S["@@iterator"],typeof S=="function"?S:null)}var X={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},B=Object.assign,L={};function K(S,N,Y){this.props=S,this.context=N,this.refs=L,this.updater=Y||X}K.prototype.isReactComponent={},K.prototype.setState=function(S,N){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,N,"setState")},K.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function Z(){}Z.prototype=K.prototype;function w(S,N,Y){this.props=S,this.context=N,this.refs=L,this.updater=Y||X}var P=w.prototype=new Z;P.constructor=w,B(P,K.prototype),P.isPureReactComponent=!0;var J=Array.isArray;function tt(){}var G={H:null,A:null,T:null,S:null},et=Object.prototype.hasOwnProperty;function vt(S,N,Y){var Q=Y.ref;return{$$typeof:i,type:S,key:N,ref:Q!==void 0?Q:null,props:Y}}function _t(S,N){return vt(S.type,N,S.props)}function gt(S){return typeof S=="object"&&S!==null&&S.$$typeof===i}function Rt(S){var N={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function(Y){return N[Y]})}var Pt=/\/+/g;function Jt(S,N){return typeof S=="object"&&S!==null&&S.key!=null?Rt(""+S.key):N.toString(36)}function ve(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(tt,tt):(S.status="pending",S.then(function(N){S.status==="pending"&&(S.status="fulfilled",S.value=N)},function(N){S.status==="pending"&&(S.status="rejected",S.reason=N)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function _(S,N,Y,Q,nt){var lt=typeof S;(lt==="undefined"||lt==="boolean")&&(S=null);var ht=!1;if(S===null)ht=!0;else switch(lt){case"bigint":case"string":case"number":ht=!0;break;case"object":switch(S.$$typeof){case i:case c:ht=!0;break;case R:return ht=S._init,_(ht(S._payload),N,Y,Q,nt)}}if(ht)return nt=nt(S),ht=Q===""?"."+Jt(S,0):Q,J(nt)?(Y="",ht!=null&&(Y=ht.replace(Pt,"$&/")+"/"),_(nt,N,Y,"",function(te){return te})):nt!=null&&(gt(nt)&&(nt=_t(nt,Y+(nt.key==null||S&&S.key===nt.key?"":(""+nt.key).replace(Pt,"$&/")+"/")+ht)),N.push(nt)),1;ht=0;var kt=Q===""?".":Q+":";if(J(S))for(var Nt=0;Nt<S.length;Nt++)Q=S[Nt],lt=kt+Jt(Q,Nt),ht+=_(Q,N,Y,lt,nt);else if(Nt=H(S),typeof Nt=="function")for(S=Nt.call(S),Nt=0;!(Q=S.next()).done;)Q=Q.value,lt=kt+Jt(Q,Nt++),ht+=_(Q,N,Y,lt,nt);else if(lt==="object"){if(typeof S.then=="function")return _(ve(S),N,Y,Q,nt);throw N=String(S),Error("Objects are not valid as a React child (found: "+(N==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":N)+"). If you meant to render a collection of children, use an array instead.")}return ht}function j(S,N,Y){if(S==null)return S;var Q=[],nt=0;return _(S,Q,"","",function(lt){return N.call(Y,lt,nt++)}),Q}function F(S){if(S._status===-1){var N=S._result;N=N(),N.then(function(Y){(S._status===0||S._status===-1)&&(S._status=1,S._result=Y)},function(Y){(S._status===0||S._status===-1)&&(S._status=2,S._result=Y)}),S._status===-1&&(S._status=0,S._result=N)}if(S._status===1)return S._result.default;throw S._result}var ot=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var N=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(N))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)},bt={map:j,forEach:function(S,N,Y){j(S,function(){N.apply(this,arguments)},Y)},count:function(S){var N=0;return j(S,function(){N++}),N},toArray:function(S){return j(S,function(N){return N})||[]},only:function(S){if(!gt(S))throw Error("React.Children.only expected to receive a single React element child.");return S}};return at.Activity=M,at.Children=bt,at.Component=K,at.Fragment=o,at.Profiler=d,at.PureComponent=w,at.StrictMode=s,at.Suspense=m,at.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=G,at.__COMPILER_RUNTIME={__proto__:null,c:function(S){return G.H.useMemoCache(S)}},at.cache=function(S){return function(){return S.apply(null,arguments)}},at.cacheSignal=function(){return null},at.cloneElement=function(S,N,Y){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var Q=B({},S.props),nt=S.key;if(N!=null)for(lt in N.key!==void 0&&(nt=""+N.key),N)!et.call(N,lt)||lt==="key"||lt==="__self"||lt==="__source"||lt==="ref"&&N.ref===void 0||(Q[lt]=N[lt]);var lt=arguments.length-2;if(lt===1)Q.children=Y;else if(1<lt){for(var ht=Array(lt),kt=0;kt<lt;kt++)ht[kt]=arguments[kt+2];Q.children=ht}return vt(S.type,nt,Q)},at.createContext=function(S){return S={$$typeof:p,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:h,_context:S},S},at.createElement=function(S,N,Y){var Q,nt={},lt=null;if(N!=null)for(Q in N.key!==void 0&&(lt=""+N.key),N)et.call(N,Q)&&Q!=="key"&&Q!=="__self"&&Q!=="__source"&&(nt[Q]=N[Q]);var ht=arguments.length-2;if(ht===1)nt.children=Y;else if(1<ht){for(var kt=Array(ht),Nt=0;Nt<ht;Nt++)kt[Nt]=arguments[Nt+2];nt.children=kt}if(S&&S.defaultProps)for(Q in ht=S.defaultProps,ht)nt[Q]===void 0&&(nt[Q]=ht[Q]);return vt(S,lt,nt)},at.createRef=function(){return{current:null}},at.forwardRef=function(S){return{$$typeof:y,render:S}},at.isValidElement=gt,at.lazy=function(S){return{$$typeof:R,_payload:{_status:-1,_result:S},_init:F}},at.memo=function(S,N){return{$$typeof:g,type:S,compare:N===void 0?null:N}},at.startTransition=function(S){var N=G.T,Y={};G.T=Y;try{var Q=S(),nt=G.S;nt!==null&&nt(Y,Q),typeof Q=="object"&&Q!==null&&typeof Q.then=="function"&&Q.then(tt,ot)}catch(lt){ot(lt)}finally{N!==null&&Y.types!==null&&(N.types=Y.types),G.T=N}},at.unstable_useCacheRefresh=function(){return G.H.useCacheRefresh()},at.use=function(S){return G.H.use(S)},at.useActionState=function(S,N,Y){return G.H.useActionState(S,N,Y)},at.useCallback=function(S,N){return G.H.useCallback(S,N)},at.useContext=function(S){return G.H.useContext(S)},at.useDebugValue=function(){},at.useDeferredValue=function(S,N){return G.H.useDeferredValue(S,N)},at.useEffect=function(S,N){return G.H.useEffect(S,N)},at.useEffectEvent=function(S){return G.H.useEffectEvent(S)},at.useId=function(){return G.H.useId()},at.useImperativeHandle=function(S,N,Y){return G.H.useImperativeHandle(S,N,Y)},at.useInsertionEffect=function(S,N){return G.H.useInsertionEffect(S,N)},at.useLayoutEffect=function(S,N){return G.H.useLayoutEffect(S,N)},at.useMemo=function(S,N){return G.H.useMemo(S,N)},at.useOptimistic=function(S,N){return G.H.useOptimistic(S,N)},at.useReducer=function(S,N,Y){return G.H.useReducer(S,N,Y)},at.useRef=function(S){return G.H.useRef(S)},at.useState=function(S){return G.H.useState(S)},at.useSyncExternalStore=function(S,N,Y){return G.H.useSyncExternalStore(S,N,Y)},at.useTransition=function(){return G.H.useTransition()},at.version="19.2.0",at}var qh;function Ro(){return qh||(qh=1,yo.exports=Zy()),yo.exports}var C=Ro();const Yt=Xy(C);var go={exports:{}},ru={},bo={exports:{}},So={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bh;function Vy(){return Bh||(Bh=1,(function(i){function c(_,j){var F=_.length;_.push(j);t:for(;0<F;){var ot=F-1>>>1,bt=_[ot];if(0<d(bt,j))_[ot]=j,_[F]=bt,F=ot;else break t}}function o(_){return _.length===0?null:_[0]}function s(_){if(_.length===0)return null;var j=_[0],F=_.pop();if(F!==j){_[0]=F;t:for(var ot=0,bt=_.length,S=bt>>>1;ot<S;){var N=2*(ot+1)-1,Y=_[N],Q=N+1,nt=_[Q];if(0>d(Y,F))Q<bt&&0>d(nt,Y)?(_[ot]=nt,_[Q]=F,ot=Q):(_[ot]=Y,_[N]=F,ot=N);else if(Q<bt&&0>d(nt,F))_[ot]=nt,_[Q]=F,ot=Q;else break t}}return j}function d(_,j){var F=_.sortIndex-j.sortIndex;return F!==0?F:_.id-j.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;i.unstable_now=function(){return h.now()}}else{var p=Date,y=p.now();i.unstable_now=function(){return p.now()-y}}var m=[],g=[],R=1,M=null,q=3,H=!1,X=!1,B=!1,L=!1,K=typeof setTimeout=="function"?setTimeout:null,Z=typeof clearTimeout=="function"?clearTimeout:null,w=typeof setImmediate<"u"?setImmediate:null;function P(_){for(var j=o(g);j!==null;){if(j.callback===null)s(g);else if(j.startTime<=_)s(g),j.sortIndex=j.expirationTime,c(m,j);else break;j=o(g)}}function J(_){if(B=!1,P(_),!X)if(o(m)!==null)X=!0,tt||(tt=!0,Rt());else{var j=o(g);j!==null&&ve(J,j.startTime-_)}}var tt=!1,G=-1,et=5,vt=-1;function _t(){return L?!0:!(i.unstable_now()-vt<et)}function gt(){if(L=!1,tt){var _=i.unstable_now();vt=_;var j=!0;try{t:{X=!1,B&&(B=!1,Z(G),G=-1),H=!0;var F=q;try{e:{for(P(_),M=o(m);M!==null&&!(M.expirationTime>_&&_t());){var ot=M.callback;if(typeof ot=="function"){M.callback=null,q=M.priorityLevel;var bt=ot(M.expirationTime<=_);if(_=i.unstable_now(),typeof bt=="function"){M.callback=bt,P(_),j=!0;break e}M===o(m)&&s(m),P(_)}else s(m);M=o(m)}if(M!==null)j=!0;else{var S=o(g);S!==null&&ve(J,S.startTime-_),j=!1}}break t}finally{M=null,q=F,H=!1}j=void 0}}finally{j?Rt():tt=!1}}}var Rt;if(typeof w=="function")Rt=function(){w(gt)};else if(typeof MessageChannel<"u"){var Pt=new MessageChannel,Jt=Pt.port2;Pt.port1.onmessage=gt,Rt=function(){Jt.postMessage(null)}}else Rt=function(){K(gt,0)};function ve(_,j){G=K(function(){_(i.unstable_now())},j)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(_){_.callback=null},i.unstable_forceFrameRate=function(_){0>_||125<_?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):et=0<_?Math.floor(1e3/_):5},i.unstable_getCurrentPriorityLevel=function(){return q},i.unstable_next=function(_){switch(q){case 1:case 2:case 3:var j=3;break;default:j=q}var F=q;q=j;try{return _()}finally{q=F}},i.unstable_requestPaint=function(){L=!0},i.unstable_runWithPriority=function(_,j){switch(_){case 1:case 2:case 3:case 4:case 5:break;default:_=3}var F=q;q=_;try{return j()}finally{q=F}},i.unstable_scheduleCallback=function(_,j,F){var ot=i.unstable_now();switch(typeof F=="object"&&F!==null?(F=F.delay,F=typeof F=="number"&&0<F?ot+F:ot):F=ot,_){case 1:var bt=-1;break;case 2:bt=250;break;case 5:bt=1073741823;break;case 4:bt=1e4;break;default:bt=5e3}return bt=F+bt,_={id:R++,callback:j,priorityLevel:_,startTime:F,expirationTime:bt,sortIndex:-1},F>ot?(_.sortIndex=F,c(g,_),o(m)===null&&_===o(g)&&(B?(Z(G),G=-1):B=!0,ve(J,F-ot))):(_.sortIndex=bt,c(m,_),X||H||(X=!0,tt||(tt=!0,Rt()))),_},i.unstable_shouldYield=_t,i.unstable_wrapCallback=function(_){var j=q;return function(){var F=q;q=j;try{return _.apply(this,arguments)}finally{q=F}}}})(So)),So}var wh;function Ky(){return wh||(wh=1,bo.exports=Vy()),bo.exports}var po={exports:{}},oe={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var jh;function Jy(){if(jh)return oe;jh=1;var i=Ro();function c(m){var g="https://react.dev/errors/"+m;if(1<arguments.length){g+="?args[]="+encodeURIComponent(arguments[1]);for(var R=2;R<arguments.length;R++)g+="&args[]="+encodeURIComponent(arguments[R])}return"Minified React error #"+m+"; visit "+g+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(){}var s={d:{f:o,r:function(){throw Error(c(522))},D:o,C:o,L:o,m:o,X:o,S:o,M:o},p:0,findDOMNode:null},d=Symbol.for("react.portal");function h(m,g,R){var M=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:M==null?null:""+M,children:m,containerInfo:g,implementation:R}}var p=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function y(m,g){if(m==="font")return"";if(typeof g=="string")return g==="use-credentials"?g:""}return oe.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,oe.createPortal=function(m,g){var R=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!g||g.nodeType!==1&&g.nodeType!==9&&g.nodeType!==11)throw Error(c(299));return h(m,g,null,R)},oe.flushSync=function(m){var g=p.T,R=s.p;try{if(p.T=null,s.p=2,m)return m()}finally{p.T=g,s.p=R,s.d.f()}},oe.preconnect=function(m,g){typeof m=="string"&&(g?(g=g.crossOrigin,g=typeof g=="string"?g==="use-credentials"?g:"":void 0):g=null,s.d.C(m,g))},oe.prefetchDNS=function(m){typeof m=="string"&&s.d.D(m)},oe.preinit=function(m,g){if(typeof m=="string"&&g&&typeof g.as=="string"){var R=g.as,M=y(R,g.crossOrigin),q=typeof g.integrity=="string"?g.integrity:void 0,H=typeof g.fetchPriority=="string"?g.fetchPriority:void 0;R==="style"?s.d.S(m,typeof g.precedence=="string"?g.precedence:void 0,{crossOrigin:M,integrity:q,fetchPriority:H}):R==="script"&&s.d.X(m,{crossOrigin:M,integrity:q,fetchPriority:H,nonce:typeof g.nonce=="string"?g.nonce:void 0})}},oe.preinitModule=function(m,g){if(typeof m=="string")if(typeof g=="object"&&g!==null){if(g.as==null||g.as==="script"){var R=y(g.as,g.crossOrigin);s.d.M(m,{crossOrigin:R,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0})}}else g==null&&s.d.M(m)},oe.preload=function(m,g){if(typeof m=="string"&&typeof g=="object"&&g!==null&&typeof g.as=="string"){var R=g.as,M=y(R,g.crossOrigin);s.d.L(m,R,{crossOrigin:M,integrity:typeof g.integrity=="string"?g.integrity:void 0,nonce:typeof g.nonce=="string"?g.nonce:void 0,type:typeof g.type=="string"?g.type:void 0,fetchPriority:typeof g.fetchPriority=="string"?g.fetchPriority:void 0,referrerPolicy:typeof g.referrerPolicy=="string"?g.referrerPolicy:void 0,imageSrcSet:typeof g.imageSrcSet=="string"?g.imageSrcSet:void 0,imageSizes:typeof g.imageSizes=="string"?g.imageSizes:void 0,media:typeof g.media=="string"?g.media:void 0})}},oe.preloadModule=function(m,g){if(typeof m=="string")if(g){var R=y(g.as,g.crossOrigin);s.d.m(m,{as:typeof g.as=="string"&&g.as!=="script"?g.as:void 0,crossOrigin:R,integrity:typeof g.integrity=="string"?g.integrity:void 0})}else s.d.m(m)},oe.requestFormReset=function(m){s.d.r(m)},oe.unstable_batchedUpdates=function(m,g){return m(g)},oe.useFormState=function(m,g,R){return p.H.useFormState(m,g,R)},oe.useFormStatus=function(){return p.H.useHostTransitionStatus()},oe.version="19.2.0",oe}var Yh;function lv(){if(Yh)return po.exports;Yh=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(c){console.error(c)}}return i(),po.exports=Jy(),po.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Gh;function ky(){if(Gh)return ru;Gh=1;var i=Ky(),c=Ro(),o=lv();function s(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)e+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function h(t){var e=t,l=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(l=e.return),t=e.return;while(t)}return e.tag===3?l:null}function p(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function y(t){if(t.tag===31){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function m(t){if(h(t)!==t)throw Error(s(188))}function g(t){var e=t.alternate;if(!e){if(e=h(t),e===null)throw Error(s(188));return e!==t?null:t}for(var l=t,n=e;;){var a=l.return;if(a===null)break;var u=a.alternate;if(u===null){if(n=a.return,n!==null){l=n;continue}break}if(a.child===u.child){for(u=a.child;u;){if(u===l)return m(a),t;if(u===n)return m(a),e;u=u.sibling}throw Error(s(188))}if(l.return!==n.return)l=a,n=u;else{for(var f=!1,r=a.child;r;){if(r===l){f=!0,l=a,n=u;break}if(r===n){f=!0,n=a,l=u;break}r=r.sibling}if(!f){for(r=u.child;r;){if(r===l){f=!0,l=u,n=a;break}if(r===n){f=!0,n=u,l=a;break}r=r.sibling}if(!f)throw Error(s(189))}}if(l.alternate!==n)throw Error(s(190))}if(l.tag!==3)throw Error(s(188));return l.stateNode.current===l?t:e}function R(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=R(t),e!==null)return e;t=t.sibling}return null}var M=Object.assign,q=Symbol.for("react.element"),H=Symbol.for("react.transitional.element"),X=Symbol.for("react.portal"),B=Symbol.for("react.fragment"),L=Symbol.for("react.strict_mode"),K=Symbol.for("react.profiler"),Z=Symbol.for("react.consumer"),w=Symbol.for("react.context"),P=Symbol.for("react.forward_ref"),J=Symbol.for("react.suspense"),tt=Symbol.for("react.suspense_list"),G=Symbol.for("react.memo"),et=Symbol.for("react.lazy"),vt=Symbol.for("react.activity"),_t=Symbol.for("react.memo_cache_sentinel"),gt=Symbol.iterator;function Rt(t){return t===null||typeof t!="object"?null:(t=gt&&t[gt]||t["@@iterator"],typeof t=="function"?t:null)}var Pt=Symbol.for("react.client.reference");function Jt(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===Pt?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case B:return"Fragment";case K:return"Profiler";case L:return"StrictMode";case J:return"Suspense";case tt:return"SuspenseList";case vt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case X:return"Portal";case w:return t.displayName||"Context";case Z:return(t._context.displayName||"Context")+".Consumer";case P:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case G:return e=t.displayName||null,e!==null?e:Jt(t.type)||"Memo";case et:e=t._payload,t=t._init;try{return Jt(t(e))}catch{}}return null}var ve=Array.isArray,_=c.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,j=o.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,F={pending:!1,data:null,method:null,action:null},ot=[],bt=-1;function S(t){return{current:t}}function N(t){0>bt||(t.current=ot[bt],ot[bt]=null,bt--)}function Y(t,e){bt++,ot[bt]=t.current,t.current=e}var Q=S(null),nt=S(null),lt=S(null),ht=S(null);function kt(t,e){switch(Y(lt,e),Y(nt,t),Y(Q,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?eh(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=eh(e),t=lh(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}N(Q),Y(Q,t)}function Nt(){N(Q),N(nt),N(lt)}function te(t){t.memoizedState!==null&&Y(ht,t);var e=Q.current,l=lh(e,t.type);e!==l&&(Y(nt,t),Y(Q,l))}function xl(t){nt.current===t&&(N(Q),N(nt)),ht.current===t&&(N(ht),iu._currentValue=F)}var We,ba;function se(t){if(We===void 0)try{throw Error()}catch(l){var e=l.stack.trim().match(/\n( *(at )?)/);We=e&&e[1]||"",ba=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+We+t+ba}var Ul=!1;function al(t,e){if(!t||Ul)return"";Ul=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var n={DetermineComponentFrameRoot:function(){try{if(e){var U=function(){throw Error()};if(Object.defineProperty(U.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(U,[])}catch(D){var A=D}Reflect.construct(t,[],U)}else{try{U.call()}catch(D){A=D}t.call(U.prototype)}}else{try{throw Error()}catch(D){A=D}(U=t())&&typeof U.catch=="function"&&U.catch(function(){})}}catch(D){if(D&&A&&typeof D.stack=="string")return[D.stack,A.stack]}return[null,null]}};n.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var a=Object.getOwnPropertyDescriptor(n.DetermineComponentFrameRoot,"name");a&&a.configurable&&Object.defineProperty(n.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=n.DetermineComponentFrameRoot(),f=u[0],r=u[1];if(f&&r){var v=f.split(`
`),z=r.split(`
`);for(a=n=0;n<v.length&&!v[n].includes("DetermineComponentFrameRoot");)n++;for(;a<z.length&&!z[a].includes("DetermineComponentFrameRoot");)a++;if(n===v.length||a===z.length)for(n=v.length-1,a=z.length-1;1<=n&&0<=a&&v[n]!==z[a];)a--;for(;1<=n&&0<=a;n--,a--)if(v[n]!==z[a]){if(n!==1||a!==1)do if(n--,a--,0>a||v[n]!==z[a]){var O=`
`+v[n].replace(" at new "," at ");return t.displayName&&O.includes("<anonymous>")&&(O=O.replace("<anonymous>",t.displayName)),O}while(1<=n&&0<=a);break}}}finally{Ul=!1,Error.prepareStackTrace=l}return(l=t?t.displayName||t.name:"")?se(l):""}function Du(t,e){switch(t.tag){case 26:case 27:case 5:return se(t.type);case 16:return se("Lazy");case 13:return t.child!==e&&e!==null?se("Suspense Fallback"):se("Suspense");case 19:return se("SuspenseList");case 0:case 15:return al(t.type,!1);case 11:return al(t.type.render,!1);case 1:return al(t.type,!0);case 31:return se("Activity");default:return""}}function Sa(t){try{var e="",l=null;do e+=Du(t,l),l=t,t=t.return;while(t);return e}catch(n){return`
Error generating stack: `+n.message+`
`+n.stack}}var cn=Object.prototype.hasOwnProperty,He=i.unstable_scheduleCallback,Hl=i.unstable_cancelCallback,Cn=i.unstable_shouldYield,Mu=i.unstable_requestPaint,ie=i.unstable_now,ec=i.unstable_getCurrentPriorityLevel,Cu=i.unstable_ImmediatePriority,sl=i.unstable_UserBlockingPriority,Fe=i.unstable_NormalPriority,fn=i.unstable_LowPriority,pa=i.unstable_IdlePriority,ul=i.log,Ou=i.unstable_setDisableYieldValue,on=null,re=null;function qe(t){if(typeof ul=="function"&&Ou(t),re&&typeof re.setStrictMode=="function")try{re.setStrictMode(on,t)}catch{}}var ce=Math.clz32?Math.clz32:nc,lc=Math.log,_u=Math.LN2;function nc(t){return t>>>=0,t===0?32:31-(lc(t)/_u|0)|0}var On=256,_n=262144,qt=4194304;function Bt(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:return t&261888;case 262144:case 524288:case 1048576:case 2097152:return t&3932160;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function $t(t,e,l){var n=t.pendingLanes;if(n===0)return 0;var a=0,u=t.suspendedLanes,f=t.pingedLanes;t=t.warmLanes;var r=n&134217727;return r!==0?(n=r&~u,n!==0?a=Bt(n):(f&=r,f!==0?a=Bt(f):l||(l=r&~t,l!==0&&(a=Bt(l))))):(r=n&~u,r!==0?a=Bt(r):f!==0?a=Bt(f):l||(l=n&~t,l!==0&&(a=Bt(l)))),a===0?0:e!==0&&e!==a&&(e&u)===0&&(u=a&-a,l=e&-e,u>=l||u===32&&(l&4194048)!==0)?e:a}function me(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function ye(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function fe(){var t=qt;return qt<<=1,(qt&62914560)===0&&(qt=4194304),t}function ge(t){for(var e=[],l=0;31>l;l++)e.push(t);return e}function Be(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Zt(t,e,l,n,a,u){var f=t.pendingLanes;t.pendingLanes=l,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=l,t.entangledLanes&=l,t.errorRecoveryDisabledLanes&=l,t.shellSuspendCounter=0;var r=t.entanglements,v=t.expirationTimes,z=t.hiddenUpdates;for(l=f&~l;0<l;){var O=31-ce(l),U=1<<O;r[O]=0,v[O]=-1;var A=z[O];if(A!==null)for(z[O]=null,O=0;O<A.length;O++){var D=A[O];D!==null&&(D.lane&=-536870913)}l&=~U}n!==0&&Ie(t,n,0),u!==0&&a===0&&t.tag!==0&&(t.suspendedLanes|=u&~(f&~e))}function Ie(t,e,l){t.pendingLanes|=e,t.suspendedLanes&=~e;var n=31-ce(e);t.entangledLanes|=e,t.entanglements[n]=t.entanglements[n]|1073741824|l&261930}function Me(t,e){var l=t.entangledLanes|=e;for(t=t.entanglements;l;){var n=31-ce(l),a=1<<n;a&e|t[n]&e&&(t[n]|=e),l&=~a}}function we(t,e){var l=e&-e;return l=(l&42)!==0?1:rl(l),(l&(t.suspendedLanes|e))!==0?0:l}function rl(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function dl(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Pe(){var t=j.p;return t!==0?t:(t=window.event,t===void 0?32:Dh(t.type))}function ql(t,e){var l=j.p;try{return j.p=t,e()}finally{j.p=l}}var je=Math.random().toString(36).slice(2),ee="__reactFiber$"+je,be="__reactProps$"+je,Rn="__reactContainer$"+je,ac="__reactEvents$"+je,Rv="__reactListeners$"+je,Nv="__reactHandles$"+je,Qo="__reactResources$"+je,Ea="__reactMarker$"+je;function uc(t){delete t[ee],delete t[be],delete t[ac],delete t[Rv],delete t[Nv]}function Nn(t){var e=t[ee];if(e)return e;for(var l=t.parentNode;l;){if(e=l[Rn]||l[ee]){if(l=e.alternate,e.child!==null||l!==null&&l.child!==null)for(t=oh(t);t!==null;){if(l=t[ee])return l;t=oh(t)}return e}t=l,l=t.parentNode}return null}function xn(t){if(t=t[ee]||t[Rn]){var e=t.tag;if(e===5||e===6||e===13||e===31||e===26||e===27||e===3)return t}return null}function Ta(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(s(33))}function Un(t){var e=t[Qo];return e||(e=t[Qo]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Wt(t){t[Ea]=!0}var Lo=new Set,Zo={};function sn(t,e){Hn(t,e),Hn(t+"Capture",e)}function Hn(t,e){for(Zo[t]=e,t=0;t<e.length;t++)Lo.add(e[t])}var xv=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Vo={},Ko={};function Uv(t){return cn.call(Ko,t)?!0:cn.call(Vo,t)?!1:xv.test(t)?Ko[t]=!0:(Vo[t]=!0,!1)}function Ru(t,e,l){if(Uv(e))if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var n=e.toLowerCase().slice(0,5);if(n!=="data-"&&n!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+l)}}function Nu(t,e,l){if(l===null)t.removeAttribute(e);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+l)}}function hl(t,e,l,n){if(n===null)t.removeAttribute(l);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(l);return}t.setAttributeNS(e,l,""+n)}}function Ye(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Jo(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Hv(t,e,l){var n=Object.getOwnPropertyDescriptor(t.constructor.prototype,e);if(!t.hasOwnProperty(e)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var a=n.get,u=n.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return a.call(this)},set:function(f){l=""+f,u.call(this,f)}}),Object.defineProperty(t,e,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function ic(t){if(!t._valueTracker){var e=Jo(t)?"checked":"value";t._valueTracker=Hv(t,e,""+t[e])}}function ko(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var l=e.getValue(),n="";return t&&(n=Jo(t)?t.checked?"true":"false":t.value),t=n,t!==l?(e.setValue(t),!0):!1}function xu(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var qv=/[\n"\\]/g;function Ge(t){return t.replace(qv,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function cc(t,e,l,n,a,u,f,r){t.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?t.type=f:t.removeAttribute("type"),e!=null?f==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+Ye(e)):t.value!==""+Ye(e)&&(t.value=""+Ye(e)):f!=="submit"&&f!=="reset"||t.removeAttribute("value"),e!=null?fc(t,f,Ye(e)):l!=null?fc(t,f,Ye(l)):n!=null&&t.removeAttribute("value"),a==null&&u!=null&&(t.defaultChecked=!!u),a!=null&&(t.checked=a&&typeof a!="function"&&typeof a!="symbol"),r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?t.name=""+Ye(r):t.removeAttribute("name")}function $o(t,e,l,n,a,u,f,r){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||e!=null)){ic(t);return}l=l!=null?""+Ye(l):"",e=e!=null?""+Ye(e):l,r||e===t.value||(t.value=e),t.defaultValue=e}n=n??a,n=typeof n!="function"&&typeof n!="symbol"&&!!n,t.checked=r?t.checked:!!n,t.defaultChecked=!!n,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(t.name=f),ic(t)}function fc(t,e,l){e==="number"&&xu(t.ownerDocument)===t||t.defaultValue===""+l||(t.defaultValue=""+l)}function qn(t,e,l,n){if(t=t.options,e){e={};for(var a=0;a<l.length;a++)e["$"+l[a]]=!0;for(l=0;l<t.length;l++)a=e.hasOwnProperty("$"+t[l].value),t[l].selected!==a&&(t[l].selected=a),a&&n&&(t[l].defaultSelected=!0)}else{for(l=""+Ye(l),e=null,a=0;a<t.length;a++){if(t[a].value===l){t[a].selected=!0,n&&(t[a].defaultSelected=!0);return}e!==null||t[a].disabled||(e=t[a])}e!==null&&(e.selected=!0)}}function Wo(t,e,l){if(e!=null&&(e=""+Ye(e),e!==t.value&&(t.value=e),l==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=l!=null?""+Ye(l):""}function Fo(t,e,l,n){if(e==null){if(n!=null){if(l!=null)throw Error(s(92));if(ve(n)){if(1<n.length)throw Error(s(93));n=n[0]}l=n}l==null&&(l=""),e=l}l=Ye(e),t.defaultValue=l,n=t.textContent,n===l&&n!==""&&n!==null&&(t.value=n),ic(t)}function Bn(t,e){if(e){var l=t.firstChild;if(l&&l===t.lastChild&&l.nodeType===3){l.nodeValue=e;return}}t.textContent=e}var Bv=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Io(t,e,l){var n=e.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?n?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":n?t.setProperty(e,l):typeof l!="number"||l===0||Bv.has(e)?e==="float"?t.cssFloat=l:t[e]=(""+l).trim():t[e]=l+"px"}function Po(t,e,l){if(e!=null&&typeof e!="object")throw Error(s(62));if(t=t.style,l!=null){for(var n in l)!l.hasOwnProperty(n)||e!=null&&e.hasOwnProperty(n)||(n.indexOf("--")===0?t.setProperty(n,""):n==="float"?t.cssFloat="":t[n]="");for(var a in e)n=e[a],e.hasOwnProperty(a)&&l[a]!==n&&Io(t,a,n)}else for(var u in e)e.hasOwnProperty(u)&&Io(t,u,e[u])}function oc(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var wv=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),jv=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Uu(t){return jv.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}function vl(){}var sc=null;function rc(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var wn=null,jn=null;function ts(t){var e=xn(t);if(e&&(t=e.stateNode)){var l=t[be]||null;t:switch(t=e.stateNode,e.type){case"input":if(cc(t,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),e=l.name,l.type==="radio"&&e!=null){for(l=t;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+Ge(""+e)+'"][type="radio"]'),e=0;e<l.length;e++){var n=l[e];if(n!==t&&n.form===t.form){var a=n[be]||null;if(!a)throw Error(s(90));cc(n,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name)}}for(e=0;e<l.length;e++)n=l[e],n.form===t.form&&ko(n)}break t;case"textarea":Wo(t,l.value,l.defaultValue);break t;case"select":e=l.value,e!=null&&qn(t,!!l.multiple,e,!1)}}}var dc=!1;function es(t,e,l){if(dc)return t(e,l);dc=!0;try{var n=t(e);return n}finally{if(dc=!1,(wn!==null||jn!==null)&&(pi(),wn&&(e=wn,t=jn,jn=wn=null,ts(e),t)))for(e=0;e<t.length;e++)ts(t[e])}}function za(t,e){var l=t.stateNode;if(l===null)return null;var n=l[be]||null;if(n===null)return null;l=n[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(n=!n.disabled)||(t=t.type,n=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!n;break t;default:t=!1}if(t)return null;if(l&&typeof l!="function")throw Error(s(231,e,typeof l));return l}var ml=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),hc=!1;if(ml)try{var Aa={};Object.defineProperty(Aa,"passive",{get:function(){hc=!0}}),window.addEventListener("test",Aa,Aa),window.removeEventListener("test",Aa,Aa)}catch{hc=!1}var Bl=null,vc=null,Hu=null;function ls(){if(Hu)return Hu;var t,e=vc,l=e.length,n,a="value"in Bl?Bl.value:Bl.textContent,u=a.length;for(t=0;t<l&&e[t]===a[t];t++);var f=l-t;for(n=1;n<=f&&e[l-n]===a[u-n];n++);return Hu=a.slice(t,1<n?1-n:void 0)}function qu(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function Bu(){return!0}function ns(){return!1}function Se(t){function e(l,n,a,u,f){this._reactName=l,this._targetInst=a,this.type=n,this.nativeEvent=u,this.target=f,this.currentTarget=null;for(var r in t)t.hasOwnProperty(r)&&(l=t[r],this[r]=l?l(u):u[r]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Bu:ns,this.isPropagationStopped=ns,this}return M(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Bu)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Bu)},persist:function(){},isPersistent:Bu}),e}var rn={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},wu=Se(rn),Da=M({},rn,{view:0,detail:0}),Yv=Se(Da),mc,yc,Ma,ju=M({},Da,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:bc,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==Ma&&(Ma&&t.type==="mousemove"?(mc=t.screenX-Ma.screenX,yc=t.screenY-Ma.screenY):yc=mc=0,Ma=t),mc)},movementY:function(t){return"movementY"in t?t.movementY:yc}}),as=Se(ju),Gv=M({},ju,{dataTransfer:0}),Xv=Se(Gv),Qv=M({},Da,{relatedTarget:0}),gc=Se(Qv),Lv=M({},rn,{animationName:0,elapsedTime:0,pseudoElement:0}),Zv=Se(Lv),Vv=M({},rn,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),Kv=Se(Vv),Jv=M({},rn,{data:0}),us=Se(Jv),kv={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},$v={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Wv={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Fv(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=Wv[t])?!!e[t]:!1}function bc(){return Fv}var Iv=M({},Da,{key:function(t){if(t.key){var e=kv[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=qu(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?$v[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:bc,charCode:function(t){return t.type==="keypress"?qu(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?qu(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),Pv=Se(Iv),tm=M({},ju,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),is=Se(tm),em=M({},Da,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:bc}),lm=Se(em),nm=M({},rn,{propertyName:0,elapsedTime:0,pseudoElement:0}),am=Se(nm),um=M({},ju,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),im=Se(um),cm=M({},rn,{newState:0,oldState:0}),fm=Se(cm),om=[9,13,27,32],Sc=ml&&"CompositionEvent"in window,Ca=null;ml&&"documentMode"in document&&(Ca=document.documentMode);var sm=ml&&"TextEvent"in window&&!Ca,cs=ml&&(!Sc||Ca&&8<Ca&&11>=Ca),fs=" ",os=!1;function ss(t,e){switch(t){case"keyup":return om.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function rs(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var Yn=!1;function rm(t,e){switch(t){case"compositionend":return rs(e);case"keypress":return e.which!==32?null:(os=!0,fs);case"textInput":return t=e.data,t===fs&&os?null:t;default:return null}}function dm(t,e){if(Yn)return t==="compositionend"||!Sc&&ss(t,e)?(t=ls(),Hu=vc=Bl=null,Yn=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return cs&&e.locale!=="ko"?null:e.data;default:return null}}var hm={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ds(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!hm[t.type]:e==="textarea"}function hs(t,e,l,n){wn?jn?jn.push(n):jn=[n]:wn=n,e=Ci(e,"onChange"),0<e.length&&(l=new wu("onChange","change",null,l,n),t.push({event:l,listeners:e}))}var Oa=null,_a=null;function vm(t){$d(t,0)}function Yu(t){var e=Ta(t);if(ko(e))return t}function vs(t,e){if(t==="change")return e}var ms=!1;if(ml){var pc;if(ml){var Ec="oninput"in document;if(!Ec){var ys=document.createElement("div");ys.setAttribute("oninput","return;"),Ec=typeof ys.oninput=="function"}pc=Ec}else pc=!1;ms=pc&&(!document.documentMode||9<document.documentMode)}function gs(){Oa&&(Oa.detachEvent("onpropertychange",bs),_a=Oa=null)}function bs(t){if(t.propertyName==="value"&&Yu(_a)){var e=[];hs(e,_a,t,rc(t)),es(vm,e)}}function mm(t,e,l){t==="focusin"?(gs(),Oa=e,_a=l,Oa.attachEvent("onpropertychange",bs)):t==="focusout"&&gs()}function ym(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Yu(_a)}function gm(t,e){if(t==="click")return Yu(e)}function bm(t,e){if(t==="input"||t==="change")return Yu(e)}function Sm(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var Ce=typeof Object.is=="function"?Object.is:Sm;function Ra(t,e){if(Ce(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var l=Object.keys(t),n=Object.keys(e);if(l.length!==n.length)return!1;for(n=0;n<l.length;n++){var a=l[n];if(!cn.call(e,a)||!Ce(t[a],e[a]))return!1}return!0}function Ss(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function ps(t,e){var l=Ss(t);t=0;for(var n;l;){if(l.nodeType===3){if(n=t+l.textContent.length,t<=e&&n>=e)return{node:l,offset:e-t};t=n}t:{for(;l;){if(l.nextSibling){l=l.nextSibling;break t}l=l.parentNode}l=void 0}l=Ss(l)}}function Es(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?Es(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Ts(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=xu(t.document);e instanceof t.HTMLIFrameElement;){try{var l=typeof e.contentWindow.location.href=="string"}catch{l=!1}if(l)t=e.contentWindow;else break;e=xu(t.document)}return e}function Tc(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var pm=ml&&"documentMode"in document&&11>=document.documentMode,Gn=null,zc=null,Na=null,Ac=!1;function zs(t,e,l){var n=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;Ac||Gn==null||Gn!==xu(n)||(n=Gn,"selectionStart"in n&&Tc(n)?n={start:n.selectionStart,end:n.selectionEnd}:(n=(n.ownerDocument&&n.ownerDocument.defaultView||window).getSelection(),n={anchorNode:n.anchorNode,anchorOffset:n.anchorOffset,focusNode:n.focusNode,focusOffset:n.focusOffset}),Na&&Ra(Na,n)||(Na=n,n=Ci(zc,"onSelect"),0<n.length&&(e=new wu("onSelect","select",null,e,l),t.push({event:e,listeners:n}),e.target=Gn)))}function dn(t,e){var l={};return l[t.toLowerCase()]=e.toLowerCase(),l["Webkit"+t]="webkit"+e,l["Moz"+t]="moz"+e,l}var Xn={animationend:dn("Animation","AnimationEnd"),animationiteration:dn("Animation","AnimationIteration"),animationstart:dn("Animation","AnimationStart"),transitionrun:dn("Transition","TransitionRun"),transitionstart:dn("Transition","TransitionStart"),transitioncancel:dn("Transition","TransitionCancel"),transitionend:dn("Transition","TransitionEnd")},Dc={},As={};ml&&(As=document.createElement("div").style,"AnimationEvent"in window||(delete Xn.animationend.animation,delete Xn.animationiteration.animation,delete Xn.animationstart.animation),"TransitionEvent"in window||delete Xn.transitionend.transition);function hn(t){if(Dc[t])return Dc[t];if(!Xn[t])return t;var e=Xn[t],l;for(l in e)if(e.hasOwnProperty(l)&&l in As)return Dc[t]=e[l];return t}var Ds=hn("animationend"),Ms=hn("animationiteration"),Cs=hn("animationstart"),Em=hn("transitionrun"),Tm=hn("transitionstart"),zm=hn("transitioncancel"),Os=hn("transitionend"),_s=new Map,Mc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Mc.push("scrollEnd");function tl(t,e){_s.set(t,e),sn(e,[t])}var Gu=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)},Xe=[],Qn=0,Cc=0;function Xu(){for(var t=Qn,e=Cc=Qn=0;e<t;){var l=Xe[e];Xe[e++]=null;var n=Xe[e];Xe[e++]=null;var a=Xe[e];Xe[e++]=null;var u=Xe[e];if(Xe[e++]=null,n!==null&&a!==null){var f=n.pending;f===null?a.next=a:(a.next=f.next,f.next=a),n.pending=a}u!==0&&Rs(l,a,u)}}function Qu(t,e,l,n){Xe[Qn++]=t,Xe[Qn++]=e,Xe[Qn++]=l,Xe[Qn++]=n,Cc|=n,t.lanes|=n,t=t.alternate,t!==null&&(t.lanes|=n)}function Oc(t,e,l,n){return Qu(t,e,l,n),Lu(t)}function vn(t,e){return Qu(t,null,null,e),Lu(t)}function Rs(t,e,l){t.lanes|=l;var n=t.alternate;n!==null&&(n.lanes|=l);for(var a=!1,u=t.return;u!==null;)u.childLanes|=l,n=u.alternate,n!==null&&(n.childLanes|=l),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(a=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,a&&e!==null&&(a=31-ce(l),t=u.hiddenUpdates,n=t[a],n===null?t[a]=[e]:n.push(e),e.lane=l|536870912),u):null}function Lu(t){if(50<Pa)throw Pa=0,jf=null,Error(s(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var Ln={};function Am(t,e,l,n){this.tag=t,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=n,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Oe(t,e,l,n){return new Am(t,e,l,n)}function _c(t){return t=t.prototype,!(!t||!t.isReactComponent)}function yl(t,e){var l=t.alternate;return l===null?(l=Oe(t.tag,e,t.key,t.mode),l.elementType=t.elementType,l.type=t.type,l.stateNode=t.stateNode,l.alternate=t,t.alternate=l):(l.pendingProps=e,l.type=t.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=t.flags&65011712,l.childLanes=t.childLanes,l.lanes=t.lanes,l.child=t.child,l.memoizedProps=t.memoizedProps,l.memoizedState=t.memoizedState,l.updateQueue=t.updateQueue,e=t.dependencies,l.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},l.sibling=t.sibling,l.index=t.index,l.ref=t.ref,l.refCleanup=t.refCleanup,l}function Ns(t,e){t.flags&=65011714;var l=t.alternate;return l===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=l.childLanes,t.lanes=l.lanes,t.child=l.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=l.memoizedProps,t.memoizedState=l.memoizedState,t.updateQueue=l.updateQueue,t.type=l.type,e=l.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Zu(t,e,l,n,a,u){var f=0;if(n=t,typeof t=="function")_c(t)&&(f=1);else if(typeof t=="string")f=_y(t,l,Q.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case vt:return t=Oe(31,l,e,a),t.elementType=vt,t.lanes=u,t;case B:return mn(l.children,a,u,e);case L:f=8,a|=24;break;case K:return t=Oe(12,l,e,a|2),t.elementType=K,t.lanes=u,t;case J:return t=Oe(13,l,e,a),t.elementType=J,t.lanes=u,t;case tt:return t=Oe(19,l,e,a),t.elementType=tt,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case w:f=10;break t;case Z:f=9;break t;case P:f=11;break t;case G:f=14;break t;case et:f=16,n=null;break t}f=29,l=Error(s(130,t===null?"null":typeof t,"")),n=null}return e=Oe(f,l,e,a),e.elementType=t,e.type=n,e.lanes=u,e}function mn(t,e,l,n){return t=Oe(7,t,n,e),t.lanes=l,t}function Rc(t,e,l){return t=Oe(6,t,null,e),t.lanes=l,t}function xs(t){var e=Oe(18,null,null,0);return e.stateNode=t,e}function Nc(t,e,l){return e=Oe(4,t.children!==null?t.children:[],t.key,e),e.lanes=l,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var Us=new WeakMap;function Qe(t,e){if(typeof t=="object"&&t!==null){var l=Us.get(t);return l!==void 0?l:(e={value:t,source:e,stack:Sa(e)},Us.set(t,e),e)}return{value:t,source:e,stack:Sa(e)}}var Zn=[],Vn=0,Vu=null,xa=0,Le=[],Ze=0,wl=null,il=1,cl="";function gl(t,e){Zn[Vn++]=xa,Zn[Vn++]=Vu,Vu=t,xa=e}function Hs(t,e,l){Le[Ze++]=il,Le[Ze++]=cl,Le[Ze++]=wl,wl=t;var n=il;t=cl;var a=32-ce(n)-1;n&=~(1<<a),l+=1;var u=32-ce(e)+a;if(30<u){var f=a-a%5;u=(n&(1<<f)-1).toString(32),n>>=f,a-=f,il=1<<32-ce(e)+a|l<<a|n,cl=u+t}else il=1<<u|l<<a|n,cl=t}function xc(t){t.return!==null&&(gl(t,1),Hs(t,1,0))}function Uc(t){for(;t===Vu;)Vu=Zn[--Vn],Zn[Vn]=null,xa=Zn[--Vn],Zn[Vn]=null;for(;t===wl;)wl=Le[--Ze],Le[Ze]=null,cl=Le[--Ze],Le[Ze]=null,il=Le[--Ze],Le[Ze]=null}function qs(t,e){Le[Ze++]=il,Le[Ze++]=cl,Le[Ze++]=wl,il=e.id,cl=e.overflow,wl=t}var le=null,Ct=null,dt=!1,jl=null,Ve=!1,Hc=Error(s(519));function Yl(t){var e=Error(s(418,1<arguments.length&&arguments[1]!==void 0&&arguments[1]?"text":"HTML",""));throw Ua(Qe(e,t)),Hc}function Bs(t){var e=t.stateNode,l=t.type,n=t.memoizedProps;switch(e[ee]=t,e[be]=n,l){case"dialog":ft("cancel",e),ft("close",e);break;case"iframe":case"object":case"embed":ft("load",e);break;case"video":case"audio":for(l=0;l<eu.length;l++)ft(eu[l],e);break;case"source":ft("error",e);break;case"img":case"image":case"link":ft("error",e),ft("load",e);break;case"details":ft("toggle",e);break;case"input":ft("invalid",e),$o(e,n.value,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name,!0);break;case"select":ft("invalid",e);break;case"textarea":ft("invalid",e),Fo(e,n.value,n.defaultValue,n.children)}l=n.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||e.textContent===""+l||n.suppressHydrationWarning===!0||Pd(e.textContent,l)?(n.popover!=null&&(ft("beforetoggle",e),ft("toggle",e)),n.onScroll!=null&&ft("scroll",e),n.onScrollEnd!=null&&ft("scrollend",e),n.onClick!=null&&(e.onclick=vl),e=!0):e=!1,e||Yl(t,!0)}function ws(t){for(le=t.return;le;)switch(le.tag){case 5:case 31:case 13:Ve=!1;return;case 27:case 3:Ve=!0;return;default:le=le.return}}function Kn(t){if(t!==le)return!1;if(!dt)return ws(t),dt=!0,!1;var e=t.tag,l;if((l=e!==3&&e!==27)&&((l=e===5)&&(l=t.type,l=!(l!=="form"&&l!=="button")||Pf(t.type,t.memoizedProps)),l=!l),l&&Ct&&Yl(t),ws(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(s(317));Ct=fh(t)}else if(e===31){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(s(317));Ct=fh(t)}else e===27?(e=Ct,Pl(t.type)?(t=ao,ao=null,Ct=t):Ct=e):Ct=le?Je(t.stateNode.nextSibling):null;return!0}function yn(){Ct=le=null,dt=!1}function qc(){var t=jl;return t!==null&&(ze===null?ze=t:ze.push.apply(ze,t),jl=null),t}function Ua(t){jl===null?jl=[t]:jl.push(t)}var Bc=S(null),gn=null,bl=null;function Gl(t,e,l){Y(Bc,e._currentValue),e._currentValue=l}function Sl(t){t._currentValue=Bc.current,N(Bc)}function wc(t,e,l){for(;t!==null;){var n=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,n!==null&&(n.childLanes|=e)):n!==null&&(n.childLanes&e)!==e&&(n.childLanes|=e),t===l)break;t=t.return}}function jc(t,e,l,n){var a=t.child;for(a!==null&&(a.return=t);a!==null;){var u=a.dependencies;if(u!==null){var f=a.child;u=u.firstContext;t:for(;u!==null;){var r=u;u=a;for(var v=0;v<e.length;v++)if(r.context===e[v]){u.lanes|=l,r=u.alternate,r!==null&&(r.lanes|=l),wc(u.return,l,t),n||(f=null);break t}u=r.next}}else if(a.tag===18){if(f=a.return,f===null)throw Error(s(341));f.lanes|=l,u=f.alternate,u!==null&&(u.lanes|=l),wc(f,l,t),f=null}else f=a.child;if(f!==null)f.return=a;else for(f=a;f!==null;){if(f===t){f=null;break}if(a=f.sibling,a!==null){a.return=f.return,f=a;break}f=f.return}a=f}}function Jn(t,e,l,n){t=null;for(var a=e,u=!1;a!==null;){if(!u){if((a.flags&524288)!==0)u=!0;else if((a.flags&262144)!==0)break}if(a.tag===10){var f=a.alternate;if(f===null)throw Error(s(387));if(f=f.memoizedProps,f!==null){var r=a.type;Ce(a.pendingProps.value,f.value)||(t!==null?t.push(r):t=[r])}}else if(a===ht.current){if(f=a.alternate,f===null)throw Error(s(387));f.memoizedState.memoizedState!==a.memoizedState.memoizedState&&(t!==null?t.push(iu):t=[iu])}a=a.return}t!==null&&jc(e,t,l,n),e.flags|=262144}function Ku(t){for(t=t.firstContext;t!==null;){if(!Ce(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function bn(t){gn=t,bl=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function ne(t){return js(gn,t)}function Ju(t,e){return gn===null&&bn(t),js(t,e)}function js(t,e){var l=e._currentValue;if(e={context:e,memoizedValue:l,next:null},bl===null){if(t===null)throw Error(s(308));bl=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else bl=bl.next=e;return l}var Dm=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(l,n){t.push(n)}};this.abort=function(){e.aborted=!0,t.forEach(function(l){return l()})}},Mm=i.unstable_scheduleCallback,Cm=i.unstable_NormalPriority,Gt={$$typeof:w,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Yc(){return{controller:new Dm,data:new Map,refCount:0}}function Ha(t){t.refCount--,t.refCount===0&&Mm(Cm,function(){t.controller.abort()})}var qa=null,Gc=0,kn=0,$n=null;function Om(t,e){if(qa===null){var l=qa=[];Gc=0,kn=Zf(),$n={status:"pending",value:void 0,then:function(n){l.push(n)}}}return Gc++,e.then(Ys,Ys),e}function Ys(){if(--Gc===0&&qa!==null){$n!==null&&($n.status="fulfilled");var t=qa;qa=null,kn=0,$n=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function _m(t,e){var l=[],n={status:"pending",value:null,reason:null,then:function(a){l.push(a)}};return t.then(function(){n.status="fulfilled",n.value=e;for(var a=0;a<l.length;a++)(0,l[a])(e)},function(a){for(n.status="rejected",n.reason=a,a=0;a<l.length;a++)(0,l[a])(void 0)}),n}var Gs=_.S;_.S=function(t,e){zd=ie(),typeof e=="object"&&e!==null&&typeof e.then=="function"&&Om(t,e),Gs!==null&&Gs(t,e)};var Sn=S(null);function Xc(){var t=Sn.current;return t!==null?t:Dt.pooledCache}function ku(t,e){e===null?Y(Sn,Sn.current):Y(Sn,e.pool)}function Xs(){var t=Xc();return t===null?null:{parent:Gt._currentValue,pool:t}}var Wn=Error(s(460)),Qc=Error(s(474)),$u=Error(s(542)),Wu={then:function(){}};function Qs(t){return t=t.status,t==="fulfilled"||t==="rejected"}function Ls(t,e,l){switch(l=t[l],l===void 0?t.push(e):l!==e&&(e.then(vl,vl),e=l),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Vs(t),t;default:if(typeof e.status=="string")e.then(vl,vl);else{if(t=Dt,t!==null&&100<t.shellSuspendCounter)throw Error(s(482));t=e,t.status="pending",t.then(function(n){if(e.status==="pending"){var a=e;a.status="fulfilled",a.value=n}},function(n){if(e.status==="pending"){var a=e;a.status="rejected",a.reason=n}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Vs(t),t}throw En=e,Wn}}function pn(t){try{var e=t._init;return e(t._payload)}catch(l){throw l!==null&&typeof l=="object"&&typeof l.then=="function"?(En=l,Wn):l}}var En=null;function Zs(){if(En===null)throw Error(s(459));var t=En;return En=null,t}function Vs(t){if(t===Wn||t===$u)throw Error(s(483))}var Fn=null,Ba=0;function Fu(t){var e=Ba;return Ba+=1,Fn===null&&(Fn=[]),Ls(Fn,t,e)}function wa(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Iu(t,e){throw e.$$typeof===q?Error(s(525)):(t=Object.prototype.toString.call(e),Error(s(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function Ks(t){function e(E,b){if(t){var T=E.deletions;T===null?(E.deletions=[b],E.flags|=16):T.push(b)}}function l(E,b){if(!t)return null;for(;b!==null;)e(E,b),b=b.sibling;return null}function n(E){for(var b=new Map;E!==null;)E.key!==null?b.set(E.key,E):b.set(E.index,E),E=E.sibling;return b}function a(E,b){return E=yl(E,b),E.index=0,E.sibling=null,E}function u(E,b,T){return E.index=T,t?(T=E.alternate,T!==null?(T=T.index,T<b?(E.flags|=67108866,b):T):(E.flags|=67108866,b)):(E.flags|=1048576,b)}function f(E){return t&&E.alternate===null&&(E.flags|=67108866),E}function r(E,b,T,x){return b===null||b.tag!==6?(b=Rc(T,E.mode,x),b.return=E,b):(b=a(b,T),b.return=E,b)}function v(E,b,T,x){var W=T.type;return W===B?O(E,b,T.props.children,x,T.key):b!==null&&(b.elementType===W||typeof W=="object"&&W!==null&&W.$$typeof===et&&pn(W)===b.type)?(b=a(b,T.props),wa(b,T),b.return=E,b):(b=Zu(T.type,T.key,T.props,null,E.mode,x),wa(b,T),b.return=E,b)}function z(E,b,T,x){return b===null||b.tag!==4||b.stateNode.containerInfo!==T.containerInfo||b.stateNode.implementation!==T.implementation?(b=Nc(T,E.mode,x),b.return=E,b):(b=a(b,T.children||[]),b.return=E,b)}function O(E,b,T,x,W){return b===null||b.tag!==7?(b=mn(T,E.mode,x,W),b.return=E,b):(b=a(b,T),b.return=E,b)}function U(E,b,T){if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return b=Rc(""+b,E.mode,T),b.return=E,b;if(typeof b=="object"&&b!==null){switch(b.$$typeof){case H:return T=Zu(b.type,b.key,b.props,null,E.mode,T),wa(T,b),T.return=E,T;case X:return b=Nc(b,E.mode,T),b.return=E,b;case et:return b=pn(b),U(E,b,T)}if(ve(b)||Rt(b))return b=mn(b,E.mode,T,null),b.return=E,b;if(typeof b.then=="function")return U(E,Fu(b),T);if(b.$$typeof===w)return U(E,Ju(E,b),T);Iu(E,b)}return null}function A(E,b,T,x){var W=b!==null?b.key:null;if(typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint")return W!==null?null:r(E,b,""+T,x);if(typeof T=="object"&&T!==null){switch(T.$$typeof){case H:return T.key===W?v(E,b,T,x):null;case X:return T.key===W?z(E,b,T,x):null;case et:return T=pn(T),A(E,b,T,x)}if(ve(T)||Rt(T))return W!==null?null:O(E,b,T,x,null);if(typeof T.then=="function")return A(E,b,Fu(T),x);if(T.$$typeof===w)return A(E,b,Ju(E,T),x);Iu(E,T)}return null}function D(E,b,T,x,W){if(typeof x=="string"&&x!==""||typeof x=="number"||typeof x=="bigint")return E=E.get(T)||null,r(b,E,""+x,W);if(typeof x=="object"&&x!==null){switch(x.$$typeof){case H:return E=E.get(x.key===null?T:x.key)||null,v(b,E,x,W);case X:return E=E.get(x.key===null?T:x.key)||null,z(b,E,x,W);case et:return x=pn(x),D(E,b,T,x,W)}if(ve(x)||Rt(x))return E=E.get(T)||null,O(b,E,x,W,null);if(typeof x.then=="function")return D(E,b,T,Fu(x),W);if(x.$$typeof===w)return D(E,b,T,Ju(b,x),W);Iu(b,x)}return null}function V(E,b,T,x){for(var W=null,mt=null,k=b,it=b=0,rt=null;k!==null&&it<T.length;it++){k.index>it?(rt=k,k=null):rt=k.sibling;var yt=A(E,k,T[it],x);if(yt===null){k===null&&(k=rt);break}t&&k&&yt.alternate===null&&e(E,k),b=u(yt,b,it),mt===null?W=yt:mt.sibling=yt,mt=yt,k=rt}if(it===T.length)return l(E,k),dt&&gl(E,it),W;if(k===null){for(;it<T.length;it++)k=U(E,T[it],x),k!==null&&(b=u(k,b,it),mt===null?W=k:mt.sibling=k,mt=k);return dt&&gl(E,it),W}for(k=n(k);it<T.length;it++)rt=D(k,E,it,T[it],x),rt!==null&&(t&&rt.alternate!==null&&k.delete(rt.key===null?it:rt.key),b=u(rt,b,it),mt===null?W=rt:mt.sibling=rt,mt=rt);return t&&k.forEach(function(an){return e(E,an)}),dt&&gl(E,it),W}function I(E,b,T,x){if(T==null)throw Error(s(151));for(var W=null,mt=null,k=b,it=b=0,rt=null,yt=T.next();k!==null&&!yt.done;it++,yt=T.next()){k.index>it?(rt=k,k=null):rt=k.sibling;var an=A(E,k,yt.value,x);if(an===null){k===null&&(k=rt);break}t&&k&&an.alternate===null&&e(E,k),b=u(an,b,it),mt===null?W=an:mt.sibling=an,mt=an,k=rt}if(yt.done)return l(E,k),dt&&gl(E,it),W;if(k===null){for(;!yt.done;it++,yt=T.next())yt=U(E,yt.value,x),yt!==null&&(b=u(yt,b,it),mt===null?W=yt:mt.sibling=yt,mt=yt);return dt&&gl(E,it),W}for(k=n(k);!yt.done;it++,yt=T.next())yt=D(k,E,it,yt.value,x),yt!==null&&(t&&yt.alternate!==null&&k.delete(yt.key===null?it:yt.key),b=u(yt,b,it),mt===null?W=yt:mt.sibling=yt,mt=yt);return t&&k.forEach(function(Gy){return e(E,Gy)}),dt&&gl(E,it),W}function At(E,b,T,x){if(typeof T=="object"&&T!==null&&T.type===B&&T.key===null&&(T=T.props.children),typeof T=="object"&&T!==null){switch(T.$$typeof){case H:t:{for(var W=T.key;b!==null;){if(b.key===W){if(W=T.type,W===B){if(b.tag===7){l(E,b.sibling),x=a(b,T.props.children),x.return=E,E=x;break t}}else if(b.elementType===W||typeof W=="object"&&W!==null&&W.$$typeof===et&&pn(W)===b.type){l(E,b.sibling),x=a(b,T.props),wa(x,T),x.return=E,E=x;break t}l(E,b);break}else e(E,b);b=b.sibling}T.type===B?(x=mn(T.props.children,E.mode,x,T.key),x.return=E,E=x):(x=Zu(T.type,T.key,T.props,null,E.mode,x),wa(x,T),x.return=E,E=x)}return f(E);case X:t:{for(W=T.key;b!==null;){if(b.key===W)if(b.tag===4&&b.stateNode.containerInfo===T.containerInfo&&b.stateNode.implementation===T.implementation){l(E,b.sibling),x=a(b,T.children||[]),x.return=E,E=x;break t}else{l(E,b);break}else e(E,b);b=b.sibling}x=Nc(T,E.mode,x),x.return=E,E=x}return f(E);case et:return T=pn(T),At(E,b,T,x)}if(ve(T))return V(E,b,T,x);if(Rt(T)){if(W=Rt(T),typeof W!="function")throw Error(s(150));return T=W.call(T),I(E,b,T,x)}if(typeof T.then=="function")return At(E,b,Fu(T),x);if(T.$$typeof===w)return At(E,b,Ju(E,T),x);Iu(E,T)}return typeof T=="string"&&T!==""||typeof T=="number"||typeof T=="bigint"?(T=""+T,b!==null&&b.tag===6?(l(E,b.sibling),x=a(b,T),x.return=E,E=x):(l(E,b),x=Rc(T,E.mode,x),x.return=E,E=x),f(E)):l(E,b)}return function(E,b,T,x){try{Ba=0;var W=At(E,b,T,x);return Fn=null,W}catch(k){if(k===Wn||k===$u)throw k;var mt=Oe(29,k,null,E.mode);return mt.lanes=x,mt.return=E,mt}finally{}}}var Tn=Ks(!0),Js=Ks(!1),Xl=!1;function Lc(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Zc(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function Ql(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function Ll(t,e,l){var n=t.updateQueue;if(n===null)return null;if(n=n.shared,(St&2)!==0){var a=n.pending;return a===null?e.next=e:(e.next=a.next,a.next=e),n.pending=e,e=Lu(t),Rs(t,null,l),e}return Qu(t,n,e,l),Lu(t)}function ja(t,e,l){if(e=e.updateQueue,e!==null&&(e=e.shared,(l&4194048)!==0)){var n=e.lanes;n&=t.pendingLanes,l|=n,e.lanes=l,Me(t,l)}}function Vc(t,e){var l=t.updateQueue,n=t.alternate;if(n!==null&&(n=n.updateQueue,l===n)){var a=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var f={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?a=u=f:u=u.next=f,l=l.next}while(l!==null);u===null?a=u=e:u=u.next=e}else a=u=e;l={baseState:n.baseState,firstBaseUpdate:a,lastBaseUpdate:u,shared:n.shared,callbacks:n.callbacks},t.updateQueue=l;return}t=l.lastBaseUpdate,t===null?l.firstBaseUpdate=e:t.next=e,l.lastBaseUpdate=e}var Kc=!1;function Ya(){if(Kc){var t=$n;if(t!==null)throw t}}function Ga(t,e,l,n){Kc=!1;var a=t.updateQueue;Xl=!1;var u=a.firstBaseUpdate,f=a.lastBaseUpdate,r=a.shared.pending;if(r!==null){a.shared.pending=null;var v=r,z=v.next;v.next=null,f===null?u=z:f.next=z,f=v;var O=t.alternate;O!==null&&(O=O.updateQueue,r=O.lastBaseUpdate,r!==f&&(r===null?O.firstBaseUpdate=z:r.next=z,O.lastBaseUpdate=v))}if(u!==null){var U=a.baseState;f=0,O=z=v=null,r=u;do{var A=r.lane&-536870913,D=A!==r.lane;if(D?(st&A)===A:(n&A)===A){A!==0&&A===kn&&(Kc=!0),O!==null&&(O=O.next={lane:0,tag:r.tag,payload:r.payload,callback:null,next:null});t:{var V=t,I=r;A=e;var At=l;switch(I.tag){case 1:if(V=I.payload,typeof V=="function"){U=V.call(At,U,A);break t}U=V;break t;case 3:V.flags=V.flags&-65537|128;case 0:if(V=I.payload,A=typeof V=="function"?V.call(At,U,A):V,A==null)break t;U=M({},U,A);break t;case 2:Xl=!0}}A=r.callback,A!==null&&(t.flags|=64,D&&(t.flags|=8192),D=a.callbacks,D===null?a.callbacks=[A]:D.push(A))}else D={lane:A,tag:r.tag,payload:r.payload,callback:r.callback,next:null},O===null?(z=O=D,v=U):O=O.next=D,f|=A;if(r=r.next,r===null){if(r=a.shared.pending,r===null)break;D=r,r=D.next,D.next=null,a.lastBaseUpdate=D,a.shared.pending=null}}while(!0);O===null&&(v=U),a.baseState=v,a.firstBaseUpdate=z,a.lastBaseUpdate=O,u===null&&(a.shared.lanes=0),kl|=f,t.lanes=f,t.memoizedState=U}}function ks(t,e){if(typeof t!="function")throw Error(s(191,t));t.call(e)}function $s(t,e){var l=t.callbacks;if(l!==null)for(t.callbacks=null,t=0;t<l.length;t++)ks(l[t],e)}var In=S(null),Pu=S(0);function Ws(t,e){t=Ol,Y(Pu,t),Y(In,e),Ol=t|e.baseLanes}function Jc(){Y(Pu,Ol),Y(In,In.current)}function kc(){Ol=Pu.current,N(In),N(Pu)}var _e=S(null),Ke=null;function Zl(t){var e=t.alternate;Y(wt,wt.current&1),Y(_e,t),Ke===null&&(e===null||In.current!==null||e.memoizedState!==null)&&(Ke=t)}function $c(t){Y(wt,wt.current),Y(_e,t),Ke===null&&(Ke=t)}function Fs(t){t.tag===22?(Y(wt,wt.current),Y(_e,t),Ke===null&&(Ke=t)):Vl()}function Vl(){Y(wt,wt.current),Y(_e,_e.current)}function Re(t){N(_e),Ke===t&&(Ke=null),N(wt)}var wt=S(0);function ti(t){for(var e=t;e!==null;){if(e.tag===13){var l=e.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||lo(l)||no(l)))return e}else if(e.tag===19&&(e.memoizedProps.revealOrder==="forwards"||e.memoizedProps.revealOrder==="backwards"||e.memoizedProps.revealOrder==="unstable_legacy-backwards"||e.memoizedProps.revealOrder==="together")){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}var pl=0,ut=null,Tt=null,Xt=null,ei=!1,Pn=!1,zn=!1,li=0,Xa=0,ta=null,Rm=0;function Ut(){throw Error(s(321))}function Wc(t,e){if(e===null)return!1;for(var l=0;l<e.length&&l<t.length;l++)if(!Ce(t[l],e[l]))return!1;return!0}function Fc(t,e,l,n,a,u){return pl=u,ut=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,_.H=t===null||t.memoizedState===null?Hr:hf,zn=!1,u=l(n,a),zn=!1,Pn&&(u=Ps(e,l,n,a)),Is(t),u}function Is(t){_.H=Za;var e=Tt!==null&&Tt.next!==null;if(pl=0,Xt=Tt=ut=null,ei=!1,Xa=0,ta=null,e)throw Error(s(300));t===null||Qt||(t=t.dependencies,t!==null&&Ku(t)&&(Qt=!0))}function Ps(t,e,l,n){ut=t;var a=0;do{if(Pn&&(ta=null),Xa=0,Pn=!1,25<=a)throw Error(s(301));if(a+=1,Xt=Tt=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}_.H=qr,u=e(l,n)}while(Pn);return u}function Nm(){var t=_.H,e=t.useState()[0];return e=typeof e.then=="function"?Qa(e):e,t=t.useState()[0],(Tt!==null?Tt.memoizedState:null)!==t&&(ut.flags|=1024),e}function Ic(){var t=li!==0;return li=0,t}function Pc(t,e,l){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~l}function tf(t){if(ei){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}ei=!1}pl=0,Xt=Tt=ut=null,Pn=!1,Xa=li=0,ta=null}function de(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Xt===null?ut.memoizedState=Xt=t:Xt=Xt.next=t,Xt}function jt(){if(Tt===null){var t=ut.alternate;t=t!==null?t.memoizedState:null}else t=Tt.next;var e=Xt===null?ut.memoizedState:Xt.next;if(e!==null)Xt=e,Tt=t;else{if(t===null)throw ut.alternate===null?Error(s(467)):Error(s(310));Tt=t,t={memoizedState:Tt.memoizedState,baseState:Tt.baseState,baseQueue:Tt.baseQueue,queue:Tt.queue,next:null},Xt===null?ut.memoizedState=Xt=t:Xt=Xt.next=t}return Xt}function ni(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Qa(t){var e=Xa;return Xa+=1,ta===null&&(ta=[]),t=Ls(ta,t,e),e=ut,(Xt===null?e.memoizedState:Xt.next)===null&&(e=e.alternate,_.H=e===null||e.memoizedState===null?Hr:hf),t}function ai(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Qa(t);if(t.$$typeof===w)return ne(t)}throw Error(s(438,String(t)))}function ef(t){var e=null,l=ut.updateQueue;if(l!==null&&(e=l.memoCache),e==null){var n=ut.alternate;n!==null&&(n=n.updateQueue,n!==null&&(n=n.memoCache,n!=null&&(e={data:n.data.map(function(a){return a.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),l===null&&(l=ni(),ut.updateQueue=l),l.memoCache=e,l=e.data[e.index],l===void 0)for(l=e.data[e.index]=Array(t),n=0;n<t;n++)l[n]=_t;return e.index++,l}function El(t,e){return typeof e=="function"?e(t):e}function ui(t){var e=jt();return lf(e,Tt,t)}function lf(t,e,l){var n=t.queue;if(n===null)throw Error(s(311));n.lastRenderedReducer=l;var a=t.baseQueue,u=n.pending;if(u!==null){if(a!==null){var f=a.next;a.next=u.next,u.next=f}e.baseQueue=a=u,n.pending=null}if(u=t.baseState,a===null)t.memoizedState=u;else{e=a.next;var r=f=null,v=null,z=e,O=!1;do{var U=z.lane&-536870913;if(U!==z.lane?(st&U)===U:(pl&U)===U){var A=z.revertLane;if(A===0)v!==null&&(v=v.next={lane:0,revertLane:0,gesture:null,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null}),U===kn&&(O=!0);else if((pl&A)===A){z=z.next,A===kn&&(O=!0);continue}else U={lane:0,revertLane:z.revertLane,gesture:null,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},v===null?(r=v=U,f=u):v=v.next=U,ut.lanes|=A,kl|=A;U=z.action,zn&&l(u,U),u=z.hasEagerState?z.eagerState:l(u,U)}else A={lane:U,revertLane:z.revertLane,gesture:z.gesture,action:z.action,hasEagerState:z.hasEagerState,eagerState:z.eagerState,next:null},v===null?(r=v=A,f=u):v=v.next=A,ut.lanes|=U,kl|=U;z=z.next}while(z!==null&&z!==e);if(v===null?f=u:v.next=r,!Ce(u,t.memoizedState)&&(Qt=!0,O&&(l=$n,l!==null)))throw l;t.memoizedState=u,t.baseState=f,t.baseQueue=v,n.lastRenderedState=u}return a===null&&(n.lanes=0),[t.memoizedState,n.dispatch]}function nf(t){var e=jt(),l=e.queue;if(l===null)throw Error(s(311));l.lastRenderedReducer=t;var n=l.dispatch,a=l.pending,u=e.memoizedState;if(a!==null){l.pending=null;var f=a=a.next;do u=t(u,f.action),f=f.next;while(f!==a);Ce(u,e.memoizedState)||(Qt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),l.lastRenderedState=u}return[u,n]}function tr(t,e,l){var n=ut,a=jt(),u=dt;if(u){if(l===void 0)throw Error(s(407));l=l()}else l=e();var f=!Ce((Tt||a).memoizedState,l);if(f&&(a.memoizedState=l,Qt=!0),a=a.queue,cf(nr.bind(null,n,a,t),[t]),a.getSnapshot!==e||f||Xt!==null&&Xt.memoizedState.tag&1){if(n.flags|=2048,ea(9,{destroy:void 0},lr.bind(null,n,a,l,e),null),Dt===null)throw Error(s(349));u||(pl&127)!==0||er(n,e,l)}return l}function er(t,e,l){t.flags|=16384,t={getSnapshot:e,value:l},e=ut.updateQueue,e===null?(e=ni(),ut.updateQueue=e,e.stores=[t]):(l=e.stores,l===null?e.stores=[t]:l.push(t))}function lr(t,e,l,n){e.value=l,e.getSnapshot=n,ar(e)&&ur(t)}function nr(t,e,l){return l(function(){ar(e)&&ur(t)})}function ar(t){var e=t.getSnapshot;t=t.value;try{var l=e();return!Ce(t,l)}catch{return!0}}function ur(t){var e=vn(t,2);e!==null&&Ae(e,t,2)}function af(t){var e=de();if(typeof t=="function"){var l=t;if(t=l(),zn){qe(!0);try{l()}finally{qe(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:El,lastRenderedState:t},e}function ir(t,e,l,n){return t.baseState=l,lf(t,Tt,typeof n=="function"?n:El)}function xm(t,e,l,n,a){if(fi(t))throw Error(s(485));if(t=e.action,t!==null){var u={payload:a,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){u.listeners.push(f)}};_.T!==null?l(!0):u.isTransition=!1,n(u),l=e.pending,l===null?(u.next=e.pending=u,cr(e,u)):(u.next=l.next,e.pending=l.next=u)}}function cr(t,e){var l=e.action,n=e.payload,a=t.state;if(e.isTransition){var u=_.T,f={};_.T=f;try{var r=l(a,n),v=_.S;v!==null&&v(f,r),fr(t,e,r)}catch(z){uf(t,e,z)}finally{u!==null&&f.types!==null&&(u.types=f.types),_.T=u}}else try{u=l(a,n),fr(t,e,u)}catch(z){uf(t,e,z)}}function fr(t,e,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(n){or(t,e,n)},function(n){return uf(t,e,n)}):or(t,e,l)}function or(t,e,l){e.status="fulfilled",e.value=l,sr(e),t.state=l,e=t.pending,e!==null&&(l=e.next,l===e?t.pending=null:(l=l.next,e.next=l,cr(t,l)))}function uf(t,e,l){var n=t.pending;if(t.pending=null,n!==null){n=n.next;do e.status="rejected",e.reason=l,sr(e),e=e.next;while(e!==n)}t.action=null}function sr(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function rr(t,e){return e}function dr(t,e){if(dt){var l=Dt.formState;if(l!==null){t:{var n=ut;if(dt){if(Ct){e:{for(var a=Ct,u=Ve;a.nodeType!==8;){if(!u){a=null;break e}if(a=Je(a.nextSibling),a===null){a=null;break e}}u=a.data,a=u==="F!"||u==="F"?a:null}if(a){Ct=Je(a.nextSibling),n=a.data==="F!";break t}}Yl(n)}n=!1}n&&(e=l[0])}}return l=de(),l.memoizedState=l.baseState=e,n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:rr,lastRenderedState:e},l.queue=n,l=Nr.bind(null,ut,n),n.dispatch=l,n=af(!1),u=df.bind(null,ut,!1,n.queue),n=de(),a={state:e,dispatch:null,action:t,pending:null},n.queue=a,l=xm.bind(null,ut,a,u,l),a.dispatch=l,n.memoizedState=t,[e,l,!1]}function hr(t){var e=jt();return vr(e,Tt,t)}function vr(t,e,l){if(e=lf(t,e,rr)[0],t=ui(El)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var n=Qa(e)}catch(f){throw f===Wn?$u:f}else n=e;e=jt();var a=e.queue,u=a.dispatch;return l!==e.memoizedState&&(ut.flags|=2048,ea(9,{destroy:void 0},Um.bind(null,a,l),null)),[n,u,t]}function Um(t,e){t.action=e}function mr(t){var e=jt(),l=Tt;if(l!==null)return vr(e,l,t);jt(),e=e.memoizedState,l=jt();var n=l.queue.dispatch;return l.memoizedState=t,[e,n,!1]}function ea(t,e,l,n){return t={tag:t,create:l,deps:n,inst:e,next:null},e=ut.updateQueue,e===null&&(e=ni(),ut.updateQueue=e),l=e.lastEffect,l===null?e.lastEffect=t.next=t:(n=l.next,l.next=t,t.next=n,e.lastEffect=t),t}function yr(){return jt().memoizedState}function ii(t,e,l,n){var a=de();ut.flags|=t,a.memoizedState=ea(1|e,{destroy:void 0},l,n===void 0?null:n)}function ci(t,e,l,n){var a=jt();n=n===void 0?null:n;var u=a.memoizedState.inst;Tt!==null&&n!==null&&Wc(n,Tt.memoizedState.deps)?a.memoizedState=ea(e,u,l,n):(ut.flags|=t,a.memoizedState=ea(1|e,u,l,n))}function gr(t,e){ii(8390656,8,t,e)}function cf(t,e){ci(2048,8,t,e)}function Hm(t){ut.flags|=4;var e=ut.updateQueue;if(e===null)e=ni(),ut.updateQueue=e,e.events=[t];else{var l=e.events;l===null?e.events=[t]:l.push(t)}}function br(t){var e=jt().memoizedState;return Hm({ref:e,nextImpl:t}),function(){if((St&2)!==0)throw Error(s(440));return e.impl.apply(void 0,arguments)}}function Sr(t,e){return ci(4,2,t,e)}function pr(t,e){return ci(4,4,t,e)}function Er(t,e){if(typeof e=="function"){t=t();var l=e(t);return function(){typeof l=="function"?l():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function Tr(t,e,l){l=l!=null?l.concat([t]):null,ci(4,4,Er.bind(null,e,t),l)}function ff(){}function zr(t,e){var l=jt();e=e===void 0?null:e;var n=l.memoizedState;return e!==null&&Wc(e,n[1])?n[0]:(l.memoizedState=[t,e],t)}function Ar(t,e){var l=jt();e=e===void 0?null:e;var n=l.memoizedState;if(e!==null&&Wc(e,n[1]))return n[0];if(n=t(),zn){qe(!0);try{t()}finally{qe(!1)}}return l.memoizedState=[n,e],n}function of(t,e,l){return l===void 0||(pl&1073741824)!==0&&(st&261930)===0?t.memoizedState=e:(t.memoizedState=l,t=Dd(),ut.lanes|=t,kl|=t,l)}function Dr(t,e,l,n){return Ce(l,e)?l:In.current!==null?(t=of(t,l,n),Ce(t,e)||(Qt=!0),t):(pl&42)===0||(pl&1073741824)!==0&&(st&261930)===0?(Qt=!0,t.memoizedState=l):(t=Dd(),ut.lanes|=t,kl|=t,e)}function Mr(t,e,l,n,a){var u=j.p;j.p=u!==0&&8>u?u:8;var f=_.T,r={};_.T=r,df(t,!1,e,l);try{var v=a(),z=_.S;if(z!==null&&z(r,v),v!==null&&typeof v=="object"&&typeof v.then=="function"){var O=_m(v,n);La(t,e,O,Ue(t))}else La(t,e,n,Ue(t))}catch(U){La(t,e,{then:function(){},status:"rejected",reason:U},Ue())}finally{j.p=u,f!==null&&r.types!==null&&(f.types=r.types),_.T=f}}function qm(){}function sf(t,e,l,n){if(t.tag!==5)throw Error(s(476));var a=Cr(t).queue;Mr(t,a,e,F,l===null?qm:function(){return Or(t),l(n)})}function Cr(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:F,baseState:F,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:El,lastRenderedState:F},next:null};var l={};return e.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:El,lastRenderedState:l},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Or(t){var e=Cr(t);e.next===null&&(e=t.alternate.memoizedState),La(t,e.next.queue,{},Ue())}function rf(){return ne(iu)}function _r(){return jt().memoizedState}function Rr(){return jt().memoizedState}function Bm(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var l=Ue();t=Ql(l);var n=Ll(e,t,l);n!==null&&(Ae(n,e,l),ja(n,e,l)),e={cache:Yc()},t.payload=e;return}e=e.return}}function wm(t,e,l){var n=Ue();l={lane:n,revertLane:0,gesture:null,action:l,hasEagerState:!1,eagerState:null,next:null},fi(t)?xr(e,l):(l=Oc(t,e,l,n),l!==null&&(Ae(l,t,n),Ur(l,e,n)))}function Nr(t,e,l){var n=Ue();La(t,e,l,n)}function La(t,e,l,n){var a={lane:n,revertLane:0,gesture:null,action:l,hasEagerState:!1,eagerState:null,next:null};if(fi(t))xr(e,a);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var f=e.lastRenderedState,r=u(f,l);if(a.hasEagerState=!0,a.eagerState=r,Ce(r,f))return Qu(t,e,a,0),Dt===null&&Xu(),!1}catch{}finally{}if(l=Oc(t,e,a,n),l!==null)return Ae(l,t,n),Ur(l,e,n),!0}return!1}function df(t,e,l,n){if(n={lane:2,revertLane:Zf(),gesture:null,action:n,hasEagerState:!1,eagerState:null,next:null},fi(t)){if(e)throw Error(s(479))}else e=Oc(t,l,n,2),e!==null&&Ae(e,t,2)}function fi(t){var e=t.alternate;return t===ut||e!==null&&e===ut}function xr(t,e){Pn=ei=!0;var l=t.pending;l===null?e.next=e:(e.next=l.next,l.next=e),t.pending=e}function Ur(t,e,l){if((l&4194048)!==0){var n=e.lanes;n&=t.pendingLanes,l|=n,e.lanes=l,Me(t,l)}}var Za={readContext:ne,use:ai,useCallback:Ut,useContext:Ut,useEffect:Ut,useImperativeHandle:Ut,useLayoutEffect:Ut,useInsertionEffect:Ut,useMemo:Ut,useReducer:Ut,useRef:Ut,useState:Ut,useDebugValue:Ut,useDeferredValue:Ut,useTransition:Ut,useSyncExternalStore:Ut,useId:Ut,useHostTransitionStatus:Ut,useFormState:Ut,useActionState:Ut,useOptimistic:Ut,useMemoCache:Ut,useCacheRefresh:Ut};Za.useEffectEvent=Ut;var Hr={readContext:ne,use:ai,useCallback:function(t,e){return de().memoizedState=[t,e===void 0?null:e],t},useContext:ne,useEffect:gr,useImperativeHandle:function(t,e,l){l=l!=null?l.concat([t]):null,ii(4194308,4,Er.bind(null,e,t),l)},useLayoutEffect:function(t,e){return ii(4194308,4,t,e)},useInsertionEffect:function(t,e){ii(4,2,t,e)},useMemo:function(t,e){var l=de();e=e===void 0?null:e;var n=t();if(zn){qe(!0);try{t()}finally{qe(!1)}}return l.memoizedState=[n,e],n},useReducer:function(t,e,l){var n=de();if(l!==void 0){var a=l(e);if(zn){qe(!0);try{l(e)}finally{qe(!1)}}}else a=e;return n.memoizedState=n.baseState=a,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:a},n.queue=t,t=t.dispatch=wm.bind(null,ut,t),[n.memoizedState,t]},useRef:function(t){var e=de();return t={current:t},e.memoizedState=t},useState:function(t){t=af(t);var e=t.queue,l=Nr.bind(null,ut,e);return e.dispatch=l,[t.memoizedState,l]},useDebugValue:ff,useDeferredValue:function(t,e){var l=de();return of(l,t,e)},useTransition:function(){var t=af(!1);return t=Mr.bind(null,ut,t.queue,!0,!1),de().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,l){var n=ut,a=de();if(dt){if(l===void 0)throw Error(s(407));l=l()}else{if(l=e(),Dt===null)throw Error(s(349));(st&127)!==0||er(n,e,l)}a.memoizedState=l;var u={value:l,getSnapshot:e};return a.queue=u,gr(nr.bind(null,n,u,t),[t]),n.flags|=2048,ea(9,{destroy:void 0},lr.bind(null,n,u,l,e),null),l},useId:function(){var t=de(),e=Dt.identifierPrefix;if(dt){var l=cl,n=il;l=(n&~(1<<32-ce(n)-1)).toString(32)+l,e="_"+e+"R_"+l,l=li++,0<l&&(e+="H"+l.toString(32)),e+="_"}else l=Rm++,e="_"+e+"r_"+l.toString(32)+"_";return t.memoizedState=e},useHostTransitionStatus:rf,useFormState:dr,useActionState:dr,useOptimistic:function(t){var e=de();e.memoizedState=e.baseState=t;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=l,e=df.bind(null,ut,!0,l),l.dispatch=e,[t,e]},useMemoCache:ef,useCacheRefresh:function(){return de().memoizedState=Bm.bind(null,ut)},useEffectEvent:function(t){var e=de(),l={impl:t};return e.memoizedState=l,function(){if((St&2)!==0)throw Error(s(440));return l.impl.apply(void 0,arguments)}}},hf={readContext:ne,use:ai,useCallback:zr,useContext:ne,useEffect:cf,useImperativeHandle:Tr,useInsertionEffect:Sr,useLayoutEffect:pr,useMemo:Ar,useReducer:ui,useRef:yr,useState:function(){return ui(El)},useDebugValue:ff,useDeferredValue:function(t,e){var l=jt();return Dr(l,Tt.memoizedState,t,e)},useTransition:function(){var t=ui(El)[0],e=jt().memoizedState;return[typeof t=="boolean"?t:Qa(t),e]},useSyncExternalStore:tr,useId:_r,useHostTransitionStatus:rf,useFormState:hr,useActionState:hr,useOptimistic:function(t,e){var l=jt();return ir(l,Tt,t,e)},useMemoCache:ef,useCacheRefresh:Rr};hf.useEffectEvent=br;var qr={readContext:ne,use:ai,useCallback:zr,useContext:ne,useEffect:cf,useImperativeHandle:Tr,useInsertionEffect:Sr,useLayoutEffect:pr,useMemo:Ar,useReducer:nf,useRef:yr,useState:function(){return nf(El)},useDebugValue:ff,useDeferredValue:function(t,e){var l=jt();return Tt===null?of(l,t,e):Dr(l,Tt.memoizedState,t,e)},useTransition:function(){var t=nf(El)[0],e=jt().memoizedState;return[typeof t=="boolean"?t:Qa(t),e]},useSyncExternalStore:tr,useId:_r,useHostTransitionStatus:rf,useFormState:mr,useActionState:mr,useOptimistic:function(t,e){var l=jt();return Tt!==null?ir(l,Tt,t,e):(l.baseState=t,[t,l.queue.dispatch])},useMemoCache:ef,useCacheRefresh:Rr};qr.useEffectEvent=br;function vf(t,e,l,n){e=t.memoizedState,l=l(n,e),l=l==null?e:M({},e,l),t.memoizedState=l,t.lanes===0&&(t.updateQueue.baseState=l)}var mf={enqueueSetState:function(t,e,l){t=t._reactInternals;var n=Ue(),a=Ql(n);a.payload=e,l!=null&&(a.callback=l),e=Ll(t,a,n),e!==null&&(Ae(e,t,n),ja(e,t,n))},enqueueReplaceState:function(t,e,l){t=t._reactInternals;var n=Ue(),a=Ql(n);a.tag=1,a.payload=e,l!=null&&(a.callback=l),e=Ll(t,a,n),e!==null&&(Ae(e,t,n),ja(e,t,n))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var l=Ue(),n=Ql(l);n.tag=2,e!=null&&(n.callback=e),e=Ll(t,n,l),e!==null&&(Ae(e,t,l),ja(e,t,l))}};function Br(t,e,l,n,a,u,f){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(n,u,f):e.prototype&&e.prototype.isPureReactComponent?!Ra(l,n)||!Ra(a,u):!0}function wr(t,e,l,n){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(l,n),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(l,n),e.state!==t&&mf.enqueueReplaceState(e,e.state,null)}function An(t,e){var l=e;if("ref"in e){l={};for(var n in e)n!=="ref"&&(l[n]=e[n])}if(t=t.defaultProps){l===e&&(l=M({},l));for(var a in t)l[a]===void 0&&(l[a]=t[a])}return l}function jr(t){Gu(t)}function Yr(t){console.error(t)}function Gr(t){Gu(t)}function oi(t,e){try{var l=t.onUncaughtError;l(e.value,{componentStack:e.stack})}catch(n){setTimeout(function(){throw n})}}function Xr(t,e,l){try{var n=t.onCaughtError;n(l.value,{componentStack:l.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(a){setTimeout(function(){throw a})}}function yf(t,e,l){return l=Ql(l),l.tag=3,l.payload={element:null},l.callback=function(){oi(t,e)},l}function Qr(t){return t=Ql(t),t.tag=3,t}function Lr(t,e,l,n){var a=l.type.getDerivedStateFromError;if(typeof a=="function"){var u=n.value;t.payload=function(){return a(u)},t.callback=function(){Xr(e,l,n)}}var f=l.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(t.callback=function(){Xr(e,l,n),typeof a!="function"&&($l===null?$l=new Set([this]):$l.add(this));var r=n.stack;this.componentDidCatch(n.value,{componentStack:r!==null?r:""})})}function jm(t,e,l,n,a){if(l.flags|=32768,n!==null&&typeof n=="object"&&typeof n.then=="function"){if(e=l.alternate,e!==null&&Jn(e,l,a,!0),l=_e.current,l!==null){switch(l.tag){case 31:case 13:return Ke===null?Ei():l.alternate===null&&Ht===0&&(Ht=3),l.flags&=-257,l.flags|=65536,l.lanes=a,n===Wu?l.flags|=16384:(e=l.updateQueue,e===null?l.updateQueue=new Set([n]):e.add(n),Xf(t,n,a)),!1;case 22:return l.flags|=65536,n===Wu?l.flags|=16384:(e=l.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([n])},l.updateQueue=e):(l=e.retryQueue,l===null?e.retryQueue=new Set([n]):l.add(n)),Xf(t,n,a)),!1}throw Error(s(435,l.tag))}return Xf(t,n,a),Ei(),!1}if(dt)return e=_e.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=a,n!==Hc&&(t=Error(s(422),{cause:n}),Ua(Qe(t,l)))):(n!==Hc&&(e=Error(s(423),{cause:n}),Ua(Qe(e,l))),t=t.current.alternate,t.flags|=65536,a&=-a,t.lanes|=a,n=Qe(n,l),a=yf(t.stateNode,n,a),Vc(t,a),Ht!==4&&(Ht=2)),!1;var u=Error(s(520),{cause:n});if(u=Qe(u,l),Ia===null?Ia=[u]:Ia.push(u),Ht!==4&&(Ht=2),e===null)return!0;n=Qe(n,l),l=e;do{switch(l.tag){case 3:return l.flags|=65536,t=a&-a,l.lanes|=t,t=yf(l.stateNode,n,t),Vc(l,t),!1;case 1:if(e=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&($l===null||!$l.has(u))))return l.flags|=65536,a&=-a,l.lanes|=a,a=Qr(a),Lr(a,t,l,n),Vc(l,a),!1}l=l.return}while(l!==null);return!1}var gf=Error(s(461)),Qt=!1;function ae(t,e,l,n){e.child=t===null?Js(e,null,l,n):Tn(e,t.child,l,n)}function Zr(t,e,l,n,a){l=l.render;var u=e.ref;if("ref"in n){var f={};for(var r in n)r!=="ref"&&(f[r]=n[r])}else f=n;return bn(e),n=Fc(t,e,l,f,u,a),r=Ic(),t!==null&&!Qt?(Pc(t,e,a),Tl(t,e,a)):(dt&&r&&xc(e),e.flags|=1,ae(t,e,n,a),e.child)}function Vr(t,e,l,n,a){if(t===null){var u=l.type;return typeof u=="function"&&!_c(u)&&u.defaultProps===void 0&&l.compare===null?(e.tag=15,e.type=u,Kr(t,e,u,n,a)):(t=Zu(l.type,null,n,e,e.mode,a),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!Df(t,a)){var f=u.memoizedProps;if(l=l.compare,l=l!==null?l:Ra,l(f,n)&&t.ref===e.ref)return Tl(t,e,a)}return e.flags|=1,t=yl(u,n),t.ref=e.ref,t.return=e,e.child=t}function Kr(t,e,l,n,a){if(t!==null){var u=t.memoizedProps;if(Ra(u,n)&&t.ref===e.ref)if(Qt=!1,e.pendingProps=n=u,Df(t,a))(t.flags&131072)!==0&&(Qt=!0);else return e.lanes=t.lanes,Tl(t,e,a)}return bf(t,e,l,n,a)}function Jr(t,e,l,n){var a=n.children,u=t!==null?t.memoizedState:null;if(t===null&&e.stateNode===null&&(e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null}),n.mode==="hidden"){if((e.flags&128)!==0){if(u=u!==null?u.baseLanes|l:l,t!==null){for(n=e.child=t.child,a=0;n!==null;)a=a|n.lanes|n.childLanes,n=n.sibling;n=a&~u}else n=0,e.child=null;return kr(t,e,u,l,n)}if((l&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&ku(e,u!==null?u.cachePool:null),u!==null?Ws(e,u):Jc(),Fs(e);else return n=e.lanes=536870912,kr(t,e,u!==null?u.baseLanes|l:l,l,n)}else u!==null?(ku(e,u.cachePool),Ws(e,u),Vl(),e.memoizedState=null):(t!==null&&ku(e,null),Jc(),Vl());return ae(t,e,a,l),e.child}function Va(t,e){return t!==null&&t.tag===22||e.stateNode!==null||(e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null}),e.sibling}function kr(t,e,l,n,a){var u=Xc();return u=u===null?null:{parent:Gt._currentValue,pool:u},e.memoizedState={baseLanes:l,cachePool:u},t!==null&&ku(e,null),Jc(),Fs(e),t!==null&&Jn(t,e,n,!0),e.childLanes=a,null}function si(t,e){return e=di({mode:e.mode,children:e.children},t.mode),e.ref=t.ref,t.child=e,e.return=t,e}function $r(t,e,l){return Tn(e,t.child,null,l),t=si(e,e.pendingProps),t.flags|=2,Re(e),e.memoizedState=null,t}function Ym(t,e,l){var n=e.pendingProps,a=(e.flags&128)!==0;if(e.flags&=-129,t===null){if(dt){if(n.mode==="hidden")return t=si(e,n),e.lanes=536870912,Va(null,t);if($c(e),(t=Ct)?(t=ch(t,Ve),t=t!==null&&t.data==="&"?t:null,t!==null&&(e.memoizedState={dehydrated:t,treeContext:wl!==null?{id:il,overflow:cl}:null,retryLane:536870912,hydrationErrors:null},l=xs(t),l.return=e,e.child=l,le=e,Ct=null)):t=null,t===null)throw Yl(e);return e.lanes=536870912,null}return si(e,n)}var u=t.memoizedState;if(u!==null){var f=u.dehydrated;if($c(e),a)if(e.flags&256)e.flags&=-257,e=$r(t,e,l);else if(e.memoizedState!==null)e.child=t.child,e.flags|=128,e=null;else throw Error(s(558));else if(Qt||Jn(t,e,l,!1),a=(l&t.childLanes)!==0,Qt||a){if(n=Dt,n!==null&&(f=we(n,l),f!==0&&f!==u.retryLane))throw u.retryLane=f,vn(t,f),Ae(n,t,f),gf;Ei(),e=$r(t,e,l)}else t=u.treeContext,Ct=Je(f.nextSibling),le=e,dt=!0,jl=null,Ve=!1,t!==null&&qs(e,t),e=si(e,n),e.flags|=4096;return e}return t=yl(t.child,{mode:n.mode,children:n.children}),t.ref=e.ref,e.child=t,t.return=e,t}function ri(t,e){var l=e.ref;if(l===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(s(284));(t===null||t.ref!==l)&&(e.flags|=4194816)}}function bf(t,e,l,n,a){return bn(e),l=Fc(t,e,l,n,void 0,a),n=Ic(),t!==null&&!Qt?(Pc(t,e,a),Tl(t,e,a)):(dt&&n&&xc(e),e.flags|=1,ae(t,e,l,a),e.child)}function Wr(t,e,l,n,a,u){return bn(e),e.updateQueue=null,l=Ps(e,n,l,a),Is(t),n=Ic(),t!==null&&!Qt?(Pc(t,e,u),Tl(t,e,u)):(dt&&n&&xc(e),e.flags|=1,ae(t,e,l,u),e.child)}function Fr(t,e,l,n,a){if(bn(e),e.stateNode===null){var u=Ln,f=l.contextType;typeof f=="object"&&f!==null&&(u=ne(f)),u=new l(n,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=mf,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=n,u.state=e.memoizedState,u.refs={},Lc(e),f=l.contextType,u.context=typeof f=="object"&&f!==null?ne(f):Ln,u.state=e.memoizedState,f=l.getDerivedStateFromProps,typeof f=="function"&&(vf(e,l,f,n),u.state=e.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(f=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),f!==u.state&&mf.enqueueReplaceState(u,u.state,null),Ga(e,n,u,a),Ya(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),n=!0}else if(t===null){u=e.stateNode;var r=e.memoizedProps,v=An(l,r);u.props=v;var z=u.context,O=l.contextType;f=Ln,typeof O=="object"&&O!==null&&(f=ne(O));var U=l.getDerivedStateFromProps;O=typeof U=="function"||typeof u.getSnapshotBeforeUpdate=="function",r=e.pendingProps!==r,O||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(r||z!==f)&&wr(e,u,n,f),Xl=!1;var A=e.memoizedState;u.state=A,Ga(e,n,u,a),Ya(),z=e.memoizedState,r||A!==z||Xl?(typeof U=="function"&&(vf(e,l,U,n),z=e.memoizedState),(v=Xl||Br(e,l,v,n,A,z,f))?(O||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=n,e.memoizedState=z),u.props=n,u.state=z,u.context=f,n=v):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),n=!1)}else{u=e.stateNode,Zc(t,e),f=e.memoizedProps,O=An(l,f),u.props=O,U=e.pendingProps,A=u.context,z=l.contextType,v=Ln,typeof z=="object"&&z!==null&&(v=ne(z)),r=l.getDerivedStateFromProps,(z=typeof r=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f!==U||A!==v)&&wr(e,u,n,v),Xl=!1,A=e.memoizedState,u.state=A,Ga(e,n,u,a),Ya();var D=e.memoizedState;f!==U||A!==D||Xl||t!==null&&t.dependencies!==null&&Ku(t.dependencies)?(typeof r=="function"&&(vf(e,l,r,n),D=e.memoizedState),(O=Xl||Br(e,l,O,n,A,D,v)||t!==null&&t.dependencies!==null&&Ku(t.dependencies))?(z||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(n,D,v),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(n,D,v)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||f===t.memoizedProps&&A===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&A===t.memoizedState||(e.flags|=1024),e.memoizedProps=n,e.memoizedState=D),u.props=n,u.state=D,u.context=v,n=O):(typeof u.componentDidUpdate!="function"||f===t.memoizedProps&&A===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===t.memoizedProps&&A===t.memoizedState||(e.flags|=1024),n=!1)}return u=n,ri(t,e),n=(e.flags&128)!==0,u||n?(u=e.stateNode,l=n&&typeof l.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&n?(e.child=Tn(e,t.child,null,a),e.child=Tn(e,null,l,a)):ae(t,e,l,a),e.memoizedState=u.state,t=e.child):t=Tl(t,e,a),t}function Ir(t,e,l,n){return yn(),e.flags|=256,ae(t,e,l,n),e.child}var Sf={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function pf(t){return{baseLanes:t,cachePool:Xs()}}function Ef(t,e,l){return t=t!==null?t.childLanes&~l:0,e&&(t|=xe),t}function Pr(t,e,l){var n=e.pendingProps,a=!1,u=(e.flags&128)!==0,f;if((f=u)||(f=t!==null&&t.memoizedState===null?!1:(wt.current&2)!==0),f&&(a=!0,e.flags&=-129),f=(e.flags&32)!==0,e.flags&=-33,t===null){if(dt){if(a?Zl(e):Vl(),(t=Ct)?(t=ch(t,Ve),t=t!==null&&t.data!=="&"?t:null,t!==null&&(e.memoizedState={dehydrated:t,treeContext:wl!==null?{id:il,overflow:cl}:null,retryLane:536870912,hydrationErrors:null},l=xs(t),l.return=e,e.child=l,le=e,Ct=null)):t=null,t===null)throw Yl(e);return no(t)?e.lanes=32:e.lanes=536870912,null}var r=n.children;return n=n.fallback,a?(Vl(),a=e.mode,r=di({mode:"hidden",children:r},a),n=mn(n,a,l,null),r.return=e,n.return=e,r.sibling=n,e.child=r,n=e.child,n.memoizedState=pf(l),n.childLanes=Ef(t,f,l),e.memoizedState=Sf,Va(null,n)):(Zl(e),Tf(e,r))}var v=t.memoizedState;if(v!==null&&(r=v.dehydrated,r!==null)){if(u)e.flags&256?(Zl(e),e.flags&=-257,e=zf(t,e,l)):e.memoizedState!==null?(Vl(),e.child=t.child,e.flags|=128,e=null):(Vl(),r=n.fallback,a=e.mode,n=di({mode:"visible",children:n.children},a),r=mn(r,a,l,null),r.flags|=2,n.return=e,r.return=e,n.sibling=r,e.child=n,Tn(e,t.child,null,l),n=e.child,n.memoizedState=pf(l),n.childLanes=Ef(t,f,l),e.memoizedState=Sf,e=Va(null,n));else if(Zl(e),no(r)){if(f=r.nextSibling&&r.nextSibling.dataset,f)var z=f.dgst;f=z,n=Error(s(419)),n.stack="",n.digest=f,Ua({value:n,source:null,stack:null}),e=zf(t,e,l)}else if(Qt||Jn(t,e,l,!1),f=(l&t.childLanes)!==0,Qt||f){if(f=Dt,f!==null&&(n=we(f,l),n!==0&&n!==v.retryLane))throw v.retryLane=n,vn(t,n),Ae(f,t,n),gf;lo(r)||Ei(),e=zf(t,e,l)}else lo(r)?(e.flags|=192,e.child=t.child,e=null):(t=v.treeContext,Ct=Je(r.nextSibling),le=e,dt=!0,jl=null,Ve=!1,t!==null&&qs(e,t),e=Tf(e,n.children),e.flags|=4096);return e}return a?(Vl(),r=n.fallback,a=e.mode,v=t.child,z=v.sibling,n=yl(v,{mode:"hidden",children:n.children}),n.subtreeFlags=v.subtreeFlags&65011712,z!==null?r=yl(z,r):(r=mn(r,a,l,null),r.flags|=2),r.return=e,n.return=e,n.sibling=r,e.child=n,Va(null,n),n=e.child,r=t.child.memoizedState,r===null?r=pf(l):(a=r.cachePool,a!==null?(v=Gt._currentValue,a=a.parent!==v?{parent:v,pool:v}:a):a=Xs(),r={baseLanes:r.baseLanes|l,cachePool:a}),n.memoizedState=r,n.childLanes=Ef(t,f,l),e.memoizedState=Sf,Va(t.child,n)):(Zl(e),l=t.child,t=l.sibling,l=yl(l,{mode:"visible",children:n.children}),l.return=e,l.sibling=null,t!==null&&(f=e.deletions,f===null?(e.deletions=[t],e.flags|=16):f.push(t)),e.child=l,e.memoizedState=null,l)}function Tf(t,e){return e=di({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function di(t,e){return t=Oe(22,t,null,e),t.lanes=0,t}function zf(t,e,l){return Tn(e,t.child,null,l),t=Tf(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function td(t,e,l){t.lanes|=e;var n=t.alternate;n!==null&&(n.lanes|=e),wc(t.return,e,l)}function Af(t,e,l,n,a,u){var f=t.memoizedState;f===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:n,tail:l,tailMode:a,treeForkCount:u}:(f.isBackwards=e,f.rendering=null,f.renderingStartTime=0,f.last=n,f.tail=l,f.tailMode=a,f.treeForkCount=u)}function ed(t,e,l){var n=e.pendingProps,a=n.revealOrder,u=n.tail;n=n.children;var f=wt.current,r=(f&2)!==0;if(r?(f=f&1|2,e.flags|=128):f&=1,Y(wt,f),ae(t,e,n,l),n=dt?xa:0,!r&&t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&td(t,l,e);else if(t.tag===19)td(t,l,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}switch(a){case"forwards":for(l=e.child,a=null;l!==null;)t=l.alternate,t!==null&&ti(t)===null&&(a=l),l=l.sibling;l=a,l===null?(a=e.child,e.child=null):(a=l.sibling,l.sibling=null),Af(e,!1,a,l,u,n);break;case"backwards":case"unstable_legacy-backwards":for(l=null,a=e.child,e.child=null;a!==null;){if(t=a.alternate,t!==null&&ti(t)===null){e.child=a;break}t=a.sibling,a.sibling=l,l=a,a=t}Af(e,!0,l,null,u,n);break;case"together":Af(e,!1,null,null,void 0,n);break;default:e.memoizedState=null}return e.child}function Tl(t,e,l){if(t!==null&&(e.dependencies=t.dependencies),kl|=e.lanes,(l&e.childLanes)===0)if(t!==null){if(Jn(t,e,l,!1),(l&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(s(153));if(e.child!==null){for(t=e.child,l=yl(t,t.pendingProps),e.child=l,l.return=e;t.sibling!==null;)t=t.sibling,l=l.sibling=yl(t,t.pendingProps),l.return=e;l.sibling=null}return e.child}function Df(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&Ku(t)))}function Gm(t,e,l){switch(e.tag){case 3:kt(e,e.stateNode.containerInfo),Gl(e,Gt,t.memoizedState.cache),yn();break;case 27:case 5:te(e);break;case 4:kt(e,e.stateNode.containerInfo);break;case 10:Gl(e,e.type,e.memoizedProps.value);break;case 31:if(e.memoizedState!==null)return e.flags|=128,$c(e),null;break;case 13:var n=e.memoizedState;if(n!==null)return n.dehydrated!==null?(Zl(e),e.flags|=128,null):(l&e.child.childLanes)!==0?Pr(t,e,l):(Zl(e),t=Tl(t,e,l),t!==null?t.sibling:null);Zl(e);break;case 19:var a=(t.flags&128)!==0;if(n=(l&e.childLanes)!==0,n||(Jn(t,e,l,!1),n=(l&e.childLanes)!==0),a){if(n)return ed(t,e,l);e.flags|=128}if(a=e.memoizedState,a!==null&&(a.rendering=null,a.tail=null,a.lastEffect=null),Y(wt,wt.current),n)break;return null;case 22:return e.lanes=0,Jr(t,e,l,e.pendingProps);case 24:Gl(e,Gt,t.memoizedState.cache)}return Tl(t,e,l)}function ld(t,e,l){if(t!==null)if(t.memoizedProps!==e.pendingProps)Qt=!0;else{if(!Df(t,l)&&(e.flags&128)===0)return Qt=!1,Gm(t,e,l);Qt=(t.flags&131072)!==0}else Qt=!1,dt&&(e.flags&1048576)!==0&&Hs(e,xa,e.index);switch(e.lanes=0,e.tag){case 16:t:{var n=e.pendingProps;if(t=pn(e.elementType),e.type=t,typeof t=="function")_c(t)?(n=An(t,n),e.tag=1,e=Fr(null,e,t,n,l)):(e.tag=0,e=bf(null,e,t,n,l));else{if(t!=null){var a=t.$$typeof;if(a===P){e.tag=11,e=Zr(null,e,t,n,l);break t}else if(a===G){e.tag=14,e=Vr(null,e,t,n,l);break t}}throw e=Jt(t)||t,Error(s(306,e,""))}}return e;case 0:return bf(t,e,e.type,e.pendingProps,l);case 1:return n=e.type,a=An(n,e.pendingProps),Fr(t,e,n,a,l);case 3:t:{if(kt(e,e.stateNode.containerInfo),t===null)throw Error(s(387));n=e.pendingProps;var u=e.memoizedState;a=u.element,Zc(t,e),Ga(e,n,null,l);var f=e.memoizedState;if(n=f.cache,Gl(e,Gt,n),n!==u.cache&&jc(e,[Gt],l,!0),Ya(),n=f.element,u.isDehydrated)if(u={element:n,isDehydrated:!1,cache:f.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=Ir(t,e,n,l);break t}else if(n!==a){a=Qe(Error(s(424)),e),Ua(a),e=Ir(t,e,n,l);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Ct=Je(t.firstChild),le=e,dt=!0,jl=null,Ve=!0,l=Js(e,null,n,l),e.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(yn(),n===a){e=Tl(t,e,l);break t}ae(t,e,n,l)}e=e.child}return e;case 26:return ri(t,e),t===null?(l=hh(e.type,null,e.pendingProps,null))?e.memoizedState=l:dt||(l=e.type,t=e.pendingProps,n=Oi(lt.current).createElement(l),n[ee]=e,n[be]=t,ue(n,l,t),Wt(n),e.stateNode=n):e.memoizedState=hh(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return te(e),t===null&&dt&&(n=e.stateNode=sh(e.type,e.pendingProps,lt.current),le=e,Ve=!0,a=Ct,Pl(e.type)?(ao=a,Ct=Je(n.firstChild)):Ct=a),ae(t,e,e.pendingProps.children,l),ri(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&dt&&((a=n=Ct)&&(n=yy(n,e.type,e.pendingProps,Ve),n!==null?(e.stateNode=n,le=e,Ct=Je(n.firstChild),Ve=!1,a=!0):a=!1),a||Yl(e)),te(e),a=e.type,u=e.pendingProps,f=t!==null?t.memoizedProps:null,n=u.children,Pf(a,u)?n=null:f!==null&&Pf(a,f)&&(e.flags|=32),e.memoizedState!==null&&(a=Fc(t,e,Nm,null,null,l),iu._currentValue=a),ri(t,e),ae(t,e,n,l),e.child;case 6:return t===null&&dt&&((t=l=Ct)&&(l=gy(l,e.pendingProps,Ve),l!==null?(e.stateNode=l,le=e,Ct=null,t=!0):t=!1),t||Yl(e)),null;case 13:return Pr(t,e,l);case 4:return kt(e,e.stateNode.containerInfo),n=e.pendingProps,t===null?e.child=Tn(e,null,n,l):ae(t,e,n,l),e.child;case 11:return Zr(t,e,e.type,e.pendingProps,l);case 7:return ae(t,e,e.pendingProps,l),e.child;case 8:return ae(t,e,e.pendingProps.children,l),e.child;case 12:return ae(t,e,e.pendingProps.children,l),e.child;case 10:return n=e.pendingProps,Gl(e,e.type,n.value),ae(t,e,n.children,l),e.child;case 9:return a=e.type._context,n=e.pendingProps.children,bn(e),a=ne(a),n=n(a),e.flags|=1,ae(t,e,n,l),e.child;case 14:return Vr(t,e,e.type,e.pendingProps,l);case 15:return Kr(t,e,e.type,e.pendingProps,l);case 19:return ed(t,e,l);case 31:return Ym(t,e,l);case 22:return Jr(t,e,l,e.pendingProps);case 24:return bn(e),n=ne(Gt),t===null?(a=Xc(),a===null&&(a=Dt,u=Yc(),a.pooledCache=u,u.refCount++,u!==null&&(a.pooledCacheLanes|=l),a=u),e.memoizedState={parent:n,cache:a},Lc(e),Gl(e,Gt,a)):((t.lanes&l)!==0&&(Zc(t,e),Ga(e,null,null,l),Ya()),a=t.memoizedState,u=e.memoizedState,a.parent!==n?(a={parent:n,cache:n},e.memoizedState=a,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=a),Gl(e,Gt,n)):(n=u.cache,Gl(e,Gt,n),n!==a.cache&&jc(e,[Gt],l,!0))),ae(t,e,e.pendingProps.children,l),e.child;case 29:throw e.pendingProps}throw Error(s(156,e.tag))}function zl(t){t.flags|=4}function Mf(t,e,l,n,a){if((e=(t.mode&32)!==0)&&(e=!1),e){if(t.flags|=16777216,(a&335544128)===a)if(t.stateNode.complete)t.flags|=8192;else if(_d())t.flags|=8192;else throw En=Wu,Qc}else t.flags&=-16777217}function nd(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!bh(e))if(_d())t.flags|=8192;else throw En=Wu,Qc}function hi(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?fe():536870912,t.lanes|=e,ua|=e)}function Ka(t,e){if(!dt)switch(t.tailMode){case"hidden":e=t.tail;for(var l=null;e!==null;)e.alternate!==null&&(l=e),e=e.sibling;l===null?t.tail=null:l.sibling=null;break;case"collapsed":l=t.tail;for(var n=null;l!==null;)l.alternate!==null&&(n=l),l=l.sibling;n===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:n.sibling=null}}function Ot(t){var e=t.alternate!==null&&t.alternate.child===t.child,l=0,n=0;if(e)for(var a=t.child;a!==null;)l|=a.lanes|a.childLanes,n|=a.subtreeFlags&65011712,n|=a.flags&65011712,a.return=t,a=a.sibling;else for(a=t.child;a!==null;)l|=a.lanes|a.childLanes,n|=a.subtreeFlags,n|=a.flags,a.return=t,a=a.sibling;return t.subtreeFlags|=n,t.childLanes=l,e}function Xm(t,e,l){var n=e.pendingProps;switch(Uc(e),e.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ot(e),null;case 1:return Ot(e),null;case 3:return l=e.stateNode,n=null,t!==null&&(n=t.memoizedState.cache),e.memoizedState.cache!==n&&(e.flags|=2048),Sl(Gt),Nt(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(t===null||t.child===null)&&(Kn(e)?zl(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,qc())),Ot(e),null;case 26:var a=e.type,u=e.memoizedState;return t===null?(zl(e),u!==null?(Ot(e),nd(e,u)):(Ot(e),Mf(e,a,null,n,l))):u?u!==t.memoizedState?(zl(e),Ot(e),nd(e,u)):(Ot(e),e.flags&=-16777217):(t=t.memoizedProps,t!==n&&zl(e),Ot(e),Mf(e,a,t,n,l)),null;case 27:if(xl(e),l=lt.current,a=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==n&&zl(e);else{if(!n){if(e.stateNode===null)throw Error(s(166));return Ot(e),null}t=Q.current,Kn(e)?Bs(e):(t=sh(a,n,l),e.stateNode=t,zl(e))}return Ot(e),null;case 5:if(xl(e),a=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==n&&zl(e);else{if(!n){if(e.stateNode===null)throw Error(s(166));return Ot(e),null}if(u=Q.current,Kn(e))Bs(e);else{var f=Oi(lt.current);switch(u){case 1:u=f.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:u=f.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":u=f.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":u=f.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":u=f.createElement("div"),u.innerHTML="<script><\/script>",u=u.removeChild(u.firstChild);break;case"select":u=typeof n.is=="string"?f.createElement("select",{is:n.is}):f.createElement("select"),n.multiple?u.multiple=!0:n.size&&(u.size=n.size);break;default:u=typeof n.is=="string"?f.createElement(a,{is:n.is}):f.createElement(a)}}u[ee]=e,u[be]=n;t:for(f=e.child;f!==null;){if(f.tag===5||f.tag===6)u.appendChild(f.stateNode);else if(f.tag!==4&&f.tag!==27&&f.child!==null){f.child.return=f,f=f.child;continue}if(f===e)break t;for(;f.sibling===null;){if(f.return===null||f.return===e)break t;f=f.return}f.sibling.return=f.return,f=f.sibling}e.stateNode=u;t:switch(ue(u,a,n),a){case"button":case"input":case"select":case"textarea":n=!!n.autoFocus;break t;case"img":n=!0;break t;default:n=!1}n&&zl(e)}}return Ot(e),Mf(e,e.type,t===null?null:t.memoizedProps,e.pendingProps,l),null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==n&&zl(e);else{if(typeof n!="string"&&e.stateNode===null)throw Error(s(166));if(t=lt.current,Kn(e)){if(t=e.stateNode,l=e.memoizedProps,n=null,a=le,a!==null)switch(a.tag){case 27:case 5:n=a.memoizedProps}t[ee]=e,t=!!(t.nodeValue===l||n!==null&&n.suppressHydrationWarning===!0||Pd(t.nodeValue,l)),t||Yl(e,!0)}else t=Oi(t).createTextNode(n),t[ee]=e,e.stateNode=t}return Ot(e),null;case 31:if(l=e.memoizedState,t===null||t.memoizedState!==null){if(n=Kn(e),l!==null){if(t===null){if(!n)throw Error(s(318));if(t=e.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(s(557));t[ee]=e}else yn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ot(e),t=!1}else l=qc(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=l),t=!0;if(!t)return e.flags&256?(Re(e),e):(Re(e),null);if((e.flags&128)!==0)throw Error(s(558))}return Ot(e),null;case 13:if(n=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(a=Kn(e),n!==null&&n.dehydrated!==null){if(t===null){if(!a)throw Error(s(318));if(a=e.memoizedState,a=a!==null?a.dehydrated:null,!a)throw Error(s(317));a[ee]=e}else yn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Ot(e),a=!1}else a=qc(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=a),a=!0;if(!a)return e.flags&256?(Re(e),e):(Re(e),null)}return Re(e),(e.flags&128)!==0?(e.lanes=l,e):(l=n!==null,t=t!==null&&t.memoizedState!==null,l&&(n=e.child,a=null,n.alternate!==null&&n.alternate.memoizedState!==null&&n.alternate.memoizedState.cachePool!==null&&(a=n.alternate.memoizedState.cachePool.pool),u=null,n.memoizedState!==null&&n.memoizedState.cachePool!==null&&(u=n.memoizedState.cachePool.pool),u!==a&&(n.flags|=2048)),l!==t&&l&&(e.child.flags|=8192),hi(e,e.updateQueue),Ot(e),null);case 4:return Nt(),t===null&&kf(e.stateNode.containerInfo),Ot(e),null;case 10:return Sl(e.type),Ot(e),null;case 19:if(N(wt),n=e.memoizedState,n===null)return Ot(e),null;if(a=(e.flags&128)!==0,u=n.rendering,u===null)if(a)Ka(n,!1);else{if(Ht!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=ti(t),u!==null){for(e.flags|=128,Ka(n,!1),t=u.updateQueue,e.updateQueue=t,hi(e,t),e.subtreeFlags=0,t=l,l=e.child;l!==null;)Ns(l,t),l=l.sibling;return Y(wt,wt.current&1|2),dt&&gl(e,n.treeForkCount),e.child}t=t.sibling}n.tail!==null&&ie()>bi&&(e.flags|=128,a=!0,Ka(n,!1),e.lanes=4194304)}else{if(!a)if(t=ti(u),t!==null){if(e.flags|=128,a=!0,t=t.updateQueue,e.updateQueue=t,hi(e,t),Ka(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!dt)return Ot(e),null}else 2*ie()-n.renderingStartTime>bi&&l!==536870912&&(e.flags|=128,a=!0,Ka(n,!1),e.lanes=4194304);n.isBackwards?(u.sibling=e.child,e.child=u):(t=n.last,t!==null?t.sibling=u:e.child=u,n.last=u)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=ie(),t.sibling=null,l=wt.current,Y(wt,a?l&1|2:l&1),dt&&gl(e,n.treeForkCount),t):(Ot(e),null);case 22:case 23:return Re(e),kc(),n=e.memoizedState!==null,t!==null?t.memoizedState!==null!==n&&(e.flags|=8192):n&&(e.flags|=8192),n?(l&536870912)!==0&&(e.flags&128)===0&&(Ot(e),e.subtreeFlags&6&&(e.flags|=8192)):Ot(e),l=e.updateQueue,l!==null&&hi(e,l.retryQueue),l=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),n=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(n=e.memoizedState.cachePool.pool),n!==l&&(e.flags|=2048),t!==null&&N(Sn),null;case 24:return l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),Sl(Gt),Ot(e),null;case 25:return null;case 30:return null}throw Error(s(156,e.tag))}function Qm(t,e){switch(Uc(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return Sl(Gt),Nt(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return xl(e),null;case 31:if(e.memoizedState!==null){if(Re(e),e.alternate===null)throw Error(s(340));yn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 13:if(Re(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(s(340));yn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return N(wt),null;case 4:return Nt(),null;case 10:return Sl(e.type),null;case 22:case 23:return Re(e),kc(),t!==null&&N(Sn),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return Sl(Gt),null;case 25:return null;default:return null}}function ad(t,e){switch(Uc(e),e.tag){case 3:Sl(Gt),Nt();break;case 26:case 27:case 5:xl(e);break;case 4:Nt();break;case 31:e.memoizedState!==null&&Re(e);break;case 13:Re(e);break;case 19:N(wt);break;case 10:Sl(e.type);break;case 22:case 23:Re(e),kc(),t!==null&&N(Sn);break;case 24:Sl(Gt)}}function Ja(t,e){try{var l=e.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var a=n.next;l=a;do{if((l.tag&t)===t){n=void 0;var u=l.create,f=l.inst;n=u(),f.destroy=n}l=l.next}while(l!==a)}}catch(r){Et(e,e.return,r)}}function Kl(t,e,l){try{var n=e.updateQueue,a=n!==null?n.lastEffect:null;if(a!==null){var u=a.next;n=u;do{if((n.tag&t)===t){var f=n.inst,r=f.destroy;if(r!==void 0){f.destroy=void 0,a=e;var v=l,z=r;try{z()}catch(O){Et(a,v,O)}}}n=n.next}while(n!==u)}}catch(O){Et(e,e.return,O)}}function ud(t){var e=t.updateQueue;if(e!==null){var l=t.stateNode;try{$s(e,l)}catch(n){Et(t,t.return,n)}}}function id(t,e,l){l.props=An(t.type,t.memoizedProps),l.state=t.memoizedState;try{l.componentWillUnmount()}catch(n){Et(t,e,n)}}function ka(t,e){try{var l=t.ref;if(l!==null){switch(t.tag){case 26:case 27:case 5:var n=t.stateNode;break;case 30:n=t.stateNode;break;default:n=t.stateNode}typeof l=="function"?t.refCleanup=l(n):l.current=n}}catch(a){Et(t,e,a)}}function fl(t,e){var l=t.ref,n=t.refCleanup;if(l!==null)if(typeof n=="function")try{n()}catch(a){Et(t,e,a)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(a){Et(t,e,a)}else l.current=null}function cd(t){var e=t.type,l=t.memoizedProps,n=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break t;case"img":l.src?n.src=l.src:l.srcSet&&(n.srcset=l.srcSet)}}catch(a){Et(t,t.return,a)}}function Cf(t,e,l){try{var n=t.stateNode;sy(n,t.type,l,e),n[be]=e}catch(a){Et(t,t.return,a)}}function fd(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Pl(t.type)||t.tag===4}function Of(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||fd(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Pl(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function _f(t,e,l){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(t,e):(e=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,e.appendChild(t),l=l._reactRootContainer,l!=null||e.onclick!==null||(e.onclick=vl));else if(n!==4&&(n===27&&Pl(t.type)&&(l=t.stateNode,e=null),t=t.child,t!==null))for(_f(t,e,l),t=t.sibling;t!==null;)_f(t,e,l),t=t.sibling}function vi(t,e,l){var n=t.tag;if(n===5||n===6)t=t.stateNode,e?l.insertBefore(t,e):l.appendChild(t);else if(n!==4&&(n===27&&Pl(t.type)&&(l=t.stateNode),t=t.child,t!==null))for(vi(t,e,l),t=t.sibling;t!==null;)vi(t,e,l),t=t.sibling}function od(t){var e=t.stateNode,l=t.memoizedProps;try{for(var n=t.type,a=e.attributes;a.length;)e.removeAttributeNode(a[0]);ue(e,n,l),e[ee]=t,e[be]=l}catch(u){Et(t,t.return,u)}}var Al=!1,Lt=!1,Rf=!1,sd=typeof WeakSet=="function"?WeakSet:Set,Ft=null;function Lm(t,e){if(t=t.containerInfo,Ff=qi,t=Ts(t),Tc(t)){if("selectionStart"in t)var l={start:t.selectionStart,end:t.selectionEnd};else t:{l=(l=t.ownerDocument)&&l.defaultView||window;var n=l.getSelection&&l.getSelection();if(n&&n.rangeCount!==0){l=n.anchorNode;var a=n.anchorOffset,u=n.focusNode;n=n.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break t}var f=0,r=-1,v=-1,z=0,O=0,U=t,A=null;e:for(;;){for(var D;U!==l||a!==0&&U.nodeType!==3||(r=f+a),U!==u||n!==0&&U.nodeType!==3||(v=f+n),U.nodeType===3&&(f+=U.nodeValue.length),(D=U.firstChild)!==null;)A=U,U=D;for(;;){if(U===t)break e;if(A===l&&++z===a&&(r=f),A===u&&++O===n&&(v=f),(D=U.nextSibling)!==null)break;U=A,A=U.parentNode}U=D}l=r===-1||v===-1?null:{start:r,end:v}}else l=null}l=l||{start:0,end:0}}else l=null;for(If={focusedElem:t,selectionRange:l},qi=!1,Ft=e;Ft!==null;)if(e=Ft,t=e.child,(e.subtreeFlags&1028)!==0&&t!==null)t.return=e,Ft=t;else for(;Ft!==null;){switch(e=Ft,u=e.alternate,t=e.flags,e.tag){case 0:if((t&4)!==0&&(t=e.updateQueue,t=t!==null?t.events:null,t!==null))for(l=0;l<t.length;l++)a=t[l],a.ref.impl=a.nextImpl;break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,l=e,a=u.memoizedProps,u=u.memoizedState,n=l.stateNode;try{var V=An(l.type,a);t=n.getSnapshotBeforeUpdate(V,u),n.__reactInternalSnapshotBeforeUpdate=t}catch(I){Et(l,l.return,I)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,l=t.nodeType,l===9)eo(t);else if(l===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":eo(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(s(163))}if(t=e.sibling,t!==null){t.return=e.return,Ft=t;break}Ft=e.return}}function rd(t,e,l){var n=l.flags;switch(l.tag){case 0:case 11:case 15:Ml(t,l),n&4&&Ja(5,l);break;case 1:if(Ml(t,l),n&4)if(t=l.stateNode,e===null)try{t.componentDidMount()}catch(f){Et(l,l.return,f)}else{var a=An(l.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(a,e,t.__reactInternalSnapshotBeforeUpdate)}catch(f){Et(l,l.return,f)}}n&64&&ud(l),n&512&&ka(l,l.return);break;case 3:if(Ml(t,l),n&64&&(t=l.updateQueue,t!==null)){if(e=null,l.child!==null)switch(l.child.tag){case 27:case 5:e=l.child.stateNode;break;case 1:e=l.child.stateNode}try{$s(t,e)}catch(f){Et(l,l.return,f)}}break;case 27:e===null&&n&4&&od(l);case 26:case 5:Ml(t,l),e===null&&n&4&&cd(l),n&512&&ka(l,l.return);break;case 12:Ml(t,l);break;case 31:Ml(t,l),n&4&&vd(t,l);break;case 13:Ml(t,l),n&4&&md(t,l),n&64&&(t=l.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(l=Im.bind(null,l),by(t,l))));break;case 22:if(n=l.memoizedState!==null||Al,!n){e=e!==null&&e.memoizedState!==null||Lt,a=Al;var u=Lt;Al=n,(Lt=e)&&!u?Cl(t,l,(l.subtreeFlags&8772)!==0):Ml(t,l),Al=a,Lt=u}break;case 30:break;default:Ml(t,l)}}function dd(t){var e=t.alternate;e!==null&&(t.alternate=null,dd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&uc(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var xt=null,pe=!1;function Dl(t,e,l){for(l=l.child;l!==null;)hd(t,e,l),l=l.sibling}function hd(t,e,l){if(re&&typeof re.onCommitFiberUnmount=="function")try{re.onCommitFiberUnmount(on,l)}catch{}switch(l.tag){case 26:Lt||fl(l,e),Dl(t,e,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:Lt||fl(l,e);var n=xt,a=pe;Pl(l.type)&&(xt=l.stateNode,pe=!1),Dl(t,e,l),nu(l.stateNode),xt=n,pe=a;break;case 5:Lt||fl(l,e);case 6:if(n=xt,a=pe,xt=null,Dl(t,e,l),xt=n,pe=a,xt!==null)if(pe)try{(xt.nodeType===9?xt.body:xt.nodeName==="HTML"?xt.ownerDocument.body:xt).removeChild(l.stateNode)}catch(u){Et(l,e,u)}else try{xt.removeChild(l.stateNode)}catch(u){Et(l,e,u)}break;case 18:xt!==null&&(pe?(t=xt,uh(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,l.stateNode),ha(t)):uh(xt,l.stateNode));break;case 4:n=xt,a=pe,xt=l.stateNode.containerInfo,pe=!0,Dl(t,e,l),xt=n,pe=a;break;case 0:case 11:case 14:case 15:Kl(2,l,e),Lt||Kl(4,l,e),Dl(t,e,l);break;case 1:Lt||(fl(l,e),n=l.stateNode,typeof n.componentWillUnmount=="function"&&id(l,e,n)),Dl(t,e,l);break;case 21:Dl(t,e,l);break;case 22:Lt=(n=Lt)||l.memoizedState!==null,Dl(t,e,l),Lt=n;break;default:Dl(t,e,l)}}function vd(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null))){t=t.dehydrated;try{ha(t)}catch(l){Et(e,e.return,l)}}}function md(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{ha(t)}catch(l){Et(e,e.return,l)}}function Zm(t){switch(t.tag){case 31:case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new sd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new sd),e;default:throw Error(s(435,t.tag))}}function mi(t,e){var l=Zm(t);e.forEach(function(n){if(!l.has(n)){l.add(n);var a=Pm.bind(null,t,n);n.then(a,a)}})}function Ee(t,e){var l=e.deletions;if(l!==null)for(var n=0;n<l.length;n++){var a=l[n],u=t,f=e,r=f;t:for(;r!==null;){switch(r.tag){case 27:if(Pl(r.type)){xt=r.stateNode,pe=!1;break t}break;case 5:xt=r.stateNode,pe=!1;break t;case 3:case 4:xt=r.stateNode.containerInfo,pe=!0;break t}r=r.return}if(xt===null)throw Error(s(160));hd(u,f,a),xt=null,pe=!1,u=a.alternate,u!==null&&(u.return=null),a.return=null}if(e.subtreeFlags&13886)for(e=e.child;e!==null;)yd(e,t),e=e.sibling}var el=null;function yd(t,e){var l=t.alternate,n=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:Ee(e,t),Te(t),n&4&&(Kl(3,t,t.return),Ja(3,t),Kl(5,t,t.return));break;case 1:Ee(e,t),Te(t),n&512&&(Lt||l===null||fl(l,l.return)),n&64&&Al&&(t=t.updateQueue,t!==null&&(n=t.callbacks,n!==null&&(l=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=l===null?n:l.concat(n))));break;case 26:var a=el;if(Ee(e,t),Te(t),n&512&&(Lt||l===null||fl(l,l.return)),n&4){var u=l!==null?l.memoizedState:null;if(n=t.memoizedState,l===null)if(n===null)if(t.stateNode===null){t:{n=t.type,l=t.memoizedProps,a=a.ownerDocument||a;e:switch(n){case"title":u=a.getElementsByTagName("title")[0],(!u||u[Ea]||u[ee]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=a.createElement(n),a.head.insertBefore(u,a.querySelector("head > title"))),ue(u,n,l),u[ee]=t,Wt(u),n=u;break t;case"link":var f=yh("link","href",a).get(n+(l.href||""));if(f){for(var r=0;r<f.length;r++)if(u=f[r],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){f.splice(r,1);break e}}u=a.createElement(n),ue(u,n,l),a.head.appendChild(u);break;case"meta":if(f=yh("meta","content",a).get(n+(l.content||""))){for(r=0;r<f.length;r++)if(u=f[r],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){f.splice(r,1);break e}}u=a.createElement(n),ue(u,n,l),a.head.appendChild(u);break;default:throw Error(s(468,n))}u[ee]=t,Wt(u),n=u}t.stateNode=n}else gh(a,t.type,t.stateNode);else t.stateNode=mh(a,n,t.memoizedProps);else u!==n?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,n===null?gh(a,t.type,t.stateNode):mh(a,n,t.memoizedProps)):n===null&&t.stateNode!==null&&Cf(t,t.memoizedProps,l.memoizedProps)}break;case 27:Ee(e,t),Te(t),n&512&&(Lt||l===null||fl(l,l.return)),l!==null&&n&4&&Cf(t,t.memoizedProps,l.memoizedProps);break;case 5:if(Ee(e,t),Te(t),n&512&&(Lt||l===null||fl(l,l.return)),t.flags&32){a=t.stateNode;try{Bn(a,"")}catch(V){Et(t,t.return,V)}}n&4&&t.stateNode!=null&&(a=t.memoizedProps,Cf(t,a,l!==null?l.memoizedProps:a)),n&1024&&(Rf=!0);break;case 6:if(Ee(e,t),Te(t),n&4){if(t.stateNode===null)throw Error(s(162));n=t.memoizedProps,l=t.stateNode;try{l.nodeValue=n}catch(V){Et(t,t.return,V)}}break;case 3:if(Ni=null,a=el,el=_i(e.containerInfo),Ee(e,t),el=a,Te(t),n&4&&l!==null&&l.memoizedState.isDehydrated)try{ha(e.containerInfo)}catch(V){Et(t,t.return,V)}Rf&&(Rf=!1,gd(t));break;case 4:n=el,el=_i(t.stateNode.containerInfo),Ee(e,t),Te(t),el=n;break;case 12:Ee(e,t),Te(t);break;case 31:Ee(e,t),Te(t),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,mi(t,n)));break;case 13:Ee(e,t),Te(t),t.child.flags&8192&&t.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(gi=ie()),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,mi(t,n)));break;case 22:a=t.memoizedState!==null;var v=l!==null&&l.memoizedState!==null,z=Al,O=Lt;if(Al=z||a,Lt=O||v,Ee(e,t),Lt=O,Al=z,Te(t),n&8192)t:for(e=t.stateNode,e._visibility=a?e._visibility&-2:e._visibility|1,a&&(l===null||v||Al||Lt||Dn(t)),l=null,e=t;;){if(e.tag===5||e.tag===26){if(l===null){v=l=e;try{if(u=v.stateNode,a)f=u.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{r=v.stateNode;var U=v.memoizedProps.style,A=U!=null&&U.hasOwnProperty("display")?U.display:null;r.style.display=A==null||typeof A=="boolean"?"":(""+A).trim()}}catch(V){Et(v,v.return,V)}}}else if(e.tag===6){if(l===null){v=e;try{v.stateNode.nodeValue=a?"":v.memoizedProps}catch(V){Et(v,v.return,V)}}}else if(e.tag===18){if(l===null){v=e;try{var D=v.stateNode;a?ih(D,!0):ih(v.stateNode,!1)}catch(V){Et(v,v.return,V)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;l===e&&(l=null),e=e.return}l===e&&(l=null),e.sibling.return=e.return,e=e.sibling}n&4&&(n=t.updateQueue,n!==null&&(l=n.retryQueue,l!==null&&(n.retryQueue=null,mi(t,l))));break;case 19:Ee(e,t),Te(t),n&4&&(n=t.updateQueue,n!==null&&(t.updateQueue=null,mi(t,n)));break;case 30:break;case 21:break;default:Ee(e,t),Te(t)}}function Te(t){var e=t.flags;if(e&2){try{for(var l,n=t.return;n!==null;){if(fd(n)){l=n;break}n=n.return}if(l==null)throw Error(s(160));switch(l.tag){case 27:var a=l.stateNode,u=Of(t);vi(t,u,a);break;case 5:var f=l.stateNode;l.flags&32&&(Bn(f,""),l.flags&=-33);var r=Of(t);vi(t,r,f);break;case 3:case 4:var v=l.stateNode.containerInfo,z=Of(t);_f(t,z,v);break;default:throw Error(s(161))}}catch(O){Et(t,t.return,O)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function gd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;gd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function Ml(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)rd(t,e.alternate,e),e=e.sibling}function Dn(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:Kl(4,e,e.return),Dn(e);break;case 1:fl(e,e.return);var l=e.stateNode;typeof l.componentWillUnmount=="function"&&id(e,e.return,l),Dn(e);break;case 27:nu(e.stateNode);case 26:case 5:fl(e,e.return),Dn(e);break;case 22:e.memoizedState===null&&Dn(e);break;case 30:Dn(e);break;default:Dn(e)}t=t.sibling}}function Cl(t,e,l){for(l=l&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var n=e.alternate,a=t,u=e,f=u.flags;switch(u.tag){case 0:case 11:case 15:Cl(a,u,l),Ja(4,u);break;case 1:if(Cl(a,u,l),n=u,a=n.stateNode,typeof a.componentDidMount=="function")try{a.componentDidMount()}catch(z){Et(n,n.return,z)}if(n=u,a=n.updateQueue,a!==null){var r=n.stateNode;try{var v=a.shared.hiddenCallbacks;if(v!==null)for(a.shared.hiddenCallbacks=null,a=0;a<v.length;a++)ks(v[a],r)}catch(z){Et(n,n.return,z)}}l&&f&64&&ud(u),ka(u,u.return);break;case 27:od(u);case 26:case 5:Cl(a,u,l),l&&n===null&&f&4&&cd(u),ka(u,u.return);break;case 12:Cl(a,u,l);break;case 31:Cl(a,u,l),l&&f&4&&vd(a,u);break;case 13:Cl(a,u,l),l&&f&4&&md(a,u);break;case 22:u.memoizedState===null&&Cl(a,u,l),ka(u,u.return);break;case 30:break;default:Cl(a,u,l)}e=e.sibling}}function Nf(t,e){var l=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==l&&(t!=null&&t.refCount++,l!=null&&Ha(l))}function xf(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ha(t))}function ll(t,e,l,n){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)bd(t,e,l,n),e=e.sibling}function bd(t,e,l,n){var a=e.flags;switch(e.tag){case 0:case 11:case 15:ll(t,e,l,n),a&2048&&Ja(9,e);break;case 1:ll(t,e,l,n);break;case 3:ll(t,e,l,n),a&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&Ha(t)));break;case 12:if(a&2048){ll(t,e,l,n),t=e.stateNode;try{var u=e.memoizedProps,f=u.id,r=u.onPostCommit;typeof r=="function"&&r(f,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(v){Et(e,e.return,v)}}else ll(t,e,l,n);break;case 31:ll(t,e,l,n);break;case 13:ll(t,e,l,n);break;case 23:break;case 22:u=e.stateNode,f=e.alternate,e.memoizedState!==null?u._visibility&2?ll(t,e,l,n):$a(t,e):u._visibility&2?ll(t,e,l,n):(u._visibility|=2,la(t,e,l,n,(e.subtreeFlags&10256)!==0||!1)),a&2048&&Nf(f,e);break;case 24:ll(t,e,l,n),a&2048&&xf(e.alternate,e);break;default:ll(t,e,l,n)}}function la(t,e,l,n,a){for(a=a&&((e.subtreeFlags&10256)!==0||!1),e=e.child;e!==null;){var u=t,f=e,r=l,v=n,z=f.flags;switch(f.tag){case 0:case 11:case 15:la(u,f,r,v,a),Ja(8,f);break;case 23:break;case 22:var O=f.stateNode;f.memoizedState!==null?O._visibility&2?la(u,f,r,v,a):$a(u,f):(O._visibility|=2,la(u,f,r,v,a)),a&&z&2048&&Nf(f.alternate,f);break;case 24:la(u,f,r,v,a),a&&z&2048&&xf(f.alternate,f);break;default:la(u,f,r,v,a)}e=e.sibling}}function $a(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var l=t,n=e,a=n.flags;switch(n.tag){case 22:$a(l,n),a&2048&&Nf(n.alternate,n);break;case 24:$a(l,n),a&2048&&xf(n.alternate,n);break;default:$a(l,n)}e=e.sibling}}var Wa=8192;function na(t,e,l){if(t.subtreeFlags&Wa)for(t=t.child;t!==null;)Sd(t,e,l),t=t.sibling}function Sd(t,e,l){switch(t.tag){case 26:na(t,e,l),t.flags&Wa&&t.memoizedState!==null&&Ry(l,el,t.memoizedState,t.memoizedProps);break;case 5:na(t,e,l);break;case 3:case 4:var n=el;el=_i(t.stateNode.containerInfo),na(t,e,l),el=n;break;case 22:t.memoizedState===null&&(n=t.alternate,n!==null&&n.memoizedState!==null?(n=Wa,Wa=16777216,na(t,e,l),Wa=n):na(t,e,l));break;default:na(t,e,l)}}function pd(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Fa(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var n=e[l];Ft=n,Td(n,t)}pd(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ed(t),t=t.sibling}function Ed(t){switch(t.tag){case 0:case 11:case 15:Fa(t),t.flags&2048&&Kl(9,t,t.return);break;case 3:Fa(t);break;case 12:Fa(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,yi(t)):Fa(t);break;default:Fa(t)}}function yi(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var l=0;l<e.length;l++){var n=e[l];Ft=n,Td(n,t)}pd(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:Kl(8,e,e.return),yi(e);break;case 22:l=e.stateNode,l._visibility&2&&(l._visibility&=-3,yi(e));break;default:yi(e)}t=t.sibling}}function Td(t,e){for(;Ft!==null;){var l=Ft;switch(l.tag){case 0:case 11:case 15:Kl(8,l,e);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var n=l.memoizedState.cachePool.pool;n!=null&&n.refCount++}break;case 24:Ha(l.memoizedState.cache)}if(n=l.child,n!==null)n.return=l,Ft=n;else t:for(l=t;Ft!==null;){n=Ft;var a=n.sibling,u=n.return;if(dd(n),n===l){Ft=null;break t}if(a!==null){a.return=u,Ft=a;break t}Ft=u}}}var Vm={getCacheForType:function(t){var e=ne(Gt),l=e.data.get(t);return l===void 0&&(l=t(),e.data.set(t,l)),l},cacheSignal:function(){return ne(Gt).controller.signal}},Km=typeof WeakMap=="function"?WeakMap:Map,St=0,Dt=null,ct=null,st=0,pt=0,Ne=null,Jl=!1,aa=!1,Uf=!1,Ol=0,Ht=0,kl=0,Mn=0,Hf=0,xe=0,ua=0,Ia=null,ze=null,qf=!1,gi=0,zd=0,bi=1/0,Si=null,$l=null,Vt=0,Wl=null,ia=null,_l=0,Bf=0,wf=null,Ad=null,Pa=0,jf=null;function Ue(){return(St&2)!==0&&st!==0?st&-st:_.T!==null?Zf():Pe()}function Dd(){if(xe===0)if((st&536870912)===0||dt){var t=_n;_n<<=1,(_n&3932160)===0&&(_n=262144),xe=t}else xe=536870912;return t=_e.current,t!==null&&(t.flags|=32),xe}function Ae(t,e,l){(t===Dt&&(pt===2||pt===9)||t.cancelPendingCommit!==null)&&(ca(t,0),Fl(t,st,xe,!1)),Be(t,l),((St&2)===0||t!==Dt)&&(t===Dt&&((St&2)===0&&(Mn|=l),Ht===4&&Fl(t,st,xe,!1)),ol(t))}function Md(t,e,l){if((St&6)!==0)throw Error(s(327));var n=!l&&(e&127)===0&&(e&t.expiredLanes)===0||me(t,e),a=n?$m(t,e):Gf(t,e,!0),u=n;do{if(a===0){aa&&!n&&Fl(t,e,0,!1);break}else{if(l=t.current.alternate,u&&!Jm(l)){a=Gf(t,e,!1),u=!1;continue}if(a===2){if(u=e,t.errorRecoveryDisabledLanes&u)var f=0;else f=t.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){e=f;t:{var r=t;a=Ia;var v=r.current.memoizedState.isDehydrated;if(v&&(ca(r,f).flags|=256),f=Gf(r,f,!1),f!==2){if(Uf&&!v){r.errorRecoveryDisabledLanes|=u,Mn|=u,a=4;break t}u=ze,ze=a,u!==null&&(ze===null?ze=u:ze.push.apply(ze,u))}a=f}if(u=!1,a!==2)continue}}if(a===1){ca(t,0),Fl(t,e,0,!0);break}t:{switch(n=t,u=a,u){case 0:case 1:throw Error(s(345));case 4:if((e&4194048)!==e)break;case 6:Fl(n,e,xe,!Jl);break t;case 2:ze=null;break;case 3:case 5:break;default:throw Error(s(329))}if((e&62914560)===e&&(a=gi+300-ie(),10<a)){if(Fl(n,e,xe,!Jl),$t(n,0,!0)!==0)break t;_l=e,n.timeoutHandle=nh(Cd.bind(null,n,l,ze,Si,qf,e,xe,Mn,ua,Jl,u,"Throttled",-0,0),a);break t}Cd(n,l,ze,Si,qf,e,xe,Mn,ua,Jl,u,null,-0,0)}}break}while(!0);ol(t)}function Cd(t,e,l,n,a,u,f,r,v,z,O,U,A,D){if(t.timeoutHandle=-1,U=e.subtreeFlags,U&8192||(U&16785408)===16785408){U={stylesheets:null,count:0,imgCount:0,imgBytes:0,suspenseyImages:[],waitingForImages:!0,waitingForViewTransition:!1,unsuspend:vl},Sd(e,u,U);var V=(u&62914560)===u?gi-ie():(u&4194048)===u?zd-ie():0;if(V=Ny(U,V),V!==null){_l=u,t.cancelPendingCommit=V(qd.bind(null,t,e,u,l,n,a,f,r,v,O,U,null,A,D)),Fl(t,u,f,!z);return}}qd(t,e,u,l,n,a,f,r,v)}function Jm(t){for(var e=t;;){var l=e.tag;if((l===0||l===11||l===15)&&e.flags&16384&&(l=e.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var n=0;n<l.length;n++){var a=l[n],u=a.getSnapshot;a=a.value;try{if(!Ce(u(),a))return!1}catch{return!1}}if(l=e.child,e.subtreeFlags&16384&&l!==null)l.return=e,e=l;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Fl(t,e,l,n){e&=~Hf,e&=~Mn,t.suspendedLanes|=e,t.pingedLanes&=~e,n&&(t.warmLanes|=e),n=t.expirationTimes;for(var a=e;0<a;){var u=31-ce(a),f=1<<u;n[u]=-1,a&=~f}l!==0&&Ie(t,l,e)}function pi(){return(St&6)===0?(tu(0),!1):!0}function Yf(){if(ct!==null){if(pt===0)var t=ct.return;else t=ct,bl=gn=null,tf(t),Fn=null,Ba=0,t=ct;for(;t!==null;)ad(t.alternate,t),t=t.return;ct=null}}function ca(t,e){var l=t.timeoutHandle;l!==-1&&(t.timeoutHandle=-1,hy(l)),l=t.cancelPendingCommit,l!==null&&(t.cancelPendingCommit=null,l()),_l=0,Yf(),Dt=t,ct=l=yl(t.current,null),st=e,pt=0,Ne=null,Jl=!1,aa=me(t,e),Uf=!1,ua=xe=Hf=Mn=kl=Ht=0,ze=Ia=null,qf=!1,(e&8)!==0&&(e|=e&32);var n=t.entangledLanes;if(n!==0)for(t=t.entanglements,n&=e;0<n;){var a=31-ce(n),u=1<<a;e|=t[a],n&=~u}return Ol=e,Xu(),l}function Od(t,e){ut=null,_.H=Za,e===Wn||e===$u?(e=Zs(),pt=3):e===Qc?(e=Zs(),pt=4):pt=e===gf?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,Ne=e,ct===null&&(Ht=1,oi(t,Qe(e,t.current)))}function _d(){var t=_e.current;return t===null?!0:(st&4194048)===st?Ke===null:(st&62914560)===st||(st&536870912)!==0?t===Ke:!1}function Rd(){var t=_.H;return _.H=Za,t===null?Za:t}function Nd(){var t=_.A;return _.A=Vm,t}function Ei(){Ht=4,Jl||(st&4194048)!==st&&_e.current!==null||(aa=!0),(kl&134217727)===0&&(Mn&134217727)===0||Dt===null||Fl(Dt,st,xe,!1)}function Gf(t,e,l){var n=St;St|=2;var a=Rd(),u=Nd();(Dt!==t||st!==e)&&(Si=null,ca(t,e)),e=!1;var f=Ht;t:do try{if(pt!==0&&ct!==null){var r=ct,v=Ne;switch(pt){case 8:Yf(),f=6;break t;case 3:case 2:case 9:case 6:_e.current===null&&(e=!0);var z=pt;if(pt=0,Ne=null,fa(t,r,v,z),l&&aa){f=0;break t}break;default:z=pt,pt=0,Ne=null,fa(t,r,v,z)}}km(),f=Ht;break}catch(O){Od(t,O)}while(!0);return e&&t.shellSuspendCounter++,bl=gn=null,St=n,_.H=a,_.A=u,ct===null&&(Dt=null,st=0,Xu()),f}function km(){for(;ct!==null;)xd(ct)}function $m(t,e){var l=St;St|=2;var n=Rd(),a=Nd();Dt!==t||st!==e?(Si=null,bi=ie()+500,ca(t,e)):aa=me(t,e);t:do try{if(pt!==0&&ct!==null){e=ct;var u=Ne;e:switch(pt){case 1:pt=0,Ne=null,fa(t,e,u,1);break;case 2:case 9:if(Qs(u)){pt=0,Ne=null,Ud(e);break}e=function(){pt!==2&&pt!==9||Dt!==t||(pt=7),ol(t)},u.then(e,e);break t;case 3:pt=7;break t;case 4:pt=5;break t;case 7:Qs(u)?(pt=0,Ne=null,Ud(e)):(pt=0,Ne=null,fa(t,e,u,7));break;case 5:var f=null;switch(ct.tag){case 26:f=ct.memoizedState;case 5:case 27:var r=ct;if(f?bh(f):r.stateNode.complete){pt=0,Ne=null;var v=r.sibling;if(v!==null)ct=v;else{var z=r.return;z!==null?(ct=z,Ti(z)):ct=null}break e}}pt=0,Ne=null,fa(t,e,u,5);break;case 6:pt=0,Ne=null,fa(t,e,u,6);break;case 8:Yf(),Ht=6;break t;default:throw Error(s(462))}}Wm();break}catch(O){Od(t,O)}while(!0);return bl=gn=null,_.H=n,_.A=a,St=l,ct!==null?0:(Dt=null,st=0,Xu(),Ht)}function Wm(){for(;ct!==null&&!Cn();)xd(ct)}function xd(t){var e=ld(t.alternate,t,Ol);t.memoizedProps=t.pendingProps,e===null?Ti(t):ct=e}function Ud(t){var e=t,l=e.alternate;switch(e.tag){case 15:case 0:e=Wr(l,e,e.pendingProps,e.type,void 0,st);break;case 11:e=Wr(l,e,e.pendingProps,e.type.render,e.ref,st);break;case 5:tf(e);default:ad(l,e),e=ct=Ns(e,Ol),e=ld(l,e,Ol)}t.memoizedProps=t.pendingProps,e===null?Ti(t):ct=e}function fa(t,e,l,n){bl=gn=null,tf(e),Fn=null,Ba=0;var a=e.return;try{if(jm(t,a,e,l,st)){Ht=1,oi(t,Qe(l,t.current)),ct=null;return}}catch(u){if(a!==null)throw ct=a,u;Ht=1,oi(t,Qe(l,t.current)),ct=null;return}e.flags&32768?(dt||n===1?t=!0:aa||(st&536870912)!==0?t=!1:(Jl=t=!0,(n===2||n===9||n===3||n===6)&&(n=_e.current,n!==null&&n.tag===13&&(n.flags|=16384))),Hd(e,t)):Ti(e)}function Ti(t){var e=t;do{if((e.flags&32768)!==0){Hd(e,Jl);return}t=e.return;var l=Xm(e.alternate,e,Ol);if(l!==null){ct=l;return}if(e=e.sibling,e!==null){ct=e;return}ct=e=t}while(e!==null);Ht===0&&(Ht=5)}function Hd(t,e){do{var l=Qm(t.alternate,t);if(l!==null){l.flags&=32767,ct=l;return}if(l=t.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!e&&(t=t.sibling,t!==null)){ct=t;return}ct=t=l}while(t!==null);Ht=6,ct=null}function qd(t,e,l,n,a,u,f,r,v){t.cancelPendingCommit=null;do zi();while(Vt!==0);if((St&6)!==0)throw Error(s(327));if(e!==null){if(e===t.current)throw Error(s(177));if(u=e.lanes|e.childLanes,u|=Cc,Zt(t,l,u,f,r,v),t===Dt&&(ct=Dt=null,st=0),ia=e,Wl=t,_l=l,Bf=u,wf=a,Ad=n,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,ty(Fe,function(){return Gd(),null})):(t.callbackNode=null,t.callbackPriority=0),n=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||n){n=_.T,_.T=null,a=j.p,j.p=2,f=St,St|=4;try{Lm(t,e,l)}finally{St=f,j.p=a,_.T=n}}Vt=1,Bd(),wd(),jd()}}function Bd(){if(Vt===1){Vt=0;var t=Wl,e=ia,l=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||l){l=_.T,_.T=null;var n=j.p;j.p=2;var a=St;St|=4;try{yd(e,t);var u=If,f=Ts(t.containerInfo),r=u.focusedElem,v=u.selectionRange;if(f!==r&&r&&r.ownerDocument&&Es(r.ownerDocument.documentElement,r)){if(v!==null&&Tc(r)){var z=v.start,O=v.end;if(O===void 0&&(O=z),"selectionStart"in r)r.selectionStart=z,r.selectionEnd=Math.min(O,r.value.length);else{var U=r.ownerDocument||document,A=U&&U.defaultView||window;if(A.getSelection){var D=A.getSelection(),V=r.textContent.length,I=Math.min(v.start,V),At=v.end===void 0?I:Math.min(v.end,V);!D.extend&&I>At&&(f=At,At=I,I=f);var E=ps(r,I),b=ps(r,At);if(E&&b&&(D.rangeCount!==1||D.anchorNode!==E.node||D.anchorOffset!==E.offset||D.focusNode!==b.node||D.focusOffset!==b.offset)){var T=U.createRange();T.setStart(E.node,E.offset),D.removeAllRanges(),I>At?(D.addRange(T),D.extend(b.node,b.offset)):(T.setEnd(b.node,b.offset),D.addRange(T))}}}}for(U=[],D=r;D=D.parentNode;)D.nodeType===1&&U.push({element:D,left:D.scrollLeft,top:D.scrollTop});for(typeof r.focus=="function"&&r.focus(),r=0;r<U.length;r++){var x=U[r];x.element.scrollLeft=x.left,x.element.scrollTop=x.top}}qi=!!Ff,If=Ff=null}finally{St=a,j.p=n,_.T=l}}t.current=e,Vt=2}}function wd(){if(Vt===2){Vt=0;var t=Wl,e=ia,l=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||l){l=_.T,_.T=null;var n=j.p;j.p=2;var a=St;St|=4;try{rd(t,e.alternate,e)}finally{St=a,j.p=n,_.T=l}}Vt=3}}function jd(){if(Vt===4||Vt===3){Vt=0,Mu();var t=Wl,e=ia,l=_l,n=Ad;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Vt=5:(Vt=0,ia=Wl=null,Yd(t,t.pendingLanes));var a=t.pendingLanes;if(a===0&&($l=null),dl(l),e=e.stateNode,re&&typeof re.onCommitFiberRoot=="function")try{re.onCommitFiberRoot(on,e,void 0,(e.current.flags&128)===128)}catch{}if(n!==null){e=_.T,a=j.p,j.p=2,_.T=null;try{for(var u=t.onRecoverableError,f=0;f<n.length;f++){var r=n[f];u(r.value,{componentStack:r.stack})}}finally{_.T=e,j.p=a}}(_l&3)!==0&&zi(),ol(t),a=t.pendingLanes,(l&261930)!==0&&(a&42)!==0?t===jf?Pa++:(Pa=0,jf=t):Pa=0,tu(0)}}function Yd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,Ha(e)))}function zi(){return Bd(),wd(),jd(),Gd()}function Gd(){if(Vt!==5)return!1;var t=Wl,e=Bf;Bf=0;var l=dl(_l),n=_.T,a=j.p;try{j.p=32>l?32:l,_.T=null,l=wf,wf=null;var u=Wl,f=_l;if(Vt=0,ia=Wl=null,_l=0,(St&6)!==0)throw Error(s(331));var r=St;if(St|=4,Ed(u.current),bd(u,u.current,f,l),St=r,tu(0,!1),re&&typeof re.onPostCommitFiberRoot=="function")try{re.onPostCommitFiberRoot(on,u)}catch{}return!0}finally{j.p=a,_.T=n,Yd(t,e)}}function Xd(t,e,l){e=Qe(l,e),e=yf(t.stateNode,e,2),t=Ll(t,e,2),t!==null&&(Be(t,2),ol(t))}function Et(t,e,l){if(t.tag===3)Xd(t,t,l);else for(;e!==null;){if(e.tag===3){Xd(e,t,l);break}else if(e.tag===1){var n=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof n.componentDidCatch=="function"&&($l===null||!$l.has(n))){t=Qe(l,t),l=Qr(2),n=Ll(e,l,2),n!==null&&(Lr(l,n,e,t),Be(n,2),ol(n));break}}e=e.return}}function Xf(t,e,l){var n=t.pingCache;if(n===null){n=t.pingCache=new Km;var a=new Set;n.set(e,a)}else a=n.get(e),a===void 0&&(a=new Set,n.set(e,a));a.has(l)||(Uf=!0,a.add(l),t=Fm.bind(null,t,e,l),e.then(t,t))}function Fm(t,e,l){var n=t.pingCache;n!==null&&n.delete(e),t.pingedLanes|=t.suspendedLanes&l,t.warmLanes&=~l,Dt===t&&(st&l)===l&&(Ht===4||Ht===3&&(st&62914560)===st&&300>ie()-gi?(St&2)===0&&ca(t,0):Hf|=l,ua===st&&(ua=0)),ol(t)}function Qd(t,e){e===0&&(e=fe()),t=vn(t,e),t!==null&&(Be(t,e),ol(t))}function Im(t){var e=t.memoizedState,l=0;e!==null&&(l=e.retryLane),Qd(t,l)}function Pm(t,e){var l=0;switch(t.tag){case 31:case 13:var n=t.stateNode,a=t.memoizedState;a!==null&&(l=a.retryLane);break;case 19:n=t.stateNode;break;case 22:n=t.stateNode._retryCache;break;default:throw Error(s(314))}n!==null&&n.delete(e),Qd(t,l)}function ty(t,e){return He(t,e)}var Ai=null,oa=null,Qf=!1,Di=!1,Lf=!1,Il=0;function ol(t){t!==oa&&t.next===null&&(oa===null?Ai=oa=t:oa=oa.next=t),Di=!0,Qf||(Qf=!0,ly())}function tu(t,e){if(!Lf&&Di){Lf=!0;do for(var l=!1,n=Ai;n!==null;){if(t!==0){var a=n.pendingLanes;if(a===0)var u=0;else{var f=n.suspendedLanes,r=n.pingedLanes;u=(1<<31-ce(42|t)+1)-1,u&=a&~(f&~r),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,Kd(n,u))}else u=st,u=$t(n,n===Dt?u:0,n.cancelPendingCommit!==null||n.timeoutHandle!==-1),(u&3)===0||me(n,u)||(l=!0,Kd(n,u));n=n.next}while(l);Lf=!1}}function ey(){Ld()}function Ld(){Di=Qf=!1;var t=0;Il!==0&&dy()&&(t=Il);for(var e=ie(),l=null,n=Ai;n!==null;){var a=n.next,u=Zd(n,e);u===0?(n.next=null,l===null?Ai=a:l.next=a,a===null&&(oa=l)):(l=n,(t!==0||(u&3)!==0)&&(Di=!0)),n=a}Vt!==0&&Vt!==5||tu(t),Il!==0&&(Il=0)}function Zd(t,e){for(var l=t.suspendedLanes,n=t.pingedLanes,a=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var f=31-ce(u),r=1<<f,v=a[f];v===-1?((r&l)===0||(r&n)!==0)&&(a[f]=ye(r,e)):v<=e&&(t.expiredLanes|=r),u&=~r}if(e=Dt,l=st,l=$t(t,t===e?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n=t.callbackNode,l===0||t===e&&(pt===2||pt===9)||t.cancelPendingCommit!==null)return n!==null&&n!==null&&Hl(n),t.callbackNode=null,t.callbackPriority=0;if((l&3)===0||me(t,l)){if(e=l&-l,e===t.callbackPriority)return e;switch(n!==null&&Hl(n),dl(l)){case 2:case 8:l=sl;break;case 32:l=Fe;break;case 268435456:l=pa;break;default:l=Fe}return n=Vd.bind(null,t),l=He(l,n),t.callbackPriority=e,t.callbackNode=l,e}return n!==null&&n!==null&&Hl(n),t.callbackPriority=2,t.callbackNode=null,2}function Vd(t,e){if(Vt!==0&&Vt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var l=t.callbackNode;if(zi()&&t.callbackNode!==l)return null;var n=st;return n=$t(t,t===Dt?n:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),n===0?null:(Md(t,n,e),Zd(t,ie()),t.callbackNode!=null&&t.callbackNode===l?Vd.bind(null,t):null)}function Kd(t,e){if(zi())return null;Md(t,e,!0)}function ly(){vy(function(){(St&6)!==0?He(Cu,ey):Ld()})}function Zf(){if(Il===0){var t=kn;t===0&&(t=On,On<<=1,(On&261888)===0&&(On=256)),Il=t}return Il}function Jd(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:Uu(""+t)}function kd(t,e){var l=e.ownerDocument.createElement("input");return l.name=e.name,l.value=e.value,t.id&&l.setAttribute("form",t.id),e.parentNode.insertBefore(l,e),t=new FormData(t),l.parentNode.removeChild(l),t}function ny(t,e,l,n,a){if(e==="submit"&&l&&l.stateNode===a){var u=Jd((a[be]||null).action),f=n.submitter;f&&(e=(e=f[be]||null)?Jd(e.formAction):f.getAttribute("formAction"),e!==null&&(u=e,f=null));var r=new wu("action","action",null,n,a);t.push({event:r,listeners:[{instance:null,listener:function(){if(n.defaultPrevented){if(Il!==0){var v=f?kd(a,f):new FormData(a);sf(l,{pending:!0,data:v,method:a.method,action:u},null,v)}}else typeof u=="function"&&(r.preventDefault(),v=f?kd(a,f):new FormData(a),sf(l,{pending:!0,data:v,method:a.method,action:u},u,v))},currentTarget:a}]})}}for(var Vf=0;Vf<Mc.length;Vf++){var Kf=Mc[Vf],ay=Kf.toLowerCase(),uy=Kf[0].toUpperCase()+Kf.slice(1);tl(ay,"on"+uy)}tl(Ds,"onAnimationEnd"),tl(Ms,"onAnimationIteration"),tl(Cs,"onAnimationStart"),tl("dblclick","onDoubleClick"),tl("focusin","onFocus"),tl("focusout","onBlur"),tl(Em,"onTransitionRun"),tl(Tm,"onTransitionStart"),tl(zm,"onTransitionCancel"),tl(Os,"onTransitionEnd"),Hn("onMouseEnter",["mouseout","mouseover"]),Hn("onMouseLeave",["mouseout","mouseover"]),Hn("onPointerEnter",["pointerout","pointerover"]),Hn("onPointerLeave",["pointerout","pointerover"]),sn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),sn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),sn("onBeforeInput",["compositionend","keypress","textInput","paste"]),sn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),sn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),sn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var eu="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),iy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(eu));function $d(t,e){e=(e&4)!==0;for(var l=0;l<t.length;l++){var n=t[l],a=n.event;n=n.listeners;t:{var u=void 0;if(e)for(var f=n.length-1;0<=f;f--){var r=n[f],v=r.instance,z=r.currentTarget;if(r=r.listener,v!==u&&a.isPropagationStopped())break t;u=r,a.currentTarget=z;try{u(a)}catch(O){Gu(O)}a.currentTarget=null,u=v}else for(f=0;f<n.length;f++){if(r=n[f],v=r.instance,z=r.currentTarget,r=r.listener,v!==u&&a.isPropagationStopped())break t;u=r,a.currentTarget=z;try{u(a)}catch(O){Gu(O)}a.currentTarget=null,u=v}}}}function ft(t,e){var l=e[ac];l===void 0&&(l=e[ac]=new Set);var n=t+"__bubble";l.has(n)||(Wd(e,t,2,!1),l.add(n))}function Jf(t,e,l){var n=0;e&&(n|=4),Wd(l,t,n,e)}var Mi="_reactListening"+Math.random().toString(36).slice(2);function kf(t){if(!t[Mi]){t[Mi]=!0,Lo.forEach(function(l){l!=="selectionchange"&&(iy.has(l)||Jf(l,!1,t),Jf(l,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[Mi]||(e[Mi]=!0,Jf("selectionchange",!1,e))}}function Wd(t,e,l,n){switch(Dh(e)){case 2:var a=Hy;break;case 8:a=qy;break;default:a=oo}l=a.bind(null,e,l,t),a=void 0,!hc||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(a=!0),n?a!==void 0?t.addEventListener(e,l,{capture:!0,passive:a}):t.addEventListener(e,l,!0):a!==void 0?t.addEventListener(e,l,{passive:a}):t.addEventListener(e,l,!1)}function $f(t,e,l,n,a){var u=n;if((e&1)===0&&(e&2)===0&&n!==null)t:for(;;){if(n===null)return;var f=n.tag;if(f===3||f===4){var r=n.stateNode.containerInfo;if(r===a)break;if(f===4)for(f=n.return;f!==null;){var v=f.tag;if((v===3||v===4)&&f.stateNode.containerInfo===a)return;f=f.return}for(;r!==null;){if(f=Nn(r),f===null)return;if(v=f.tag,v===5||v===6||v===26||v===27){n=u=f;continue t}r=r.parentNode}}n=n.return}es(function(){var z=u,O=rc(l),U=[];t:{var A=_s.get(t);if(A!==void 0){var D=wu,V=t;switch(t){case"keypress":if(qu(l)===0)break t;case"keydown":case"keyup":D=Pv;break;case"focusin":V="focus",D=gc;break;case"focusout":V="blur",D=gc;break;case"beforeblur":case"afterblur":D=gc;break;case"click":if(l.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":D=as;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":D=Xv;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":D=lm;break;case Ds:case Ms:case Cs:D=Zv;break;case Os:D=am;break;case"scroll":case"scrollend":D=Yv;break;case"wheel":D=im;break;case"copy":case"cut":case"paste":D=Kv;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":D=is;break;case"toggle":case"beforetoggle":D=fm}var I=(e&4)!==0,At=!I&&(t==="scroll"||t==="scrollend"),E=I?A!==null?A+"Capture":null:A;I=[];for(var b=z,T;b!==null;){var x=b;if(T=x.stateNode,x=x.tag,x!==5&&x!==26&&x!==27||T===null||E===null||(x=za(b,E),x!=null&&I.push(lu(b,x,T))),At)break;b=b.return}0<I.length&&(A=new D(A,V,null,l,O),U.push({event:A,listeners:I}))}}if((e&7)===0){t:{if(A=t==="mouseover"||t==="pointerover",D=t==="mouseout"||t==="pointerout",A&&l!==sc&&(V=l.relatedTarget||l.fromElement)&&(Nn(V)||V[Rn]))break t;if((D||A)&&(A=O.window===O?O:(A=O.ownerDocument)?A.defaultView||A.parentWindow:window,D?(V=l.relatedTarget||l.toElement,D=z,V=V?Nn(V):null,V!==null&&(At=h(V),I=V.tag,V!==At||I!==5&&I!==27&&I!==6)&&(V=null)):(D=null,V=z),D!==V)){if(I=as,x="onMouseLeave",E="onMouseEnter",b="mouse",(t==="pointerout"||t==="pointerover")&&(I=is,x="onPointerLeave",E="onPointerEnter",b="pointer"),At=D==null?A:Ta(D),T=V==null?A:Ta(V),A=new I(x,b+"leave",D,l,O),A.target=At,A.relatedTarget=T,x=null,Nn(O)===z&&(I=new I(E,b+"enter",V,l,O),I.target=T,I.relatedTarget=At,x=I),At=x,D&&V)e:{for(I=cy,E=D,b=V,T=0,x=E;x;x=I(x))T++;x=0;for(var W=b;W;W=I(W))x++;for(;0<T-x;)E=I(E),T--;for(;0<x-T;)b=I(b),x--;for(;T--;){if(E===b||b!==null&&E===b.alternate){I=E;break e}E=I(E),b=I(b)}I=null}else I=null;D!==null&&Fd(U,A,D,I,!1),V!==null&&At!==null&&Fd(U,At,V,I,!0)}}t:{if(A=z?Ta(z):window,D=A.nodeName&&A.nodeName.toLowerCase(),D==="select"||D==="input"&&A.type==="file")var mt=vs;else if(ds(A))if(ms)mt=bm;else{mt=ym;var k=mm}else D=A.nodeName,!D||D.toLowerCase()!=="input"||A.type!=="checkbox"&&A.type!=="radio"?z&&oc(z.elementType)&&(mt=vs):mt=gm;if(mt&&(mt=mt(t,z))){hs(U,mt,l,O);break t}k&&k(t,A,z),t==="focusout"&&z&&A.type==="number"&&z.memoizedProps.value!=null&&fc(A,"number",A.value)}switch(k=z?Ta(z):window,t){case"focusin":(ds(k)||k.contentEditable==="true")&&(Gn=k,zc=z,Na=null);break;case"focusout":Na=zc=Gn=null;break;case"mousedown":Ac=!0;break;case"contextmenu":case"mouseup":case"dragend":Ac=!1,zs(U,l,O);break;case"selectionchange":if(pm)break;case"keydown":case"keyup":zs(U,l,O)}var it;if(Sc)t:{switch(t){case"compositionstart":var rt="onCompositionStart";break t;case"compositionend":rt="onCompositionEnd";break t;case"compositionupdate":rt="onCompositionUpdate";break t}rt=void 0}else Yn?ss(t,l)&&(rt="onCompositionEnd"):t==="keydown"&&l.keyCode===229&&(rt="onCompositionStart");rt&&(cs&&l.locale!=="ko"&&(Yn||rt!=="onCompositionStart"?rt==="onCompositionEnd"&&Yn&&(it=ls()):(Bl=O,vc="value"in Bl?Bl.value:Bl.textContent,Yn=!0)),k=Ci(z,rt),0<k.length&&(rt=new us(rt,t,null,l,O),U.push({event:rt,listeners:k}),it?rt.data=it:(it=rs(l),it!==null&&(rt.data=it)))),(it=sm?rm(t,l):dm(t,l))&&(rt=Ci(z,"onBeforeInput"),0<rt.length&&(k=new us("onBeforeInput","beforeinput",null,l,O),U.push({event:k,listeners:rt}),k.data=it)),ny(U,t,z,l,O)}$d(U,e)})}function lu(t,e,l){return{instance:t,listener:e,currentTarget:l}}function Ci(t,e){for(var l=e+"Capture",n=[];t!==null;){var a=t,u=a.stateNode;if(a=a.tag,a!==5&&a!==26&&a!==27||u===null||(a=za(t,l),a!=null&&n.unshift(lu(t,a,u)),a=za(t,e),a!=null&&n.push(lu(t,a,u))),t.tag===3)return n;t=t.return}return[]}function cy(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Fd(t,e,l,n,a){for(var u=e._reactName,f=[];l!==null&&l!==n;){var r=l,v=r.alternate,z=r.stateNode;if(r=r.tag,v!==null&&v===n)break;r!==5&&r!==26&&r!==27||z===null||(v=z,a?(z=za(l,u),z!=null&&f.unshift(lu(l,z,v))):a||(z=za(l,u),z!=null&&f.push(lu(l,z,v)))),l=l.return}f.length!==0&&t.push({event:e,listeners:f})}var fy=/\r\n?/g,oy=/\u0000|\uFFFD/g;function Id(t){return(typeof t=="string"?t:""+t).replace(fy,`
`).replace(oy,"")}function Pd(t,e){return e=Id(e),Id(t)===e}function zt(t,e,l,n,a,u){switch(l){case"children":typeof n=="string"?e==="body"||e==="textarea"&&n===""||Bn(t,n):(typeof n=="number"||typeof n=="bigint")&&e!=="body"&&Bn(t,""+n);break;case"className":Nu(t,"class",n);break;case"tabIndex":Nu(t,"tabindex",n);break;case"dir":case"role":case"viewBox":case"width":case"height":Nu(t,l,n);break;case"style":Po(t,n,u);break;case"data":if(e!=="object"){Nu(t,"data",n);break}case"src":case"href":if(n===""&&(e!=="a"||l!=="href")){t.removeAttribute(l);break}if(n==null||typeof n=="function"||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(l);break}n=Uu(""+n),t.setAttribute(l,n);break;case"action":case"formAction":if(typeof n=="function"){t.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(e!=="input"&&zt(t,e,"name",a.name,a,null),zt(t,e,"formEncType",a.formEncType,a,null),zt(t,e,"formMethod",a.formMethod,a,null),zt(t,e,"formTarget",a.formTarget,a,null)):(zt(t,e,"encType",a.encType,a,null),zt(t,e,"method",a.method,a,null),zt(t,e,"target",a.target,a,null)));if(n==null||typeof n=="symbol"||typeof n=="boolean"){t.removeAttribute(l);break}n=Uu(""+n),t.setAttribute(l,n);break;case"onClick":n!=null&&(t.onclick=vl);break;case"onScroll":n!=null&&ft("scroll",t);break;case"onScrollEnd":n!=null&&ft("scrollend",t);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(s(61));if(l=n.__html,l!=null){if(a.children!=null)throw Error(s(60));t.innerHTML=l}}break;case"multiple":t.multiple=n&&typeof n!="function"&&typeof n!="symbol";break;case"muted":t.muted=n&&typeof n!="function"&&typeof n!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(n==null||typeof n=="function"||typeof n=="boolean"||typeof n=="symbol"){t.removeAttribute("xlink:href");break}l=Uu(""+n),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(l,""+n):t.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":n&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(l,""):t.removeAttribute(l);break;case"capture":case"download":n===!0?t.setAttribute(l,""):n!==!1&&n!=null&&typeof n!="function"&&typeof n!="symbol"?t.setAttribute(l,n):t.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":n!=null&&typeof n!="function"&&typeof n!="symbol"&&!isNaN(n)&&1<=n?t.setAttribute(l,n):t.removeAttribute(l);break;case"rowSpan":case"start":n==null||typeof n=="function"||typeof n=="symbol"||isNaN(n)?t.removeAttribute(l):t.setAttribute(l,n);break;case"popover":ft("beforetoggle",t),ft("toggle",t),Ru(t,"popover",n);break;case"xlinkActuate":hl(t,"http://www.w3.org/1999/xlink","xlink:actuate",n);break;case"xlinkArcrole":hl(t,"http://www.w3.org/1999/xlink","xlink:arcrole",n);break;case"xlinkRole":hl(t,"http://www.w3.org/1999/xlink","xlink:role",n);break;case"xlinkShow":hl(t,"http://www.w3.org/1999/xlink","xlink:show",n);break;case"xlinkTitle":hl(t,"http://www.w3.org/1999/xlink","xlink:title",n);break;case"xlinkType":hl(t,"http://www.w3.org/1999/xlink","xlink:type",n);break;case"xmlBase":hl(t,"http://www.w3.org/XML/1998/namespace","xml:base",n);break;case"xmlLang":hl(t,"http://www.w3.org/XML/1998/namespace","xml:lang",n);break;case"xmlSpace":hl(t,"http://www.w3.org/XML/1998/namespace","xml:space",n);break;case"is":Ru(t,"is",n);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=wv.get(l)||l,Ru(t,l,n))}}function Wf(t,e,l,n,a,u){switch(l){case"style":Po(t,n,u);break;case"dangerouslySetInnerHTML":if(n!=null){if(typeof n!="object"||!("__html"in n))throw Error(s(61));if(l=n.__html,l!=null){if(a.children!=null)throw Error(s(60));t.innerHTML=l}}break;case"children":typeof n=="string"?Bn(t,n):(typeof n=="number"||typeof n=="bigint")&&Bn(t,""+n);break;case"onScroll":n!=null&&ft("scroll",t);break;case"onScrollEnd":n!=null&&ft("scrollend",t);break;case"onClick":n!=null&&(t.onclick=vl);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Zo.hasOwnProperty(l))t:{if(l[0]==="o"&&l[1]==="n"&&(a=l.endsWith("Capture"),e=l.slice(2,a?l.length-7:void 0),u=t[be]||null,u=u!=null?u[l]:null,typeof u=="function"&&t.removeEventListener(e,u,a),typeof n=="function")){typeof u!="function"&&u!==null&&(l in t?t[l]=null:t.hasAttribute(l)&&t.removeAttribute(l)),t.addEventListener(e,n,a);break t}l in t?t[l]=n:n===!0?t.setAttribute(l,""):Ru(t,l,n)}}}function ue(t,e,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ft("error",t),ft("load",t);var n=!1,a=!1,u;for(u in l)if(l.hasOwnProperty(u)){var f=l[u];if(f!=null)switch(u){case"src":n=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:zt(t,e,u,f,l,null)}}a&&zt(t,e,"srcSet",l.srcSet,l,null),n&&zt(t,e,"src",l.src,l,null);return;case"input":ft("invalid",t);var r=u=f=a=null,v=null,z=null;for(n in l)if(l.hasOwnProperty(n)){var O=l[n];if(O!=null)switch(n){case"name":a=O;break;case"type":f=O;break;case"checked":v=O;break;case"defaultChecked":z=O;break;case"value":u=O;break;case"defaultValue":r=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(s(137,e));break;default:zt(t,e,n,O,l,null)}}$o(t,u,r,v,z,f,a,!1);return;case"select":ft("invalid",t),n=f=u=null;for(a in l)if(l.hasOwnProperty(a)&&(r=l[a],r!=null))switch(a){case"value":u=r;break;case"defaultValue":f=r;break;case"multiple":n=r;default:zt(t,e,a,r,l,null)}e=u,l=f,t.multiple=!!n,e!=null?qn(t,!!n,e,!1):l!=null&&qn(t,!!n,l,!0);return;case"textarea":ft("invalid",t),u=a=n=null;for(f in l)if(l.hasOwnProperty(f)&&(r=l[f],r!=null))switch(f){case"value":n=r;break;case"defaultValue":a=r;break;case"children":u=r;break;case"dangerouslySetInnerHTML":if(r!=null)throw Error(s(91));break;default:zt(t,e,f,r,l,null)}Fo(t,n,a,u);return;case"option":for(v in l)if(l.hasOwnProperty(v)&&(n=l[v],n!=null))switch(v){case"selected":t.selected=n&&typeof n!="function"&&typeof n!="symbol";break;default:zt(t,e,v,n,l,null)}return;case"dialog":ft("beforetoggle",t),ft("toggle",t),ft("cancel",t),ft("close",t);break;case"iframe":case"object":ft("load",t);break;case"video":case"audio":for(n=0;n<eu.length;n++)ft(eu[n],t);break;case"image":ft("error",t),ft("load",t);break;case"details":ft("toggle",t);break;case"embed":case"source":case"link":ft("error",t),ft("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(z in l)if(l.hasOwnProperty(z)&&(n=l[z],n!=null))switch(z){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:zt(t,e,z,n,l,null)}return;default:if(oc(e)){for(O in l)l.hasOwnProperty(O)&&(n=l[O],n!==void 0&&Wf(t,e,O,n,l,void 0));return}}for(r in l)l.hasOwnProperty(r)&&(n=l[r],n!=null&&zt(t,e,r,n,l,null))}function sy(t,e,l,n){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var a=null,u=null,f=null,r=null,v=null,z=null,O=null;for(D in l){var U=l[D];if(l.hasOwnProperty(D)&&U!=null)switch(D){case"checked":break;case"value":break;case"defaultValue":v=U;default:n.hasOwnProperty(D)||zt(t,e,D,null,n,U)}}for(var A in n){var D=n[A];if(U=l[A],n.hasOwnProperty(A)&&(D!=null||U!=null))switch(A){case"type":u=D;break;case"name":a=D;break;case"checked":z=D;break;case"defaultChecked":O=D;break;case"value":f=D;break;case"defaultValue":r=D;break;case"children":case"dangerouslySetInnerHTML":if(D!=null)throw Error(s(137,e));break;default:D!==U&&zt(t,e,A,D,n,U)}}cc(t,f,r,v,z,O,u,a);return;case"select":D=f=r=A=null;for(u in l)if(v=l[u],l.hasOwnProperty(u)&&v!=null)switch(u){case"value":break;case"multiple":D=v;default:n.hasOwnProperty(u)||zt(t,e,u,null,n,v)}for(a in n)if(u=n[a],v=l[a],n.hasOwnProperty(a)&&(u!=null||v!=null))switch(a){case"value":A=u;break;case"defaultValue":r=u;break;case"multiple":f=u;default:u!==v&&zt(t,e,a,u,n,v)}e=r,l=f,n=D,A!=null?qn(t,!!l,A,!1):!!n!=!!l&&(e!=null?qn(t,!!l,e,!0):qn(t,!!l,l?[]:"",!1));return;case"textarea":D=A=null;for(r in l)if(a=l[r],l.hasOwnProperty(r)&&a!=null&&!n.hasOwnProperty(r))switch(r){case"value":break;case"children":break;default:zt(t,e,r,null,n,a)}for(f in n)if(a=n[f],u=l[f],n.hasOwnProperty(f)&&(a!=null||u!=null))switch(f){case"value":A=a;break;case"defaultValue":D=a;break;case"children":break;case"dangerouslySetInnerHTML":if(a!=null)throw Error(s(91));break;default:a!==u&&zt(t,e,f,a,n,u)}Wo(t,A,D);return;case"option":for(var V in l)if(A=l[V],l.hasOwnProperty(V)&&A!=null&&!n.hasOwnProperty(V))switch(V){case"selected":t.selected=!1;break;default:zt(t,e,V,null,n,A)}for(v in n)if(A=n[v],D=l[v],n.hasOwnProperty(v)&&A!==D&&(A!=null||D!=null))switch(v){case"selected":t.selected=A&&typeof A!="function"&&typeof A!="symbol";break;default:zt(t,e,v,A,n,D)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var I in l)A=l[I],l.hasOwnProperty(I)&&A!=null&&!n.hasOwnProperty(I)&&zt(t,e,I,null,n,A);for(z in n)if(A=n[z],D=l[z],n.hasOwnProperty(z)&&A!==D&&(A!=null||D!=null))switch(z){case"children":case"dangerouslySetInnerHTML":if(A!=null)throw Error(s(137,e));break;default:zt(t,e,z,A,n,D)}return;default:if(oc(e)){for(var At in l)A=l[At],l.hasOwnProperty(At)&&A!==void 0&&!n.hasOwnProperty(At)&&Wf(t,e,At,void 0,n,A);for(O in n)A=n[O],D=l[O],!n.hasOwnProperty(O)||A===D||A===void 0&&D===void 0||Wf(t,e,O,A,n,D);return}}for(var E in l)A=l[E],l.hasOwnProperty(E)&&A!=null&&!n.hasOwnProperty(E)&&zt(t,e,E,null,n,A);for(U in n)A=n[U],D=l[U],!n.hasOwnProperty(U)||A===D||A==null&&D==null||zt(t,e,U,A,n,D)}function th(t){switch(t){case"css":case"script":case"font":case"img":case"image":case"input":case"link":return!0;default:return!1}}function ry(){if(typeof performance.getEntriesByType=="function"){for(var t=0,e=0,l=performance.getEntriesByType("resource"),n=0;n<l.length;n++){var a=l[n],u=a.transferSize,f=a.initiatorType,r=a.duration;if(u&&r&&th(f)){for(f=0,r=a.responseEnd,n+=1;n<l.length;n++){var v=l[n],z=v.startTime;if(z>r)break;var O=v.transferSize,U=v.initiatorType;O&&th(U)&&(v=v.responseEnd,f+=O*(v<r?1:(r-z)/(v-z)))}if(--n,e+=8*(u+f)/(a.duration/1e3),t++,10<t)break}}if(0<t)return e/t/1e6}return navigator.connection&&(t=navigator.connection.downlink,typeof t=="number")?t:5}var Ff=null,If=null;function Oi(t){return t.nodeType===9?t:t.ownerDocument}function eh(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function lh(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function Pf(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var to=null;function dy(){var t=window.event;return t&&t.type==="popstate"?t===to?!1:(to=t,!0):(to=null,!1)}var nh=typeof setTimeout=="function"?setTimeout:void 0,hy=typeof clearTimeout=="function"?clearTimeout:void 0,ah=typeof Promise=="function"?Promise:void 0,vy=typeof queueMicrotask=="function"?queueMicrotask:typeof ah<"u"?function(t){return ah.resolve(null).then(t).catch(my)}:nh;function my(t){setTimeout(function(){throw t})}function Pl(t){return t==="head"}function uh(t,e){var l=e,n=0;do{var a=l.nextSibling;if(t.removeChild(l),a&&a.nodeType===8)if(l=a.data,l==="/$"||l==="/&"){if(n===0){t.removeChild(a),ha(e);return}n--}else if(l==="$"||l==="$?"||l==="$~"||l==="$!"||l==="&")n++;else if(l==="html")nu(t.ownerDocument.documentElement);else if(l==="head"){l=t.ownerDocument.head,nu(l);for(var u=l.firstChild;u;){var f=u.nextSibling,r=u.nodeName;u[Ea]||r==="SCRIPT"||r==="STYLE"||r==="LINK"&&u.rel.toLowerCase()==="stylesheet"||l.removeChild(u),u=f}}else l==="body"&&nu(t.ownerDocument.body);l=a}while(l);ha(e)}function ih(t,e){var l=t;t=0;do{var n=l.nextSibling;if(l.nodeType===1?e?(l._stashedDisplay=l.style.display,l.style.display="none"):(l.style.display=l._stashedDisplay||"",l.getAttribute("style")===""&&l.removeAttribute("style")):l.nodeType===3&&(e?(l._stashedText=l.nodeValue,l.nodeValue=""):l.nodeValue=l._stashedText||""),n&&n.nodeType===8)if(l=n.data,l==="/$"){if(t===0)break;t--}else l!=="$"&&l!=="$?"&&l!=="$~"&&l!=="$!"||t++;l=n}while(l)}function eo(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var l=e;switch(e=e.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":eo(l),uc(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}t.removeChild(l)}}function yy(t,e,l,n){for(;t.nodeType===1;){var a=l;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!n&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(n){if(!t[Ea])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==a.rel||t.getAttribute("href")!==(a.href==null||a.href===""?null:a.href)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin)||t.getAttribute("title")!==(a.title==null?null:a.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(a.src==null?null:a.src)||t.getAttribute("type")!==(a.type==null?null:a.type)||t.getAttribute("crossorigin")!==(a.crossOrigin==null?null:a.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=a.name==null?null:""+a.name;if(a.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=Je(t.nextSibling),t===null)break}return null}function gy(t,e,l){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!l||(t=Je(t.nextSibling),t===null))return null;return t}function ch(t,e){for(;t.nodeType!==8;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!e||(t=Je(t.nextSibling),t===null))return null;return t}function lo(t){return t.data==="$?"||t.data==="$~"}function no(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState!=="loading"}function by(t,e){var l=t.ownerDocument;if(t.data==="$~")t._reactRetry=e;else if(t.data!=="$?"||l.readyState!=="loading")e();else{var n=function(){e(),l.removeEventListener("DOMContentLoaded",n)};l.addEventListener("DOMContentLoaded",n),t._reactRetry=n}}function Je(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="$~"||e==="&"||e==="F!"||e==="F")break;if(e==="/$"||e==="/&")return null}}return t}var ao=null;function fh(t){t=t.nextSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="/$"||l==="/&"){if(e===0)return Je(t.nextSibling);e--}else l!=="$"&&l!=="$!"&&l!=="$?"&&l!=="$~"&&l!=="&"||e++}t=t.nextSibling}return null}function oh(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var l=t.data;if(l==="$"||l==="$!"||l==="$?"||l==="$~"||l==="&"){if(e===0)return t;e--}else l!=="/$"&&l!=="/&"||e++}t=t.previousSibling}return null}function sh(t,e,l){switch(e=Oi(l),t){case"html":if(t=e.documentElement,!t)throw Error(s(452));return t;case"head":if(t=e.head,!t)throw Error(s(453));return t;case"body":if(t=e.body,!t)throw Error(s(454));return t;default:throw Error(s(451))}}function nu(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);uc(t)}var ke=new Map,rh=new Set;function _i(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var Rl=j.d;j.d={f:Sy,r:py,D:Ey,C:Ty,L:zy,m:Ay,X:My,S:Dy,M:Cy};function Sy(){var t=Rl.f(),e=pi();return t||e}function py(t){var e=xn(t);e!==null&&e.tag===5&&e.type==="form"?Or(e):Rl.r(t)}var sa=typeof document>"u"?null:document;function dh(t,e,l){var n=sa;if(n&&typeof e=="string"&&e){var a=Ge(e);a='link[rel="'+t+'"][href="'+a+'"]',typeof l=="string"&&(a+='[crossorigin="'+l+'"]'),rh.has(a)||(rh.add(a),t={rel:t,crossOrigin:l,href:e},n.querySelector(a)===null&&(e=n.createElement("link"),ue(e,"link",t),Wt(e),n.head.appendChild(e)))}}function Ey(t){Rl.D(t),dh("dns-prefetch",t,null)}function Ty(t,e){Rl.C(t,e),dh("preconnect",t,e)}function zy(t,e,l){Rl.L(t,e,l);var n=sa;if(n&&t&&e){var a='link[rel="preload"][as="'+Ge(e)+'"]';e==="image"&&l&&l.imageSrcSet?(a+='[imagesrcset="'+Ge(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(a+='[imagesizes="'+Ge(l.imageSizes)+'"]')):a+='[href="'+Ge(t)+'"]';var u=a;switch(e){case"style":u=ra(t);break;case"script":u=da(t)}ke.has(u)||(t=M({rel:"preload",href:e==="image"&&l&&l.imageSrcSet?void 0:t,as:e},l),ke.set(u,t),n.querySelector(a)!==null||e==="style"&&n.querySelector(au(u))||e==="script"&&n.querySelector(uu(u))||(e=n.createElement("link"),ue(e,"link",t),Wt(e),n.head.appendChild(e)))}}function Ay(t,e){Rl.m(t,e);var l=sa;if(l&&t){var n=e&&typeof e.as=="string"?e.as:"script",a='link[rel="modulepreload"][as="'+Ge(n)+'"][href="'+Ge(t)+'"]',u=a;switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=da(t)}if(!ke.has(u)&&(t=M({rel:"modulepreload",href:t},e),ke.set(u,t),l.querySelector(a)===null)){switch(n){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(uu(u)))return}n=l.createElement("link"),ue(n,"link",t),Wt(n),l.head.appendChild(n)}}}function Dy(t,e,l){Rl.S(t,e,l);var n=sa;if(n&&t){var a=Un(n).hoistableStyles,u=ra(t);e=e||"default";var f=a.get(u);if(!f){var r={loading:0,preload:null};if(f=n.querySelector(au(u)))r.loading=5;else{t=M({rel:"stylesheet",href:t,"data-precedence":e},l),(l=ke.get(u))&&uo(t,l);var v=f=n.createElement("link");Wt(v),ue(v,"link",t),v._p=new Promise(function(z,O){v.onload=z,v.onerror=O}),v.addEventListener("load",function(){r.loading|=1}),v.addEventListener("error",function(){r.loading|=2}),r.loading|=4,Ri(f,e,n)}f={type:"stylesheet",instance:f,count:1,state:r},a.set(u,f)}}}function My(t,e){Rl.X(t,e);var l=sa;if(l&&t){var n=Un(l).hoistableScripts,a=da(t),u=n.get(a);u||(u=l.querySelector(uu(a)),u||(t=M({src:t,async:!0},e),(e=ke.get(a))&&io(t,e),u=l.createElement("script"),Wt(u),ue(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},n.set(a,u))}}function Cy(t,e){Rl.M(t,e);var l=sa;if(l&&t){var n=Un(l).hoistableScripts,a=da(t),u=n.get(a);u||(u=l.querySelector(uu(a)),u||(t=M({src:t,async:!0,type:"module"},e),(e=ke.get(a))&&io(t,e),u=l.createElement("script"),Wt(u),ue(u,"link",t),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},n.set(a,u))}}function hh(t,e,l,n){var a=(a=lt.current)?_i(a):null;if(!a)throw Error(s(446));switch(t){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(e=ra(l.href),l=Un(a).hoistableStyles,n=l.get(e),n||(n={type:"style",instance:null,count:0,state:null},l.set(e,n)),n):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){t=ra(l.href);var u=Un(a).hoistableStyles,f=u.get(t);if(f||(a=a.ownerDocument||a,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,f),(u=a.querySelector(au(t)))&&!u._p&&(f.instance=u,f.state.loading=5),ke.has(t)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},ke.set(t,l),u||Oy(a,t,l,f.state))),e&&n===null)throw Error(s(528,""));return f}if(e&&n!==null)throw Error(s(529,""));return null;case"script":return e=l.async,l=l.src,typeof l=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=da(l),l=Un(a).hoistableScripts,n=l.get(e),n||(n={type:"script",instance:null,count:0,state:null},l.set(e,n)),n):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,t))}}function ra(t){return'href="'+Ge(t)+'"'}function au(t){return'link[rel="stylesheet"]['+t+"]"}function vh(t){return M({},t,{"data-precedence":t.precedence,precedence:null})}function Oy(t,e,l,n){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?n.loading=1:(e=t.createElement("link"),n.preload=e,e.addEventListener("load",function(){return n.loading|=1}),e.addEventListener("error",function(){return n.loading|=2}),ue(e,"link",l),Wt(e),t.head.appendChild(e))}function da(t){return'[src="'+Ge(t)+'"]'}function uu(t){return"script[async]"+t}function mh(t,e,l){if(e.count++,e.instance===null)switch(e.type){case"style":var n=t.querySelector('style[data-href~="'+Ge(l.href)+'"]');if(n)return e.instance=n,Wt(n),n;var a=M({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return n=(t.ownerDocument||t).createElement("style"),Wt(n),ue(n,"style",a),Ri(n,l.precedence,t),e.instance=n;case"stylesheet":a=ra(l.href);var u=t.querySelector(au(a));if(u)return e.state.loading|=4,e.instance=u,Wt(u),u;n=vh(l),(a=ke.get(a))&&uo(n,a),u=(t.ownerDocument||t).createElement("link"),Wt(u);var f=u;return f._p=new Promise(function(r,v){f.onload=r,f.onerror=v}),ue(u,"link",n),e.state.loading|=4,Ri(u,l.precedence,t),e.instance=u;case"script":return u=da(l.src),(a=t.querySelector(uu(u)))?(e.instance=a,Wt(a),a):(n=l,(a=ke.get(u))&&(n=M({},l),io(n,a)),t=t.ownerDocument||t,a=t.createElement("script"),Wt(a),ue(a,"link",n),t.head.appendChild(a),e.instance=a);case"void":return null;default:throw Error(s(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(n=e.instance,e.state.loading|=4,Ri(n,l.precedence,t));return e.instance}function Ri(t,e,l){for(var n=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),a=n.length?n[n.length-1]:null,u=a,f=0;f<n.length;f++){var r=n[f];if(r.dataset.precedence===e)u=r;else if(u!==a)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=l.nodeType===9?l.head:l,e.insertBefore(t,e.firstChild))}function uo(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function io(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var Ni=null;function yh(t,e,l){if(Ni===null){var n=new Map,a=Ni=new Map;a.set(l,n)}else a=Ni,n=a.get(l),n||(n=new Map,a.set(l,n));if(n.has(t))return n;for(n.set(t,null),l=l.getElementsByTagName(t),a=0;a<l.length;a++){var u=l[a];if(!(u[Ea]||u[ee]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var f=u.getAttribute(e)||"";f=t+f;var r=n.get(f);r?r.push(u):n.set(f,[u])}}return n}function gh(t,e,l){t=t.ownerDocument||t,t.head.insertBefore(l,e==="title"?t.querySelector("head > title"):null)}function _y(t,e,l){if(l===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function bh(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}function Ry(t,e,l,n){if(l.type==="stylesheet"&&(typeof n.media!="string"||matchMedia(n.media).matches!==!1)&&(l.state.loading&4)===0){if(l.instance===null){var a=ra(n.href),u=e.querySelector(au(a));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(t.count++,t=xi.bind(t),e.then(t,t)),l.state.loading|=4,l.instance=u,Wt(u);return}u=e.ownerDocument||e,n=vh(n),(a=ke.get(a))&&uo(n,a),u=u.createElement("link"),Wt(u);var f=u;f._p=new Promise(function(r,v){f.onload=r,f.onerror=v}),ue(u,"link",n),l.instance=u}t.stylesheets===null&&(t.stylesheets=new Map),t.stylesheets.set(l,e),(e=l.state.preload)&&(l.state.loading&3)===0&&(t.count++,l=xi.bind(t),e.addEventListener("load",l),e.addEventListener("error",l))}}var co=0;function Ny(t,e){return t.stylesheets&&t.count===0&&Hi(t,t.stylesheets),0<t.count||0<t.imgCount?function(l){var n=setTimeout(function(){if(t.stylesheets&&Hi(t,t.stylesheets),t.unsuspend){var u=t.unsuspend;t.unsuspend=null,u()}},6e4+e);0<t.imgBytes&&co===0&&(co=62500*ry());var a=setTimeout(function(){if(t.waitingForImages=!1,t.count===0&&(t.stylesheets&&Hi(t,t.stylesheets),t.unsuspend)){var u=t.unsuspend;t.unsuspend=null,u()}},(t.imgBytes>co?50:800)+e);return t.unsuspend=l,function(){t.unsuspend=null,clearTimeout(n),clearTimeout(a)}}:null}function xi(){if(this.count--,this.count===0&&(this.imgCount===0||!this.waitingForImages)){if(this.stylesheets)Hi(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var Ui=null;function Hi(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,Ui=new Map,e.forEach(xy,t),Ui=null,xi.call(t))}function xy(t,e){if(!(e.state.loading&4)){var l=Ui.get(t);if(l)var n=l.get(null);else{l=new Map,Ui.set(t,l);for(var a=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<a.length;u++){var f=a[u];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(l.set(f.dataset.precedence,f),n=f)}n&&l.set(null,n)}a=e.instance,f=a.getAttribute("data-precedence"),u=l.get(f)||n,u===n&&l.set(null,a),l.set(f,a),this.count++,n=xi.bind(this),a.addEventListener("load",n),a.addEventListener("error",n),u?u.parentNode.insertBefore(a,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(a,t.firstChild)),e.state.loading|=4}}var iu={$$typeof:w,Provider:null,Consumer:null,_currentValue:F,_currentValue2:F,_threadCount:0};function Uy(t,e,l,n,a,u,f,r,v){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=ge(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ge(0),this.hiddenUpdates=ge(null),this.identifierPrefix=n,this.onUncaughtError=a,this.onCaughtError=u,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=v,this.incompleteTransitions=new Map}function Sh(t,e,l,n,a,u,f,r,v,z,O,U){return t=new Uy(t,e,l,f,v,z,O,U,r),e=1,u===!0&&(e|=24),u=Oe(3,null,null,e),t.current=u,u.stateNode=t,e=Yc(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:n,isDehydrated:l,cache:e},Lc(u),t}function ph(t){return t?(t=Ln,t):Ln}function Eh(t,e,l,n,a,u){a=ph(a),n.context===null?n.context=a:n.pendingContext=a,n=Ql(e),n.payload={element:l},u=u===void 0?null:u,u!==null&&(n.callback=u),l=Ll(t,n,e),l!==null&&(Ae(l,t,e),ja(l,t,e))}function Th(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var l=t.retryLane;t.retryLane=l!==0&&l<e?l:e}}function fo(t,e){Th(t,e),(t=t.alternate)&&Th(t,e)}function zh(t){if(t.tag===13||t.tag===31){var e=vn(t,67108864);e!==null&&Ae(e,t,67108864),fo(t,67108864)}}function Ah(t){if(t.tag===13||t.tag===31){var e=Ue();e=rl(e);var l=vn(t,e);l!==null&&Ae(l,t,e),fo(t,e)}}var qi=!0;function Hy(t,e,l,n){var a=_.T;_.T=null;var u=j.p;try{j.p=2,oo(t,e,l,n)}finally{j.p=u,_.T=a}}function qy(t,e,l,n){var a=_.T;_.T=null;var u=j.p;try{j.p=8,oo(t,e,l,n)}finally{j.p=u,_.T=a}}function oo(t,e,l,n){if(qi){var a=so(n);if(a===null)$f(t,e,n,Bi,l),Mh(t,n);else if(wy(a,t,e,l,n))n.stopPropagation();else if(Mh(t,n),e&4&&-1<By.indexOf(t)){for(;a!==null;){var u=xn(a);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var f=Bt(u.pendingLanes);if(f!==0){var r=u;for(r.pendingLanes|=2,r.entangledLanes|=2;f;){var v=1<<31-ce(f);r.entanglements[1]|=v,f&=~v}ol(u),(St&6)===0&&(bi=ie()+500,tu(0))}}break;case 31:case 13:r=vn(u,2),r!==null&&Ae(r,u,2),pi(),fo(u,2)}if(u=so(n),u===null&&$f(t,e,n,Bi,l),u===a)break;a=u}a!==null&&n.stopPropagation()}else $f(t,e,n,null,l)}}function so(t){return t=rc(t),ro(t)}var Bi=null;function ro(t){if(Bi=null,t=Nn(t),t!==null){var e=h(t);if(e===null)t=null;else{var l=e.tag;if(l===13){if(t=p(e),t!==null)return t;t=null}else if(l===31){if(t=y(e),t!==null)return t;t=null}else if(l===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return Bi=t,null}function Dh(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ec()){case Cu:return 2;case sl:return 8;case Fe:case fn:return 32;case pa:return 268435456;default:return 32}default:return 32}}var ho=!1,tn=null,en=null,ln=null,cu=new Map,fu=new Map,nn=[],By="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Mh(t,e){switch(t){case"focusin":case"focusout":tn=null;break;case"dragenter":case"dragleave":en=null;break;case"mouseover":case"mouseout":ln=null;break;case"pointerover":case"pointerout":cu.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":fu.delete(e.pointerId)}}function ou(t,e,l,n,a,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:l,eventSystemFlags:n,nativeEvent:u,targetContainers:[a]},e!==null&&(e=xn(e),e!==null&&zh(e)),t):(t.eventSystemFlags|=n,e=t.targetContainers,a!==null&&e.indexOf(a)===-1&&e.push(a),t)}function wy(t,e,l,n,a){switch(e){case"focusin":return tn=ou(tn,t,e,l,n,a),!0;case"dragenter":return en=ou(en,t,e,l,n,a),!0;case"mouseover":return ln=ou(ln,t,e,l,n,a),!0;case"pointerover":var u=a.pointerId;return cu.set(u,ou(cu.get(u)||null,t,e,l,n,a)),!0;case"gotpointercapture":return u=a.pointerId,fu.set(u,ou(fu.get(u)||null,t,e,l,n,a)),!0}return!1}function Ch(t){var e=Nn(t.target);if(e!==null){var l=h(e);if(l!==null){if(e=l.tag,e===13){if(e=p(l),e!==null){t.blockedOn=e,ql(t.priority,function(){Ah(l)});return}}else if(e===31){if(e=y(l),e!==null){t.blockedOn=e,ql(t.priority,function(){Ah(l)});return}}else if(e===3&&l.stateNode.current.memoizedState.isDehydrated){t.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}t.blockedOn=null}function wi(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var l=so(t.nativeEvent);if(l===null){l=t.nativeEvent;var n=new l.constructor(l.type,l);sc=n,l.target.dispatchEvent(n),sc=null}else return e=xn(l),e!==null&&zh(e),t.blockedOn=l,!1;e.shift()}return!0}function Oh(t,e,l){wi(t)&&l.delete(e)}function jy(){ho=!1,tn!==null&&wi(tn)&&(tn=null),en!==null&&wi(en)&&(en=null),ln!==null&&wi(ln)&&(ln=null),cu.forEach(Oh),fu.forEach(Oh)}function ji(t,e){t.blockedOn===e&&(t.blockedOn=null,ho||(ho=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,jy)))}var Yi=null;function _h(t){Yi!==t&&(Yi=t,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Yi===t&&(Yi=null);for(var e=0;e<t.length;e+=3){var l=t[e],n=t[e+1],a=t[e+2];if(typeof n!="function"){if(ro(n||l)===null)continue;break}var u=xn(l);u!==null&&(t.splice(e,3),e-=3,sf(u,{pending:!0,data:a,method:l.method,action:n},n,a))}}))}function ha(t){function e(v){return ji(v,t)}tn!==null&&ji(tn,t),en!==null&&ji(en,t),ln!==null&&ji(ln,t),cu.forEach(e),fu.forEach(e);for(var l=0;l<nn.length;l++){var n=nn[l];n.blockedOn===t&&(n.blockedOn=null)}for(;0<nn.length&&(l=nn[0],l.blockedOn===null);)Ch(l),l.blockedOn===null&&nn.shift();if(l=(t.ownerDocument||t).$$reactFormReplay,l!=null)for(n=0;n<l.length;n+=3){var a=l[n],u=l[n+1],f=a[be]||null;if(typeof u=="function")f||_h(l);else if(f){var r=null;if(u&&u.hasAttribute("formAction")){if(a=u,f=u[be]||null)r=f.formAction;else if(ro(a)!==null)continue}else r=f.action;typeof r=="function"?l[n+1]=r:(l.splice(n,3),n-=3),_h(l)}}}function Rh(){function t(u){u.canIntercept&&u.info==="react-transition"&&u.intercept({handler:function(){return new Promise(function(f){return a=f})},focusReset:"manual",scroll:"manual"})}function e(){a!==null&&(a(),a=null),n||setTimeout(l,20)}function l(){if(!n&&!navigation.transition){var u=navigation.currentEntry;u&&u.url!=null&&navigation.navigate(u.url,{state:u.getState(),info:"react-transition",history:"replace"})}}if(typeof navigation=="object"){var n=!1,a=null;return navigation.addEventListener("navigate",t),navigation.addEventListener("navigatesuccess",e),navigation.addEventListener("navigateerror",e),setTimeout(l,100),function(){n=!0,navigation.removeEventListener("navigate",t),navigation.removeEventListener("navigatesuccess",e),navigation.removeEventListener("navigateerror",e),a!==null&&(a(),a=null)}}}function vo(t){this._internalRoot=t}Gi.prototype.render=vo.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(s(409));var l=e.current,n=Ue();Eh(l,n,t,e,null,null)},Gi.prototype.unmount=vo.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;Eh(t.current,2,null,t,null,null),pi(),e[Rn]=null}};function Gi(t){this._internalRoot=t}Gi.prototype.unstable_scheduleHydration=function(t){if(t){var e=Pe();t={blockedOn:null,target:t,priority:e};for(var l=0;l<nn.length&&e!==0&&e<nn[l].priority;l++);nn.splice(l,0,t),l===0&&Ch(t)}};var Nh=c.version;if(Nh!=="19.2.0")throw Error(s(527,Nh,"19.2.0"));j.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(s(188)):(t=Object.keys(t).join(","),Error(s(268,t)));return t=g(e),t=t!==null?R(t):null,t=t===null?null:t.stateNode,t};var Yy={bundleType:0,version:"19.2.0",rendererPackageName:"react-dom",currentDispatcherRef:_,reconcilerVersion:"19.2.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Xi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Xi.isDisabled&&Xi.supportsFiber)try{on=Xi.inject(Yy),re=Xi}catch{}}return ru.createRoot=function(t,e){if(!d(t))throw Error(s(299));var l=!1,n="",a=jr,u=Yr,f=Gr;return e!=null&&(e.unstable_strictMode===!0&&(l=!0),e.identifierPrefix!==void 0&&(n=e.identifierPrefix),e.onUncaughtError!==void 0&&(a=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(f=e.onRecoverableError)),e=Sh(t,1,!1,null,null,l,n,null,a,u,f,Rh),t[Rn]=e.current,kf(t),new vo(e)},ru.hydrateRoot=function(t,e,l){if(!d(t))throw Error(s(299));var n=!1,a="",u=jr,f=Yr,r=Gr,v=null;return l!=null&&(l.unstable_strictMode===!0&&(n=!0),l.identifierPrefix!==void 0&&(a=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(f=l.onCaughtError),l.onRecoverableError!==void 0&&(r=l.onRecoverableError),l.formState!==void 0&&(v=l.formState)),e=Sh(t,1,!0,e,l??null,n,a,v,u,f,r,Rh),e.context=ph(null),l=e.current,n=Ue(),n=rl(n),a=Ql(n),a.callback=null,Ll(l,a,n),l=n,e.current.lanes=l,Be(e,l),ol(e),t[Rn]=e.current,kf(t),new Gi(e)},ru.version="19.2.0",ru}var Xh;function $y(){if(Xh)return go.exports;Xh=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(c){console.error(c)}}return i(),go.exports=ky(),go.exports}var Wy=$y(),du=lv();const Wi=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u";function ma(i){const c=Object.prototype.toString.call(i);return c==="[object Window]"||c==="[object global]"}function No(i){return"nodeType"in i}function he(i){var c,o;return i?ma(i)?i:No(i)&&(c=(o=i.ownerDocument)==null?void 0:o.defaultView)!=null?c:window:window}function xo(i){const{Document:c}=he(i);return i instanceof c}function pu(i){return ma(i)?!1:i instanceof he(i).HTMLElement}function nv(i){return i instanceof he(i).SVGElement}function ya(i){return i?ma(i)?i.document:No(i)?xo(i)?i:pu(i)||nv(i)?i.ownerDocument:document:document:document}const Nl=Wi?C.useLayoutEffect:C.useEffect;function Fi(i){const c=C.useRef(i);return Nl(()=>{c.current=i}),C.useCallback(function(){for(var o=arguments.length,s=new Array(o),d=0;d<o;d++)s[d]=arguments[d];return c.current==null?void 0:c.current(...s)},[])}function Fy(){const i=C.useRef(null),c=C.useCallback((s,d)=>{i.current=setInterval(s,d)},[]),o=C.useCallback(()=>{i.current!==null&&(clearInterval(i.current),i.current=null)},[]);return[c,o]}function yu(i,c){c===void 0&&(c=[i]);const o=C.useRef(i);return Nl(()=>{o.current!==i&&(o.current=i)},c),o}function Eu(i,c){const o=C.useRef();return C.useMemo(()=>{const s=i(o.current);return o.current=s,s},[...c])}function Zi(i){const c=Fi(i),o=C.useRef(null),s=C.useCallback(d=>{d!==o.current&&c?.(d,o.current),o.current=d},[]);return[o,s]}function Vi(i){const c=C.useRef();return C.useEffect(()=>{c.current=i},[i]),c.current}let Eo={};function Ii(i,c){return C.useMemo(()=>{if(c)return c;const o=Eo[i]==null?0:Eo[i]+1;return Eo[i]=o,i+"-"+o},[i,c])}function av(i){return function(c){for(var o=arguments.length,s=new Array(o>1?o-1:0),d=1;d<o;d++)s[d-1]=arguments[d];return s.reduce((h,p)=>{const y=Object.entries(p);for(const[m,g]of y){const R=h[m];R!=null&&(h[m]=R+i*g)}return h},{...c})}}const va=av(1),Ki=av(-1);function Iy(i){return"clientX"in i&&"clientY"in i}function Uo(i){if(!i)return!1;const{KeyboardEvent:c}=he(i.target);return c&&i instanceof c}function Py(i){if(!i)return!1;const{TouchEvent:c}=he(i.target);return c&&i instanceof c}function gu(i){if(Py(i)){if(i.touches&&i.touches.length){const{clientX:c,clientY:o}=i.touches[0];return{x:c,y:o}}else if(i.changedTouches&&i.changedTouches.length){const{clientX:c,clientY:o}=i.changedTouches[0];return{x:c,y:o}}}return Iy(i)?{x:i.clientX,y:i.clientY}:null}const bu=Object.freeze({Translate:{toString(i){if(!i)return;const{x:c,y:o}=i;return"translate3d("+(c?Math.round(c):0)+"px, "+(o?Math.round(o):0)+"px, 0)"}},Scale:{toString(i){if(!i)return;const{scaleX:c,scaleY:o}=i;return"scaleX("+c+") scaleY("+o+")"}},Transform:{toString(i){if(i)return[bu.Translate.toString(i),bu.Scale.toString(i)].join(" ")}},Transition:{toString(i){let{property:c,duration:o,easing:s}=i;return c+" "+o+"ms "+s}}}),Qh="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function t0(i){return i.matches(Qh)?i:i.querySelector(Qh)}const e0={display:"none"};function l0(i){let{id:c,value:o}=i;return Yt.createElement("div",{id:c,style:e0},o)}function n0(i){let{id:c,announcement:o,ariaLiveType:s="assertive"}=i;const d={position:"fixed",top:0,left:0,width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};return Yt.createElement("div",{id:c,style:d,role:"status","aria-live":s,"aria-atomic":!0},o)}function a0(){const[i,c]=C.useState("");return{announce:C.useCallback(s=>{s!=null&&c(s)},[]),announcement:i}}const uv=C.createContext(null);function u0(i){const c=C.useContext(uv);C.useEffect(()=>{if(!c)throw new Error("useDndMonitor must be used within a children of <DndContext>");return c(i)},[i,c])}function i0(){const[i]=C.useState(()=>new Set),c=C.useCallback(s=>(i.add(s),()=>i.delete(s)),[i]);return[C.useCallback(s=>{let{type:d,event:h}=s;i.forEach(p=>{var y;return(y=p[d])==null?void 0:y.call(p,h)})},[i]),c]}const c0={draggable:`
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  `},f0={onDragStart(i){let{active:c}=i;return"Picked up draggable item "+c.id+"."},onDragOver(i){let{active:c,over:o}=i;return o?"Draggable item "+c.id+" was moved over droppable area "+o.id+".":"Draggable item "+c.id+" is no longer over a droppable area."},onDragEnd(i){let{active:c,over:o}=i;return o?"Draggable item "+c.id+" was dropped over droppable area "+o.id:"Draggable item "+c.id+" was dropped."},onDragCancel(i){let{active:c}=i;return"Dragging was cancelled. Draggable item "+c.id+" was dropped."}};function o0(i){let{announcements:c=f0,container:o,hiddenTextDescribedById:s,screenReaderInstructions:d=c0}=i;const{announce:h,announcement:p}=a0(),y=Ii("DndLiveRegion"),[m,g]=C.useState(!1);if(C.useEffect(()=>{g(!0)},[]),u0(C.useMemo(()=>({onDragStart(M){let{active:q}=M;h(c.onDragStart({active:q}))},onDragMove(M){let{active:q,over:H}=M;c.onDragMove&&h(c.onDragMove({active:q,over:H}))},onDragOver(M){let{active:q,over:H}=M;h(c.onDragOver({active:q,over:H}))},onDragEnd(M){let{active:q,over:H}=M;h(c.onDragEnd({active:q,over:H}))},onDragCancel(M){let{active:q,over:H}=M;h(c.onDragCancel({active:q,over:H}))}}),[h,c])),!m)return null;const R=Yt.createElement(Yt.Fragment,null,Yt.createElement(l0,{id:s,value:d.draggable}),Yt.createElement(n0,{id:y,announcement:p}));return o?du.createPortal(R,o):R}var Kt;(function(i){i.DragStart="dragStart",i.DragMove="dragMove",i.DragEnd="dragEnd",i.DragCancel="dragCancel",i.DragOver="dragOver",i.RegisterDroppable="registerDroppable",i.SetDroppableDisabled="setDroppableDisabled",i.UnregisterDroppable="unregisterDroppable"})(Kt||(Kt={}));function Ji(){}function Lh(i,c){return C.useMemo(()=>({sensor:i,options:c??{}}),[i,c])}function s0(){for(var i=arguments.length,c=new Array(i),o=0;o<i;o++)c[o]=arguments[o];return C.useMemo(()=>[...c].filter(s=>s!=null),[...c])}const nl=Object.freeze({x:0,y:0});function r0(i,c){const o=gu(i);if(!o)return"0 0";const s={x:(o.x-c.left)/c.width*100,y:(o.y-c.top)/c.height*100};return s.x+"% "+s.y+"%"}function d0(i,c){let{data:{value:o}}=i,{data:{value:s}}=c;return s-o}function h0(i,c){if(!i||i.length===0)return null;const[o]=i;return o[c]}function v0(i,c){const o=Math.max(c.top,i.top),s=Math.max(c.left,i.left),d=Math.min(c.left+c.width,i.left+i.width),h=Math.min(c.top+c.height,i.top+i.height),p=d-s,y=h-o;if(s<d&&o<h){const m=c.width*c.height,g=i.width*i.height,R=p*y,M=R/(m+g-R);return Number(M.toFixed(4))}return 0}const m0=i=>{let{collisionRect:c,droppableRects:o,droppableContainers:s}=i;const d=[];for(const h of s){const{id:p}=h,y=o.get(p);if(y){const m=v0(y,c);m>0&&d.push({id:p,data:{droppableContainer:h,value:m}})}}return d.sort(d0)};function y0(i,c,o){return{...i,scaleX:c&&o?c.width/o.width:1,scaleY:c&&o?c.height/o.height:1}}function iv(i,c){return i&&c?{x:i.left-c.left,y:i.top-c.top}:nl}function g0(i){return function(o){for(var s=arguments.length,d=new Array(s>1?s-1:0),h=1;h<s;h++)d[h-1]=arguments[h];return d.reduce((p,y)=>({...p,top:p.top+i*y.y,bottom:p.bottom+i*y.y,left:p.left+i*y.x,right:p.right+i*y.x}),{...o})}}const b0=g0(1);function cv(i){if(i.startsWith("matrix3d(")){const c=i.slice(9,-1).split(/, /);return{x:+c[12],y:+c[13],scaleX:+c[0],scaleY:+c[5]}}else if(i.startsWith("matrix(")){const c=i.slice(7,-1).split(/, /);return{x:+c[4],y:+c[5],scaleX:+c[0],scaleY:+c[3]}}return null}function S0(i,c,o){const s=cv(c);if(!s)return i;const{scaleX:d,scaleY:h,x:p,y}=s,m=i.left-p-(1-d)*parseFloat(o),g=i.top-y-(1-h)*parseFloat(o.slice(o.indexOf(" ")+1)),R=d?i.width/d:i.width,M=h?i.height/h:i.height;return{width:R,height:M,top:g,right:m+R,bottom:g+M,left:m}}const p0={ignoreTransform:!1};function Tu(i,c){c===void 0&&(c=p0);let o=i.getBoundingClientRect();if(c.ignoreTransform){const{transform:g,transformOrigin:R}=he(i).getComputedStyle(i);g&&(o=S0(o,g,R))}const{top:s,left:d,width:h,height:p,bottom:y,right:m}=o;return{top:s,left:d,width:h,height:p,bottom:y,right:m}}function Zh(i){return Tu(i,{ignoreTransform:!0})}function E0(i){const c=i.innerWidth,o=i.innerHeight;return{top:0,left:0,right:c,bottom:o,width:c,height:o}}function T0(i,c){return c===void 0&&(c=he(i).getComputedStyle(i)),c.position==="fixed"}function z0(i,c){c===void 0&&(c=he(i).getComputedStyle(i));const o=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(d=>{const h=c[d];return typeof h=="string"?o.test(h):!1})}function Ho(i,c){const o=[];function s(d){if(c!=null&&o.length>=c||!d)return o;if(xo(d)&&d.scrollingElement!=null&&!o.includes(d.scrollingElement))return o.push(d.scrollingElement),o;if(!pu(d)||nv(d)||o.includes(d))return o;const h=he(i).getComputedStyle(d);return d!==i&&z0(d,h)&&o.push(d),T0(d,h)?o:s(d.parentNode)}return i?s(i):o}function fv(i){const[c]=Ho(i,1);return c??null}function To(i){return!Wi||!i?null:ma(i)?i:No(i)?xo(i)||i===ya(i).scrollingElement?window:pu(i)?i:null:null}function ov(i){return ma(i)?i.scrollX:i.scrollLeft}function sv(i){return ma(i)?i.scrollY:i.scrollTop}function Co(i){return{x:ov(i),y:sv(i)}}var It;(function(i){i[i.Forward=1]="Forward",i[i.Backward=-1]="Backward"})(It||(It={}));function rv(i){return!Wi||!i?!1:i===document.scrollingElement}function dv(i){const c={x:0,y:0},o=rv(i)?{height:window.innerHeight,width:window.innerWidth}:{height:i.clientHeight,width:i.clientWidth},s={x:i.scrollWidth-o.width,y:i.scrollHeight-o.height},d=i.scrollTop<=c.y,h=i.scrollLeft<=c.x,p=i.scrollTop>=s.y,y=i.scrollLeft>=s.x;return{isTop:d,isLeft:h,isBottom:p,isRight:y,maxScroll:s,minScroll:c}}const A0={x:.2,y:.2};function D0(i,c,o,s,d){let{top:h,left:p,right:y,bottom:m}=o;s===void 0&&(s=10),d===void 0&&(d=A0);const{isTop:g,isBottom:R,isLeft:M,isRight:q}=dv(i),H={x:0,y:0},X={x:0,y:0},B={height:c.height*d.y,width:c.width*d.x};return!g&&h<=c.top+B.height?(H.y=It.Backward,X.y=s*Math.abs((c.top+B.height-h)/B.height)):!R&&m>=c.bottom-B.height&&(H.y=It.Forward,X.y=s*Math.abs((c.bottom-B.height-m)/B.height)),!q&&y>=c.right-B.width?(H.x=It.Forward,X.x=s*Math.abs((c.right-B.width-y)/B.width)):!M&&p<=c.left+B.width&&(H.x=It.Backward,X.x=s*Math.abs((c.left+B.width-p)/B.width)),{direction:H,speed:X}}function M0(i){if(i===document.scrollingElement){const{innerWidth:h,innerHeight:p}=window;return{top:0,left:0,right:h,bottom:p,width:h,height:p}}const{top:c,left:o,right:s,bottom:d}=i.getBoundingClientRect();return{top:c,left:o,right:s,bottom:d,width:i.clientWidth,height:i.clientHeight}}function hv(i){return i.reduce((c,o)=>va(c,Co(o)),nl)}function C0(i){return i.reduce((c,o)=>c+ov(o),0)}function O0(i){return i.reduce((c,o)=>c+sv(o),0)}function vv(i,c){if(c===void 0&&(c=Tu),!i)return;const{top:o,left:s,bottom:d,right:h}=c(i);fv(i)&&(d<=0||h<=0||o>=window.innerHeight||s>=window.innerWidth)&&i.scrollIntoView({block:"center",inline:"center"})}const _0=[["x",["left","right"],C0],["y",["top","bottom"],O0]];class qo{constructor(c,o){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;const s=Ho(o),d=hv(s);this.rect={...c},this.width=c.width,this.height=c.height;for(const[h,p,y]of _0)for(const m of p)Object.defineProperty(this,m,{get:()=>{const g=y(s),R=d[h]-g;return this.rect[m]+R},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}}class hu{constructor(c){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(o=>{var s;return(s=this.target)==null?void 0:s.removeEventListener(...o)})},this.target=c}add(c,o,s){var d;(d=this.target)==null||d.addEventListener(c,o,s),this.listeners.push([c,o,s])}}function R0(i){const{EventTarget:c}=he(i);return i instanceof c?i:ya(i)}function zo(i,c){const o=Math.abs(i.x),s=Math.abs(i.y);return typeof c=="number"?Math.sqrt(o**2+s**2)>c:"x"in c&&"y"in c?o>c.x&&s>c.y:"x"in c?o>c.x:"y"in c?s>c.y:!1}var $e;(function(i){i.Click="click",i.DragStart="dragstart",i.Keydown="keydown",i.ContextMenu="contextmenu",i.Resize="resize",i.SelectionChange="selectionchange",i.VisibilityChange="visibilitychange"})($e||($e={}));function Vh(i){i.preventDefault()}function N0(i){i.stopPropagation()}var Mt;(function(i){i.Space="Space",i.Down="ArrowDown",i.Right="ArrowRight",i.Left="ArrowLeft",i.Up="ArrowUp",i.Esc="Escape",i.Enter="Enter",i.Tab="Tab"})(Mt||(Mt={}));const mv={start:[Mt.Space,Mt.Enter],cancel:[Mt.Esc],end:[Mt.Space,Mt.Enter,Mt.Tab]},x0=(i,c)=>{let{currentCoordinates:o}=c;switch(i.code){case Mt.Right:return{...o,x:o.x+25};case Mt.Left:return{...o,x:o.x-25};case Mt.Down:return{...o,y:o.y+25};case Mt.Up:return{...o,y:o.y-25}}};class Bo{constructor(c){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=c;const{event:{target:o}}=c;this.props=c,this.listeners=new hu(ya(o)),this.windowListeners=new hu(he(o)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add($e.Resize,this.handleCancel),this.windowListeners.add($e.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add($e.Keydown,this.handleKeyDown))}handleStart(){const{activeNode:c,onStart:o}=this.props,s=c.node.current;s&&vv(s),o(nl)}handleKeyDown(c){if(Uo(c)){const{active:o,context:s,options:d}=this.props,{keyboardCodes:h=mv,coordinateGetter:p=x0,scrollBehavior:y="smooth"}=d,{code:m}=c;if(h.end.includes(m)){this.handleEnd(c);return}if(h.cancel.includes(m)){this.handleCancel(c);return}const{collisionRect:g}=s.current,R=g?{x:g.left,y:g.top}:nl;this.referenceCoordinates||(this.referenceCoordinates=R);const M=p(c,{active:o,context:s.current,currentCoordinates:R});if(M){const q=Ki(M,R),H={x:0,y:0},{scrollableAncestors:X}=s.current;for(const B of X){const L=c.code,{isTop:K,isRight:Z,isLeft:w,isBottom:P,maxScroll:J,minScroll:tt}=dv(B),G=M0(B),et={x:Math.min(L===Mt.Right?G.right-G.width/2:G.right,Math.max(L===Mt.Right?G.left:G.left+G.width/2,M.x)),y:Math.min(L===Mt.Down?G.bottom-G.height/2:G.bottom,Math.max(L===Mt.Down?G.top:G.top+G.height/2,M.y))},vt=L===Mt.Right&&!Z||L===Mt.Left&&!w,_t=L===Mt.Down&&!P||L===Mt.Up&&!K;if(vt&&et.x!==M.x){const gt=B.scrollLeft+q.x,Rt=L===Mt.Right&&gt<=J.x||L===Mt.Left&&gt>=tt.x;if(Rt&&!q.y){B.scrollTo({left:gt,behavior:y});return}Rt?H.x=B.scrollLeft-gt:H.x=L===Mt.Right?B.scrollLeft-J.x:B.scrollLeft-tt.x,H.x&&B.scrollBy({left:-H.x,behavior:y});break}else if(_t&&et.y!==M.y){const gt=B.scrollTop+q.y,Rt=L===Mt.Down&&gt<=J.y||L===Mt.Up&&gt>=tt.y;if(Rt&&!q.x){B.scrollTo({top:gt,behavior:y});return}Rt?H.y=B.scrollTop-gt:H.y=L===Mt.Down?B.scrollTop-J.y:B.scrollTop-tt.y,H.y&&B.scrollBy({top:-H.y,behavior:y});break}}this.handleMove(c,va(Ki(M,this.referenceCoordinates),H))}}}handleMove(c,o){const{onMove:s}=this.props;c.preventDefault(),s(o)}handleEnd(c){const{onEnd:o}=this.props;c.preventDefault(),this.detach(),o()}handleCancel(c){const{onCancel:o}=this.props;c.preventDefault(),this.detach(),o()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}}Bo.activators=[{eventName:"onKeyDown",handler:(i,c,o)=>{let{keyboardCodes:s=mv,onActivation:d}=c,{active:h}=o;const{code:p}=i.nativeEvent;if(s.start.includes(p)){const y=h.activatorNode.current;return y&&i.target!==y?!1:(i.preventDefault(),d?.({event:i.nativeEvent}),!0)}return!1}}];function Kh(i){return!!(i&&"distance"in i)}function Jh(i){return!!(i&&"delay"in i)}class wo{constructor(c,o,s){var d;s===void 0&&(s=R0(c.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=c,this.events=o;const{event:h}=c,{target:p}=h;this.props=c,this.events=o,this.document=ya(p),this.documentListeners=new hu(this.document),this.listeners=new hu(s),this.windowListeners=new hu(he(p)),this.initialCoordinates=(d=gu(h))!=null?d:nl,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){const{events:c,props:{options:{activationConstraint:o,bypassActivationConstraint:s}}}=this;if(this.listeners.add(c.move.name,this.handleMove,{passive:!1}),this.listeners.add(c.end.name,this.handleEnd),c.cancel&&this.listeners.add(c.cancel.name,this.handleCancel),this.windowListeners.add($e.Resize,this.handleCancel),this.windowListeners.add($e.DragStart,Vh),this.windowListeners.add($e.VisibilityChange,this.handleCancel),this.windowListeners.add($e.ContextMenu,Vh),this.documentListeners.add($e.Keydown,this.handleKeydown),o){if(s!=null&&s({event:this.props.event,activeNode:this.props.activeNode,options:this.props.options}))return this.handleStart();if(Jh(o)){this.timeoutId=setTimeout(this.handleStart,o.delay),this.handlePending(o);return}if(Kh(o)){this.handlePending(o);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),this.timeoutId!==null&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handlePending(c,o){const{active:s,onPending:d}=this.props;d(s,c,this.initialCoordinates,o)}handleStart(){const{initialCoordinates:c}=this,{onStart:o}=this.props;c&&(this.activated=!0,this.documentListeners.add($e.Click,N0,{capture:!0}),this.removeTextSelection(),this.documentListeners.add($e.SelectionChange,this.removeTextSelection),o(c))}handleMove(c){var o;const{activated:s,initialCoordinates:d,props:h}=this,{onMove:p,options:{activationConstraint:y}}=h;if(!d)return;const m=(o=gu(c))!=null?o:nl,g=Ki(d,m);if(!s&&y){if(Kh(y)){if(y.tolerance!=null&&zo(g,y.tolerance))return this.handleCancel();if(zo(g,y.distance))return this.handleStart()}if(Jh(y)&&zo(g,y.tolerance))return this.handleCancel();this.handlePending(y,g);return}c.cancelable&&c.preventDefault(),p(m)}handleEnd(){const{onAbort:c,onEnd:o}=this.props;this.detach(),this.activated||c(this.props.active),o()}handleCancel(){const{onAbort:c,onCancel:o}=this.props;this.detach(),this.activated||c(this.props.active),o()}handleKeydown(c){c.code===Mt.Esc&&this.handleCancel()}removeTextSelection(){var c;(c=this.document.getSelection())==null||c.removeAllRanges()}}const U0={cancel:{name:"pointercancel"},move:{name:"pointermove"},end:{name:"pointerup"}};class jo extends wo{constructor(c){const{event:o}=c,s=ya(o.target);super(c,U0,s)}}jo.activators=[{eventName:"onPointerDown",handler:(i,c)=>{let{nativeEvent:o}=i,{onActivation:s}=c;return!o.isPrimary||o.button!==0?!1:(s?.({event:o}),!0)}}];const H0={move:{name:"mousemove"},end:{name:"mouseup"}};var Oo;(function(i){i[i.RightClick=2]="RightClick"})(Oo||(Oo={}));class q0 extends wo{constructor(c){super(c,H0,ya(c.event.target))}}q0.activators=[{eventName:"onMouseDown",handler:(i,c)=>{let{nativeEvent:o}=i,{onActivation:s}=c;return o.button===Oo.RightClick?!1:(s?.({event:o}),!0)}}];const Ao={cancel:{name:"touchcancel"},move:{name:"touchmove"},end:{name:"touchend"}};class B0 extends wo{constructor(c){super(c,Ao)}static setup(){return window.addEventListener(Ao.move.name,c,{capture:!1,passive:!1}),function(){window.removeEventListener(Ao.move.name,c)};function c(){}}}B0.activators=[{eventName:"onTouchStart",handler:(i,c)=>{let{nativeEvent:o}=i,{onActivation:s}=c;const{touches:d}=o;return d.length>1?!1:(s?.({event:o}),!0)}}];var vu;(function(i){i[i.Pointer=0]="Pointer",i[i.DraggableRect=1]="DraggableRect"})(vu||(vu={}));var ki;(function(i){i[i.TreeOrder=0]="TreeOrder",i[i.ReversedTreeOrder=1]="ReversedTreeOrder"})(ki||(ki={}));function w0(i){let{acceleration:c,activator:o=vu.Pointer,canScroll:s,draggingRect:d,enabled:h,interval:p=5,order:y=ki.TreeOrder,pointerCoordinates:m,scrollableAncestors:g,scrollableAncestorRects:R,delta:M,threshold:q}=i;const H=Y0({delta:M,disabled:!h}),[X,B]=Fy(),L=C.useRef({x:0,y:0}),K=C.useRef({x:0,y:0}),Z=C.useMemo(()=>{switch(o){case vu.Pointer:return m?{top:m.y,bottom:m.y,left:m.x,right:m.x}:null;case vu.DraggableRect:return d}},[o,d,m]),w=C.useRef(null),P=C.useCallback(()=>{const tt=w.current;if(!tt)return;const G=L.current.x*K.current.x,et=L.current.y*K.current.y;tt.scrollBy(G,et)},[]),J=C.useMemo(()=>y===ki.TreeOrder?[...g].reverse():g,[y,g]);C.useEffect(()=>{if(!h||!g.length||!Z){B();return}for(const tt of J){if(s?.(tt)===!1)continue;const G=g.indexOf(tt),et=R[G];if(!et)continue;const{direction:vt,speed:_t}=D0(tt,et,Z,c,q);for(const gt of["x","y"])H[gt][vt[gt]]||(_t[gt]=0,vt[gt]=0);if(_t.x>0||_t.y>0){B(),w.current=tt,X(P,p),L.current=_t,K.current=vt;return}}L.current={x:0,y:0},K.current={x:0,y:0},B()},[c,P,s,B,h,p,JSON.stringify(Z),JSON.stringify(H),X,g,J,R,JSON.stringify(q)])}const j0={x:{[It.Backward]:!1,[It.Forward]:!1},y:{[It.Backward]:!1,[It.Forward]:!1}};function Y0(i){let{delta:c,disabled:o}=i;const s=Vi(c);return Eu(d=>{if(o||!s||!d)return j0;const h={x:Math.sign(c.x-s.x),y:Math.sign(c.y-s.y)};return{x:{[It.Backward]:d.x[It.Backward]||h.x===-1,[It.Forward]:d.x[It.Forward]||h.x===1},y:{[It.Backward]:d.y[It.Backward]||h.y===-1,[It.Forward]:d.y[It.Forward]||h.y===1}}},[o,c,s])}function G0(i,c){const o=c!=null?i.get(c):void 0,s=o?o.node.current:null;return Eu(d=>{var h;return c==null?null:(h=s??d)!=null?h:null},[s,c])}function X0(i,c){return C.useMemo(()=>i.reduce((o,s)=>{const{sensor:d}=s,h=d.activators.map(p=>({eventName:p.eventName,handler:c(p.handler,s)}));return[...o,...h]},[]),[i,c])}var Su;(function(i){i[i.Always=0]="Always",i[i.BeforeDragging=1]="BeforeDragging",i[i.WhileDragging=2]="WhileDragging"})(Su||(Su={}));var _o;(function(i){i.Optimized="optimized"})(_o||(_o={}));const kh=new Map;function Q0(i,c){let{dragging:o,dependencies:s,config:d}=c;const[h,p]=C.useState(null),{frequency:y,measure:m,strategy:g}=d,R=C.useRef(i),M=L(),q=yu(M),H=C.useCallback(function(K){K===void 0&&(K=[]),!q.current&&p(Z=>Z===null?K:Z.concat(K.filter(w=>!Z.includes(w))))},[q]),X=C.useRef(null),B=Eu(K=>{if(M&&!o)return kh;if(!K||K===kh||R.current!==i||h!=null){const Z=new Map;for(let w of i){if(!w)continue;if(h&&h.length>0&&!h.includes(w.id)&&w.rect.current){Z.set(w.id,w.rect.current);continue}const P=w.node.current,J=P?new qo(m(P),P):null;w.rect.current=J,J&&Z.set(w.id,J)}return Z}return K},[i,h,o,M,m]);return C.useEffect(()=>{R.current=i},[i]),C.useEffect(()=>{M||H()},[o,M]),C.useEffect(()=>{h&&h.length>0&&p(null)},[JSON.stringify(h)]),C.useEffect(()=>{M||typeof y!="number"||X.current!==null||(X.current=setTimeout(()=>{H(),X.current=null},y))},[y,M,H,...s]),{droppableRects:B,measureDroppableContainers:H,measuringScheduled:h!=null};function L(){switch(g){case Su.Always:return!1;case Su.BeforeDragging:return o;default:return!o}}}function Yo(i,c){return Eu(o=>i?o||(typeof c=="function"?c(i):i):null,[c,i])}function L0(i,c){return Yo(i,c)}function Z0(i){let{callback:c,disabled:o}=i;const s=Fi(c),d=C.useMemo(()=>{if(o||typeof window>"u"||typeof window.MutationObserver>"u")return;const{MutationObserver:h}=window;return new h(s)},[s,o]);return C.useEffect(()=>()=>d?.disconnect(),[d]),d}function Pi(i){let{callback:c,disabled:o}=i;const s=Fi(c),d=C.useMemo(()=>{if(o||typeof window>"u"||typeof window.ResizeObserver>"u")return;const{ResizeObserver:h}=window;return new h(s)},[o]);return C.useEffect(()=>()=>d?.disconnect(),[d]),d}function V0(i){return new qo(Tu(i),i)}function $h(i,c,o){c===void 0&&(c=V0);const[s,d]=C.useState(null);function h(){d(m=>{if(!i)return null;if(i.isConnected===!1){var g;return(g=m??o)!=null?g:null}const R=c(i);return JSON.stringify(m)===JSON.stringify(R)?m:R})}const p=Z0({callback(m){if(i)for(const g of m){const{type:R,target:M}=g;if(R==="childList"&&M instanceof HTMLElement&&M.contains(i)){h();break}}}}),y=Pi({callback:h});return Nl(()=>{h(),i?(y?.observe(i),p?.observe(document.body,{childList:!0,subtree:!0})):(y?.disconnect(),p?.disconnect())},[i]),s}function K0(i){const c=Yo(i);return iv(i,c)}const Wh=[];function J0(i){const c=C.useRef(i),o=Eu(s=>i?s&&s!==Wh&&i&&c.current&&i.parentNode===c.current.parentNode?s:Ho(i):Wh,[i]);return C.useEffect(()=>{c.current=i},[i]),o}function k0(i){const[c,o]=C.useState(null),s=C.useRef(i),d=C.useCallback(h=>{const p=To(h.target);p&&o(y=>y?(y.set(p,Co(p)),new Map(y)):null)},[]);return C.useEffect(()=>{const h=s.current;if(i!==h){p(h);const y=i.map(m=>{const g=To(m);return g?(g.addEventListener("scroll",d,{passive:!0}),[g,Co(g)]):null}).filter(m=>m!=null);o(y.length?new Map(y):null),s.current=i}return()=>{p(i),p(h)};function p(y){y.forEach(m=>{const g=To(m);g?.removeEventListener("scroll",d)})}},[d,i]),C.useMemo(()=>i.length?c?Array.from(c.values()).reduce((h,p)=>va(h,p),nl):hv(i):nl,[i,c])}function Fh(i,c){c===void 0&&(c=[]);const o=C.useRef(null);return C.useEffect(()=>{o.current=null},c),C.useEffect(()=>{const s=i!==nl;s&&!o.current&&(o.current=i),!s&&o.current&&(o.current=null)},[i]),o.current?Ki(i,o.current):nl}function $0(i){C.useEffect(()=>{if(!Wi)return;const c=i.map(o=>{let{sensor:s}=o;return s.setup==null?void 0:s.setup()});return()=>{for(const o of c)o?.()}},i.map(c=>{let{sensor:o}=c;return o}))}function W0(i,c){return C.useMemo(()=>i.reduce((o,s)=>{let{eventName:d,handler:h}=s;return o[d]=p=>{h(p,c)},o},{}),[i,c])}function yv(i){return C.useMemo(()=>i?E0(i):null,[i])}const Ih=[];function F0(i,c){c===void 0&&(c=Tu);const[o]=i,s=yv(o?he(o):null),[d,h]=C.useState(Ih);function p(){h(()=>i.length?i.map(m=>rv(m)?s:new qo(c(m),m)):Ih)}const y=Pi({callback:p});return Nl(()=>{y?.disconnect(),p(),i.forEach(m=>y?.observe(m))},[i]),d}function gv(i){if(!i)return null;if(i.children.length>1)return i;const c=i.children[0];return pu(c)?c:i}function I0(i){let{measure:c}=i;const[o,s]=C.useState(null),d=C.useCallback(g=>{for(const{target:R}of g)if(pu(R)){s(M=>{const q=c(R);return M?{...M,width:q.width,height:q.height}:q});break}},[c]),h=Pi({callback:d}),p=C.useCallback(g=>{const R=gv(g);h?.disconnect(),R&&h?.observe(R),s(R?c(R):null)},[c,h]),[y,m]=Zi(p);return C.useMemo(()=>({nodeRef:y,rect:o,setRef:m}),[o,y,m])}const P0=[{sensor:jo,options:{}},{sensor:Bo,options:{}}],tg={current:{}},Li={draggable:{measure:Zh},droppable:{measure:Zh,strategy:Su.WhileDragging,frequency:_o.Optimized},dragOverlay:{measure:Tu}};class mu extends Map{get(c){var o;return c!=null&&(o=super.get(c))!=null?o:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(c=>{let{disabled:o}=c;return!o})}getNodeFor(c){var o,s;return(o=(s=this.get(c))==null?void 0:s.node.current)!=null?o:void 0}}const eg={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new mu,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:Ji},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:Li,measureDroppableContainers:Ji,windowRect:null,measuringScheduled:!1},bv={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:Ji,draggableNodes:new Map,over:null,measureDroppableContainers:Ji},zu=C.createContext(bv),Sv=C.createContext(eg);function lg(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new mu}}}function ng(i,c){switch(c.type){case Kt.DragStart:return{...i,draggable:{...i.draggable,initialCoordinates:c.initialCoordinates,active:c.active}};case Kt.DragMove:return i.draggable.active==null?i:{...i,draggable:{...i.draggable,translate:{x:c.coordinates.x-i.draggable.initialCoordinates.x,y:c.coordinates.y-i.draggable.initialCoordinates.y}}};case Kt.DragEnd:case Kt.DragCancel:return{...i,draggable:{...i.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case Kt.RegisterDroppable:{const{element:o}=c,{id:s}=o,d=new mu(i.droppable.containers);return d.set(s,o),{...i,droppable:{...i.droppable,containers:d}}}case Kt.SetDroppableDisabled:{const{id:o,key:s,disabled:d}=c,h=i.droppable.containers.get(o);if(!h||s!==h.key)return i;const p=new mu(i.droppable.containers);return p.set(o,{...h,disabled:d}),{...i,droppable:{...i.droppable,containers:p}}}case Kt.UnregisterDroppable:{const{id:o,key:s}=c,d=i.droppable.containers.get(o);if(!d||s!==d.key)return i;const h=new mu(i.droppable.containers);return h.delete(o),{...i,droppable:{...i.droppable,containers:h}}}default:return i}}function ag(i){let{disabled:c}=i;const{active:o,activatorEvent:s,draggableNodes:d}=C.useContext(zu),h=Vi(s),p=Vi(o?.id);return C.useEffect(()=>{if(!c&&!s&&h&&p!=null){if(!Uo(h)||document.activeElement===h.target)return;const y=d.get(p);if(!y)return;const{activatorNode:m,node:g}=y;if(!m.current&&!g.current)return;requestAnimationFrame(()=>{for(const R of[m.current,g.current]){if(!R)continue;const M=t0(R);if(M){M.focus();break}}})}},[s,c,d,p,h]),null}function pv(i,c){let{transform:o,...s}=c;return i!=null&&i.length?i.reduce((d,h)=>h({transform:d,...s}),o):o}function ug(i){return C.useMemo(()=>({draggable:{...Li.draggable,...i?.draggable},droppable:{...Li.droppable,...i?.droppable},dragOverlay:{...Li.dragOverlay,...i?.dragOverlay}}),[i?.draggable,i?.droppable,i?.dragOverlay])}function ig(i){let{activeNode:c,measure:o,initialRect:s,config:d=!0}=i;const h=C.useRef(!1),{x:p,y}=typeof d=="boolean"?{x:d,y:d}:d;Nl(()=>{if(!p&&!y||!c){h.current=!1;return}if(h.current||!s)return;const g=c?.node.current;if(!g||g.isConnected===!1)return;const R=o(g),M=iv(R,s);if(p||(M.x=0),y||(M.y=0),h.current=!0,Math.abs(M.x)>0||Math.abs(M.y)>0){const q=fv(g);q&&q.scrollBy({top:M.y,left:M.x})}},[c,p,y,s,o])}const tc=C.createContext({...nl,scaleX:1,scaleY:1});var un;(function(i){i[i.Uninitialized=0]="Uninitialized",i[i.Initializing=1]="Initializing",i[i.Initialized=2]="Initialized"})(un||(un={}));const cg=C.memo(function(c){var o,s,d,h;let{id:p,accessibility:y,autoScroll:m=!0,children:g,sensors:R=P0,collisionDetection:M=m0,measuring:q,modifiers:H,...X}=c;const B=C.useReducer(ng,void 0,lg),[L,K]=B,[Z,w]=i0(),[P,J]=C.useState(un.Uninitialized),tt=P===un.Initialized,{draggable:{active:G,nodes:et,translate:vt},droppable:{containers:_t}}=L,gt=G!=null?et.get(G):null,Rt=C.useRef({initial:null,translated:null}),Pt=C.useMemo(()=>{var qt;return G!=null?{id:G,data:(qt=gt?.data)!=null?qt:tg,rect:Rt}:null},[G,gt]),Jt=C.useRef(null),[ve,_]=C.useState(null),[j,F]=C.useState(null),ot=yu(X,Object.values(X)),bt=Ii("DndDescribedBy",p),S=C.useMemo(()=>_t.getEnabled(),[_t]),N=ug(q),{droppableRects:Y,measureDroppableContainers:Q,measuringScheduled:nt}=Q0(S,{dragging:tt,dependencies:[vt.x,vt.y],config:N.droppable}),lt=G0(et,G),ht=C.useMemo(()=>j?gu(j):null,[j]),kt=_n(),Nt=L0(lt,N.draggable.measure);ig({activeNode:G!=null?et.get(G):null,config:kt.layoutShiftCompensation,initialRect:Nt,measure:N.draggable.measure});const te=$h(lt,N.draggable.measure,Nt),xl=$h(lt?lt.parentElement:null),We=C.useRef({activatorEvent:null,active:null,activeNode:lt,collisionRect:null,collisions:null,droppableRects:Y,draggableNodes:et,draggingNode:null,draggingNodeRect:null,droppableContainers:_t,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),ba=_t.getNodeFor((o=We.current.over)==null?void 0:o.id),se=I0({measure:N.dragOverlay.measure}),Ul=(s=se.nodeRef.current)!=null?s:lt,al=tt?(d=se.rect)!=null?d:te:null,Du=!!(se.nodeRef.current&&se.rect),Sa=K0(Du?null:te),cn=yv(Ul?he(Ul):null),He=J0(tt?ba??lt:null),Hl=F0(He),Cn=pv(H,{transform:{x:vt.x-Sa.x,y:vt.y-Sa.y,scaleX:1,scaleY:1},activatorEvent:j,active:Pt,activeNodeRect:te,containerNodeRect:xl,draggingNodeRect:al,over:We.current.over,overlayNodeRect:se.rect,scrollableAncestors:He,scrollableAncestorRects:Hl,windowRect:cn}),Mu=ht?va(ht,vt):null,ie=k0(He),ec=Fh(ie),Cu=Fh(ie,[te]),sl=va(Cn,ec),Fe=al?b0(al,Cn):null,fn=Pt&&Fe?M({active:Pt,collisionRect:Fe,droppableRects:Y,droppableContainers:S,pointerCoordinates:Mu}):null,pa=h0(fn,"id"),[ul,Ou]=C.useState(null),on=Du?Cn:va(Cn,Cu),re=y0(on,(h=ul?.rect)!=null?h:null,te),qe=C.useRef(null),ce=C.useCallback((qt,Bt)=>{let{sensor:$t,options:me}=Bt;if(Jt.current==null)return;const ye=et.get(Jt.current);if(!ye)return;const fe=qt.nativeEvent,ge=new $t({active:Jt.current,activeNode:ye,event:fe,options:me,context:We,onAbort(Zt){if(!et.get(Zt))return;const{onDragAbort:Me}=ot.current,we={id:Zt};Me?.(we),Z({type:"onDragAbort",event:we})},onPending(Zt,Ie,Me,we){if(!et.get(Zt))return;const{onDragPending:dl}=ot.current,Pe={id:Zt,constraint:Ie,initialCoordinates:Me,offset:we};dl?.(Pe),Z({type:"onDragPending",event:Pe})},onStart(Zt){const Ie=Jt.current;if(Ie==null)return;const Me=et.get(Ie);if(!Me)return;const{onDragStart:we}=ot.current,rl={activatorEvent:fe,active:{id:Ie,data:Me.data,rect:Rt}};du.unstable_batchedUpdates(()=>{we?.(rl),J(un.Initializing),K({type:Kt.DragStart,initialCoordinates:Zt,active:Ie}),Z({type:"onDragStart",event:rl}),_(qe.current),F(fe)})},onMove(Zt){K({type:Kt.DragMove,coordinates:Zt})},onEnd:Be(Kt.DragEnd),onCancel:Be(Kt.DragCancel)});qe.current=ge;function Be(Zt){return async function(){const{active:Me,collisions:we,over:rl,scrollAdjustedTranslate:dl}=We.current;let Pe=null;if(Me&&dl){const{cancelDrop:ql}=ot.current;Pe={activatorEvent:fe,active:Me,collisions:we,delta:dl,over:rl},Zt===Kt.DragEnd&&typeof ql=="function"&&await Promise.resolve(ql(Pe))&&(Zt=Kt.DragCancel)}Jt.current=null,du.unstable_batchedUpdates(()=>{K({type:Zt}),J(un.Uninitialized),Ou(null),_(null),F(null),qe.current=null;const ql=Zt===Kt.DragEnd?"onDragEnd":"onDragCancel";if(Pe){const je=ot.current[ql];je?.(Pe),Z({type:ql,event:Pe})}})}}},[et]),lc=C.useCallback((qt,Bt)=>($t,me)=>{const ye=$t.nativeEvent,fe=et.get(me);if(Jt.current!==null||!fe||ye.dndKit||ye.defaultPrevented)return;const ge={active:fe};qt($t,Bt.options,ge)===!0&&(ye.dndKit={capturedBy:Bt.sensor},Jt.current=me,ce($t,Bt))},[et,ce]),_u=X0(R,lc);$0(R),Nl(()=>{te&&P===un.Initializing&&J(un.Initialized)},[te,P]),C.useEffect(()=>{const{onDragMove:qt}=ot.current,{active:Bt,activatorEvent:$t,collisions:me,over:ye}=We.current;if(!Bt||!$t)return;const fe={active:Bt,activatorEvent:$t,collisions:me,delta:{x:sl.x,y:sl.y},over:ye};du.unstable_batchedUpdates(()=>{qt?.(fe),Z({type:"onDragMove",event:fe})})},[sl.x,sl.y]),C.useEffect(()=>{const{active:qt,activatorEvent:Bt,collisions:$t,droppableContainers:me,scrollAdjustedTranslate:ye}=We.current;if(!qt||Jt.current==null||!Bt||!ye)return;const{onDragOver:fe}=ot.current,ge=me.get(pa),Be=ge&&ge.rect.current?{id:ge.id,rect:ge.rect.current,data:ge.data,disabled:ge.disabled}:null,Zt={active:qt,activatorEvent:Bt,collisions:$t,delta:{x:ye.x,y:ye.y},over:Be};du.unstable_batchedUpdates(()=>{Ou(Be),fe?.(Zt),Z({type:"onDragOver",event:Zt})})},[pa]),Nl(()=>{We.current={activatorEvent:j,active:Pt,activeNode:lt,collisionRect:Fe,collisions:fn,droppableRects:Y,draggableNodes:et,draggingNode:Ul,draggingNodeRect:al,droppableContainers:_t,over:ul,scrollableAncestors:He,scrollAdjustedTranslate:sl},Rt.current={initial:al,translated:Fe}},[Pt,lt,fn,Fe,et,Ul,al,Y,_t,ul,He,sl]),w0({...kt,delta:vt,draggingRect:Fe,pointerCoordinates:Mu,scrollableAncestors:He,scrollableAncestorRects:Hl});const nc=C.useMemo(()=>({active:Pt,activeNode:lt,activeNodeRect:te,activatorEvent:j,collisions:fn,containerNodeRect:xl,dragOverlay:se,draggableNodes:et,droppableContainers:_t,droppableRects:Y,over:ul,measureDroppableContainers:Q,scrollableAncestors:He,scrollableAncestorRects:Hl,measuringConfiguration:N,measuringScheduled:nt,windowRect:cn}),[Pt,lt,te,j,fn,xl,se,et,_t,Y,ul,Q,He,Hl,N,nt,cn]),On=C.useMemo(()=>({activatorEvent:j,activators:_u,active:Pt,activeNodeRect:te,ariaDescribedById:{draggable:bt},dispatch:K,draggableNodes:et,over:ul,measureDroppableContainers:Q}),[j,_u,Pt,te,K,bt,et,ul,Q]);return Yt.createElement(uv.Provider,{value:w},Yt.createElement(zu.Provider,{value:On},Yt.createElement(Sv.Provider,{value:nc},Yt.createElement(tc.Provider,{value:re},g)),Yt.createElement(ag,{disabled:y?.restoreFocus===!1})),Yt.createElement(o0,{...y,hiddenTextDescribedById:bt}));function _n(){const qt=ve?.autoScrollEnabled===!1,Bt=typeof m=="object"?m.enabled===!1:m===!1,$t=tt&&!qt&&!Bt;return typeof m=="object"?{...m,enabled:$t}:{enabled:$t}}}),fg=C.createContext(null),Ph="button",og="Draggable";function sg(i){let{id:c,data:o,disabled:s=!1,attributes:d}=i;const h=Ii(og),{activators:p,activatorEvent:y,active:m,activeNodeRect:g,ariaDescribedById:R,draggableNodes:M,over:q}=C.useContext(zu),{role:H=Ph,roleDescription:X="draggable",tabIndex:B=0}=d??{},L=m?.id===c,K=C.useContext(L?tc:fg),[Z,w]=Zi(),[P,J]=Zi(),tt=W0(p,c),G=yu(o);Nl(()=>(M.set(c,{id:c,key:h,node:Z,activatorNode:P,data:G}),()=>{const vt=M.get(c);vt&&vt.key===h&&M.delete(c)}),[M,c]);const et=C.useMemo(()=>({role:H,tabIndex:B,"aria-disabled":s,"aria-pressed":L&&H===Ph?!0:void 0,"aria-roledescription":X,"aria-describedby":R.draggable}),[s,H,B,L,X,R.draggable]);return{active:m,activatorEvent:y,activeNodeRect:g,attributes:et,isDragging:L,listeners:s?void 0:tt,node:Z,over:q,setNodeRef:w,setActivatorNodeRef:J,transform:K}}function rg(){return C.useContext(Sv)}const dg="Droppable",hg={timeout:25};function vg(i){let{data:c,disabled:o=!1,id:s,resizeObserverConfig:d}=i;const h=Ii(dg),{active:p,dispatch:y,over:m,measureDroppableContainers:g}=C.useContext(zu),R=C.useRef({disabled:o}),M=C.useRef(!1),q=C.useRef(null),H=C.useRef(null),{disabled:X,updateMeasurementsFor:B,timeout:L}={...hg,...d},K=yu(B??s),Z=C.useCallback(()=>{if(!M.current){M.current=!0;return}H.current!=null&&clearTimeout(H.current),H.current=setTimeout(()=>{g(Array.isArray(K.current)?K.current:[K.current]),H.current=null},L)},[L]),w=Pi({callback:Z,disabled:X||!p}),P=C.useCallback((et,vt)=>{w&&(vt&&(w.unobserve(vt),M.current=!1),et&&w.observe(et))},[w]),[J,tt]=Zi(P),G=yu(c);return C.useEffect(()=>{!w||!J.current||(w.disconnect(),M.current=!1,w.observe(J.current))},[J,w]),C.useEffect(()=>(y({type:Kt.RegisterDroppable,element:{id:s,key:h,disabled:o,node:J,rect:q,data:G}}),()=>y({type:Kt.UnregisterDroppable,key:h,id:s})),[s]),C.useEffect(()=>{o!==R.current.disabled&&(y({type:Kt.SetDroppableDisabled,id:s,key:h,disabled:o}),R.current.disabled=o)},[s,h,o,y]),{active:p,rect:q,isOver:m?.id===s,node:J,over:m,setNodeRef:tt}}function mg(i){let{animation:c,children:o}=i;const[s,d]=C.useState(null),[h,p]=C.useState(null),y=Vi(o);return!o&&!s&&y&&d(y),Nl(()=>{if(!h)return;const m=s?.key,g=s?.props.id;if(m==null||g==null){d(null);return}Promise.resolve(c(g,h)).then(()=>{d(null)})},[c,s,h]),Yt.createElement(Yt.Fragment,null,o,s?C.cloneElement(s,{ref:p}):null)}const yg={x:0,y:0,scaleX:1,scaleY:1};function gg(i){let{children:c}=i;return Yt.createElement(zu.Provider,{value:bv},Yt.createElement(tc.Provider,{value:yg},c))}const bg={position:"fixed",touchAction:"none"},Sg=i=>Uo(i)?"transform 250ms ease":void 0,pg=C.forwardRef((i,c)=>{let{as:o,activatorEvent:s,adjustScale:d,children:h,className:p,rect:y,style:m,transform:g,transition:R=Sg}=i;if(!y)return null;const M=d?g:{...g,scaleX:1,scaleY:1},q={...bg,width:y.width,height:y.height,top:y.top,left:y.left,transform:bu.Transform.toString(M),transformOrigin:d&&s?r0(s,y):void 0,transition:typeof R=="function"?R(s):R,...m};return Yt.createElement(o,{className:p,style:q,ref:c},h)}),Eg=i=>c=>{let{active:o,dragOverlay:s}=c;const d={},{styles:h,className:p}=i;if(h!=null&&h.active)for(const[y,m]of Object.entries(h.active))m!==void 0&&(d[y]=o.node.style.getPropertyValue(y),o.node.style.setProperty(y,m));if(h!=null&&h.dragOverlay)for(const[y,m]of Object.entries(h.dragOverlay))m!==void 0&&s.node.style.setProperty(y,m);return p!=null&&p.active&&o.node.classList.add(p.active),p!=null&&p.dragOverlay&&s.node.classList.add(p.dragOverlay),function(){for(const[m,g]of Object.entries(d))o.node.style.setProperty(m,g);p!=null&&p.active&&o.node.classList.remove(p.active)}},Tg=i=>{let{transform:{initial:c,final:o}}=i;return[{transform:bu.Transform.toString(c)},{transform:bu.Transform.toString(o)}]},zg={duration:250,easing:"ease",keyframes:Tg,sideEffects:Eg({styles:{active:{opacity:"0"}}})};function Ag(i){let{config:c,draggableNodes:o,droppableContainers:s,measuringConfiguration:d}=i;return Fi((h,p)=>{if(c===null)return;const y=o.get(h);if(!y)return;const m=y.node.current;if(!m)return;const g=gv(p);if(!g)return;const{transform:R}=he(p).getComputedStyle(p),M=cv(R);if(!M)return;const q=typeof c=="function"?c:Dg(c);return vv(m,d.draggable.measure),q({active:{id:h,data:y.data,node:m,rect:d.draggable.measure(m)},draggableNodes:o,dragOverlay:{node:p,rect:d.dragOverlay.measure(g)},droppableContainers:s,measuringConfiguration:d,transform:M})})}function Dg(i){const{duration:c,easing:o,sideEffects:s,keyframes:d}={...zg,...i};return h=>{let{active:p,dragOverlay:y,transform:m,...g}=h;if(!c)return;const R={x:y.rect.left-p.rect.left,y:y.rect.top-p.rect.top},M={scaleX:m.scaleX!==1?p.rect.width*m.scaleX/y.rect.width:1,scaleY:m.scaleY!==1?p.rect.height*m.scaleY/y.rect.height:1},q={x:m.x-R.x,y:m.y-R.y,...M},H=d({...g,active:p,dragOverlay:y,transform:{initial:m,final:q}}),[X]=H,B=H[H.length-1];if(JSON.stringify(X)===JSON.stringify(B))return;const L=s?.({active:p,dragOverlay:y,...g}),K=y.node.animate(H,{duration:c,easing:o,fill:"forwards"});return new Promise(Z=>{K.onfinish=()=>{L?.(),Z()}})}}let tv=0;function Mg(i){return C.useMemo(()=>{if(i!=null)return tv++,tv},[i])}const Cg=Yt.memo(i=>{let{adjustScale:c=!1,children:o,dropAnimation:s,style:d,transition:h,modifiers:p,wrapperElement:y="div",className:m,zIndex:g=999}=i;const{activatorEvent:R,active:M,activeNodeRect:q,containerNodeRect:H,draggableNodes:X,droppableContainers:B,dragOverlay:L,over:K,measuringConfiguration:Z,scrollableAncestors:w,scrollableAncestorRects:P,windowRect:J}=rg(),tt=C.useContext(tc),G=Mg(M?.id),et=pv(p,{activatorEvent:R,active:M,activeNodeRect:q,containerNodeRect:H,draggingNodeRect:L.rect,over:K,overlayNodeRect:L.rect,scrollableAncestors:w,scrollableAncestorRects:P,transform:tt,windowRect:J}),vt=Yo(q),_t=Ag({config:s,draggableNodes:X,droppableContainers:B,measuringConfiguration:Z}),gt=vt?L.setRef:void 0;return Yt.createElement(gg,null,Yt.createElement(mg,{animation:_t},M&&G?Yt.createElement(pg,{key:G,id:M.id,ref:gt,as:y,activatorEvent:R,adjustScale:c,className:m,transition:h,rect:vt,style:{zIndex:g,...d},transform:et},o):null))}),Og=i=>{let{activatorEvent:c,draggingNodeRect:o,transform:s}=i;if(o&&c){const d=gu(c);if(!d)return s;const h=d.x-o.left,p=d.y-o.top;return{...s,x:s.x+h-o.width/2,y:s.y+p-o.height/2}}return s},Qi=(i,c,o,s)=>({id:s||`${c}-${i}-${o.row}-${o.col}`,type:i,color:c,position:o,hasMoved:!1}),_g=()=>{const i=Array(8).fill(null).map(()=>Array(8).fill(null));["rook","knight","bishop","queen","king","bishop","knight","rook"].forEach((s,d)=>{i[7][d]=Qi(s,"white",{row:7,col:d})});for(let s=0;s<8;s++)i[6][s]=Qi("pawn","white",{row:6,col:s});["rook","knight","bishop","queen","king","bishop","knight","rook"].forEach((s,d)=>{i[0][d]=Qi(s,"black",{row:0,col:d})});for(let s=0;s<8;s++)i[1][s]=Qi("pawn","black",{row:1,col:s});return i},ev=()=>({board:_g(),currentPlayer:"white",moveHistory:[],isCheck:!1,isCheckmate:!1,isStalemate:!1,canCastleKingside:{white:!0,black:!0},canCastleQueenside:{white:!0,black:!0},enPassantTarget:null,halfMoveClock:0,fullMoveNumber:1}),De=(i,c)=>c.row<0||c.row>=8||c.col<0||c.col>=8?null:i[c.row][c.col],Ev=(i,c)=>{for(let o=0;o<8;o++)for(let s=0;s<8;s++){const d=i[o][s];if(d&&d.type==="king"&&d.color===c)return{row:o,col:s}}return null},Au=i=>({...i,board:i.board.map(c=>[...c]),moveHistory:[...i.moveHistory],canCastleKingside:{...i.canCastleKingside},canCastleQueenside:{...i.canCastleQueenside},enPassantTarget:i.enPassantTarget?{...i.enPassantTarget}:null}),Rg=i=>i.row>=0&&i.row<8&&i.col>=0&&i.col<8,Tv=(i,c,o)=>{const s=o.row-c.row,d=o.col-c.col,h=s===0?0:s/Math.abs(s),p=d===0?0:d/Math.abs(d);let y=c.row+h,m=c.col+p;for(;y!==o.row||m!==o.col;){if(i[y][m]!==null)return!1;y+=h,m+=p}return!0},zv=(i,c,o,s)=>{const d=c.position,h=c.color==="white"?-1:1,p=c.color==="white"?6:1,y=o.row-d.row,m=Math.abs(o.col-d.col);if(m===0&&(y===h&&!De(i,o)||y===2*h&&d.row===p&&!De(i,o)))return!0;if(m===1&&y===h){const g=De(i,o);if(g&&g.color!==c.color||s.enPassantTarget&&o.row===s.enPassantTarget.row&&o.col===s.enPassantTarget.col)return!0}return!1},Go=(i,c,o)=>{const s=c.position;return s.row!==o.row&&s.col!==o.col?!1:Tv(i,s,o)},Xo=(i,c,o)=>{const s=c.position,d=Math.abs(o.row-s.row),h=Math.abs(o.col-s.col);return d!==h?!1:Tv(i,s,o)},Av=(i,c)=>{const o=i.position,s=Math.abs(c.row-o.row),d=Math.abs(c.col-o.col);return s===2&&d===1||s===1&&d===2},Dv=(i,c,o)=>Go(i,c,o)||Xo(i,c,o),Mv=(i,c)=>{const o=i.position,s=Math.abs(c.row-o.row),d=Math.abs(c.col-o.col);return s<=1&&d<=1&&(s!==0||d!==0)},Cv=(i,c,o,s)=>{for(let d=0;d<8;d++)for(let h=0;h<8;h++){const p=i[d][h];if(p&&p.color===o){const y={...p,position:{row:d,col:h}};switch(p.type){case"pawn":if(zv(i,y,c,s))return!0;break;case"rook":if(Go(i,y,c))return!0;break;case"bishop":if(Xo(i,y,c))return!0;break;case"knight":if(Av(y,c))return!0;break;case"queen":if(Dv(i,y,c))return!0;break;case"king":if(Mv(y,c))return!0;break}}}return!1},ga=(i,c,o)=>{const s=Ev(i,c);return s?Cv(i,s,c==="white"?"black":"white",o):!1},$i=(i,c,o)=>{const{board:s}=i,d=c.position;if(!Rg(o))return{isValid:!1,reason:"Posición fuera del tablero"};if(d.row===o.row&&d.col===o.col)return{isValid:!1,reason:"No se puede mover a la misma posición"};const h=De(s,o);if(h&&h.color===c.color)return{isValid:!1,reason:"No se puede capturar pieza propia"};let p=!1;switch(c.type){case"pawn":p=zv(s,c,o,i);break;case"rook":p=Go(s,c,o);break;case"bishop":p=Xo(s,c,o);break;case"knight":p=Av(c,o);break;case"queen":p=Dv(s,c,o);break;case"king":p=Mv(c,o);break}if(!p)return{isValid:!1,reason:"Movimiento inválido para esta pieza"};const y=Au(i),m=y.board;return m[d.row][d.col]=null,m[o.row][o.col]={...c,position:o},ga(m,c.color,y)?{isValid:!1,reason:"El movimiento deja al rey en jaque",wouldBeInCheck:!0}:{isValid:!0}},Do=(i,c)=>{const o=[];for(let s=0;s<8;s++)for(let d=0;d<8;d++){const h={row:s,col:d};$i(i,c,h).isValid&&o.push(h)}return o},Ov=({piece:i,isDragging:c=!1,isInteractive:o=!0})=>{const{attributes:s,listeners:d,setNodeRef:h,transform:p,isDragging:y}=sg({id:i.id,disabled:!o}),m=q=>({white:{king:"♔",queen:"♕",rook:"♖",bishop:"♗",knight:"♘",pawn:"♙"},black:{king:"♚",queen:"♛",rook:"♜",bishop:"♝",knight:"♞",pawn:"♟"}})[q.color][q.type],g=q=>{const H={king:"Rey",queen:"Reina",rook:"Torre",bishop:"Alfil",knight:"Caballo",pawn:"Peón"},X=q.color==="white"?"Blanco":"Negro";return`${H[q.type]} ${X}`},R=()=>{const q=["chess-piece"];return q.push(`chess-piece--${i.color}`),q.push(`chess-piece--${i.type}`),(c||y)&&q.push("chess-piece--dragging"),o&&q.push("chess-piece--interactive"),q.join(" ")},M=p?{transform:`translate3d(${p.x}px, ${p.y}px, 0)`,zIndex:1e3}:void 0;return $.jsx("div",{ref:h,className:R(),style:M,...d,...s,title:g(i),role:"button",tabIndex:o?0:-1,"aria-label":g(i),children:$.jsx("span",{className:"chess-piece__symbol",children:m(i)})})},Ng=({position:i,piece:c,isLight:o,isSelected:s=!1,isValidMove:d=!1,isLastMove:h=!1,isCheck:p=!1,onClick:y,isInteractive:m=!0})=>{const{setNodeRef:g,isOver:R}=vg({id:`${i.row}-${i.col}`}),M=()=>{const H=["chess-square"];return o?H.push("chess-square--light"):H.push("chess-square--dark"),s&&H.push("chess-square--selected"),d&&H.push("chess-square--valid-move"),h&&H.push("chess-square--last-move"),p&&H.push("chess-square--check"),R&&H.push("chess-square--drag-over"),m&&H.push("chess-square--interactive"),H.join(" ")},q=()=>{const H="abcdefgh",X="87654321";let B="";return i.row===7&&(B+=H[i.col]),i.col===0&&(B+=X[i.row]),B};return $.jsxs("div",{ref:g,className:M(),onClick:y,"data-position":`${i.row}-${i.col}`,children:[$.jsx("div",{className:"chess-square__coordinates",children:q()}),d&&!c&&$.jsx("div",{className:"chess-square__valid-move-indicator"}),d&&c&&$.jsx("div",{className:"chess-square__capture-indicator"}),c&&$.jsx(Ov,{piece:c,isInteractive:m})]})},xg=({gameState:i,onMove:c,onPieceSelect:o,showValidMoves:s=!0,isInteractive:d=!0})=>{const[h,p]=C.useState(null),[y,m]=C.useState(null),[g,R]=C.useState([]),M=s0(Lh(jo,{activationConstraint:{distance:8}}),Lh(Bo)),q=C.useCallback(Z=>{const{active:w}=Z,P=w.id,J=Ug(i.board,P);if(J&&J.color===i.currentPlayer){if(p(J),m(J),s){const tt=Do(i,J);R(tt)}o?.(J)}},[i,s,o]),H=C.useCallback(Z=>{const{over:w}=Z;if(p(null),R([]),!w||!h)return;const[P,J]=w.id.split("-").map(Number),tt={row:P,col:J};$i(i,h,tt).isValid&&c(h.position,tt)},[h,i,c]),X=C.useCallback(Z=>{if(!d)return;const w=i.board[Z.row][Z.col];if(y){if(w&&w.color===i.currentPlayer&&w.id===y.id)m(null),R([]),o?.(null);else if($i(i,y,Z).isValid)c(y.position,Z),m(null),R([]),o?.(null);else if(w&&w.color===i.currentPlayer){m(w);const J=s?Do(i,w):[];R(J),o?.(w)}}else if(w&&w.color===i.currentPlayer){m(w);const P=s?Do(i,w):[];R(P),o?.(w)}},[i,y,s,c,o,d]),B=C.useCallback(Z=>g.some(w=>w.row===Z.row&&w.col===Z.col),[g]),L=C.useCallback(Z=>{const w=i.moveHistory[i.moveHistory.length-1];return w?w.from.row===Z.row&&w.from.col===Z.col||w.to.row===Z.row&&w.to.col===Z.col:!1},[i.moveHistory]),K=()=>{const Z=[];for(let w=0;w<8;w++)for(let P=0;P<8;P++){const J={row:w,col:P},tt=i.board[w][P],G=(w+P)%2===0,et=!!(y&&y.position.row===w&&y.position.col===P);Z.push($.jsx(Ng,{position:J,piece:tt,isLight:G,isSelected:et,isValidMove:B(J),isLastMove:L(J),isCheck:i.isCheck&&tt?.type==="king"&&tt.color===i.currentPlayer,onClick:()=>X(J),isInteractive:d},`${w}-${P}`))}return Z};return $.jsxs(cg,{sensors:M,onDragStart:q,onDragEnd:H,modifiers:[Og],children:[$.jsx("div",{className:"chess-board",children:K()}),$.jsx(Cg,{children:h&&$.jsx(Ov,{piece:h,isDragging:!0})})]})},Ug=(i,c)=>{for(let o=0;o<8;o++)for(let s=0;s<8;s++){const d=i[o][s];if(d&&d.id===c)return d}return null},Hg=({gameStatus:i,onReset:c,onUndo:o,canUndo:s,moveCount:d})=>{const h=()=>{switch(i.status){case"checkmate":return`¡Jaque Mate! ${i.winner==="white"?"Blancas":"Negras"} ganan`;case"stalemate":return"¡Rey Ahogado! Empate";case"check":return`¡Jaque! ${i.player==="white"?"Blancas":"Negras"} en jaque`;case"draw":return i.reason==="50-move rule"?"Empate por regla de 50 movimientos":"Empate";case"playing":default:return`Turno de ${i.currentPlayer==="white"?"Blancas":"Negras"}`}},p=()=>{const m="game-status";switch(i.status){case"checkmate":return`${m} ${m}--checkmate`;case"stalemate":case"draw":return`${m} ${m}--draw`;case"check":return`${m} ${m}--check`;default:return`${m} ${m}--playing`}},y=()=>["checkmate","stalemate","draw"].includes(i.status);return $.jsxs("div",{className:"game-controls",children:[$.jsxs("div",{className:p(),children:[$.jsx("div",{className:"game-status__message",children:h()}),i.status==="playing"&&$.jsx("div",{className:"game-status__turn-indicator",children:$.jsx("div",{className:`turn-indicator turn-indicator--${i.currentPlayer}`,children:$.jsx("div",{className:"turn-indicator__piece",children:i.currentPlayer==="white"?"♔":"♚"})})})]}),$.jsx("div",{className:"game-info",children:$.jsxs("div",{className:"game-info__item",children:[$.jsx("span",{className:"game-info__label",children:"Movimientos:"}),$.jsx("span",{className:"game-info__value",children:Math.ceil(d/2)})]})}),$.jsxs("div",{className:"game-controls__buttons",children:[$.jsxs("button",{className:"game-button game-button--secondary",onClick:o,disabled:!s,title:"Deshacer último movimiento",children:[$.jsx("span",{className:"game-button__icon",children:"↶"}),"Deshacer"]}),$.jsxs("button",{className:"game-button game-button--primary",onClick:c,title:"Reiniciar juego",children:[$.jsx("span",{className:"game-button__icon",children:"⟲"}),"Nuevo Juego"]})]}),y()&&$.jsx("div",{className:"game-over-message",children:$.jsxs("div",{className:"game-over-message__content",children:[$.jsx("h3",{children:"¡Juego Terminado!"}),$.jsx("p",{children:h()}),$.jsx("button",{className:"game-button game-button--primary game-button--large",onClick:c,children:"Jugar de Nuevo"})]})})]})},qg=({color:i,onSelect:c,onCancel:o,isVisible:s})=>{if(!s)return null;const d=["queen","rook","bishop","knight"],h=(y,m)=>({white:{queen:"♕",rook:"♖",bishop:"♗",knight:"♘"},black:{queen:"♛",rook:"♜",bishop:"♝",knight:"♞"}})[m][y],p=y=>({queen:"Reina",rook:"Torre",bishop:"Alfil",knight:"Caballo"})[y];return $.jsx("div",{className:"promotion-dialog-overlay",children:$.jsxs("div",{className:"promotion-dialog",children:[$.jsxs("div",{className:"promotion-dialog__header",children:[$.jsx("h3",{children:"Promoción de Peón"}),$.jsx("p",{children:"Elige la pieza a la que quieres promover tu peón:"})]}),$.jsx("div",{className:"promotion-dialog__options",children:d.map(y=>$.jsxs("button",{className:`promotion-option promotion-option--${i}`,onClick:()=>c(y),title:p(y),children:[$.jsx("span",{className:"promotion-option__symbol",children:h(y,i)}),$.jsx("span",{className:"promotion-option__name",children:p(y)})]},y))}),$.jsx("div",{className:"promotion-dialog__actions",children:$.jsx("button",{className:"promotion-dialog__cancel",onClick:o,children:"Cancelar"})})]})})},Bg=(i,c,o)=>{const{board:s}=i;if(!(o==="kingside"?i.canCastleKingside[c]:i.canCastleQueenside[c]))return!1;const h=Ev(s,c);if(!h)return!1;const p=De(s,h);if(!p||p.hasMoved||ga(s,c,i))return!1;const y=c==="white"?7:0,m=o==="kingside"?7:0,g=4,R=De(s,{row:y,col:m});if(!R||R.type!=="rook"||R.color!==c||R.hasMoved)return!1;const M=Math.min(g,m),q=Math.max(g,m);for(let K=M+1;K<q;K++)if(De(s,{row:y,col:K}))return!1;const H=c==="white"?"black":"white",X=o==="kingside"?6:2,L=o==="kingside"?[X-1,X]:[X,o==="kingside"?5:3];for(const K of L)if(Cv(s,{row:y,col:K},H,i))return!1;return!0},wg=(i,c,o)=>{const s=Au(i),{board:d}=s,h=c==="white"?7:0,p=o==="kingside"?7:0,y=4,m=o==="kingside"?6:2,g=o==="kingside"?5:3,R=De(d,{row:h,col:y});R&&(d[h][y]=null,d[h][m]={...R,position:{row:h,col:m},hasMoved:!0});const M=De(d,{row:h,col:p});M&&(d[h][p]=null,d[h][g]={...M,position:{row:h,col:g},hasMoved:!0}),s.canCastleKingside[c]=!1,s.canCastleQueenside[c]=!1;const q={from:{row:h,col:y},to:{row:h,col:m},piece:R,isCastling:!0};return s.moveHistory.push(q),s},jg=(i,c,o)=>c.type!=="pawn"||!i.enPassantTarget?!1:o.row===i.enPassantTarget.row&&o.col===i.enPassantTarget.col,Yg=(i,c,o)=>{const s=Au(i),{board:d}=s,h=c.position;d[h.row][h.col]=null,d[o.row][o.col]={...c,position:o};const p=c.color==="white"?o.row+1:o.row-1,y=De(d,{row:p,col:o.col});d[p][o.col]=null;const m={from:h,to:o,piece:c,capturedPiece:y||void 0,isEnPassant:!0};return s.moveHistory.push(m),s.enPassantTarget=null,s},Gg=(i,c)=>{if(i.type!=="pawn")return!1;const o=i.color==="white"?0:7;return c.row===o},Xg=(i,c)=>ga(i.board,c,i)&&!_v(i,c),Qg=(i,c)=>!ga(i.board,c,i)&&!_v(i,c),_v=(i,c)=>{const{board:o}=i;for(let s=0;s<8;s++)for(let d=0;d<8;d++){const h=o[s][d];if(h&&h.color===c){for(let p=0;p<8;p++)for(let y=0;y<8;y++)if(Lg(i,h,{row:p,col:y}))return!0}}return!1},Lg=(i,c,o)=>{const{board:s}=i,d=c.position;if(o.row<0||o.row>=8||o.col<0||o.col>=8||d.row===o.row&&d.col===o.col)return!1;const h=De(s,o);if(h&&h.color===c.color)return!1;const p=Au(i),y=p.board;return y[d.row][d.col]=null,y[o.row][o.col]={...c,position:o},!ga(y,c.color,p)},Mo=(i,c)=>{const o=Au(i);return o.currentPlayer=o.currentPlayer==="white"?"black":"white",o.isCheck=ga(o.board,o.currentPlayer,o),o.isCheckmate=Xg(o,o.currentPlayer),o.isStalemate=Qg(o,o.currentPlayer),o.currentPlayer==="white"&&o.fullMoveNumber++,c.piece.type==="pawn"||c.capturedPiece?o.halfMoveClock=0:o.halfMoveClock++,o},Zg=()=>{const[i,c]=C.useState(ev()),[o,s]=C.useState(null),[d,h]=C.useState(null),p=C.useCallback((H,X)=>{const B=De(i.board,H);if(!B||B.color!==i.currentPlayer)return!1;if(B.type==="king"&&Math.abs(X.col-H.col)===2){const K=X.col>H.col?"kingside":"queenside";if(Bg(i,B.color,K)){const Z=wg(i,B.color,K),w=Mo(Z,{piece:B});return c(w),s(null),!0}return!1}if(jg(i,B,X)){const K=Yg(i,B,X),Z=Mo(K,{piece:B});return c(Z),s(null),!0}return $i(i,B,X).isValid?Gg(B,X)?(h({from:H,to:X,piece:B}),!0):y(H,X):!1},[i]),y=C.useCallback((H,X,B)=>{const L=De(i.board,H);if(!L)return!1;const K=De(i.board,X);let Z=i.board.map(G=>[...G]);Z[H.row][H.col]=null;const w={...L,position:X,hasMoved:!0,type:B||L.type};Z[X.row][X.col]=w;const P={from:H,to:X,piece:L,capturedPiece:K||void 0,isPromotion:!!B,promotionPiece:B};let J={...i,board:Z,moveHistory:[...i.moveHistory,P]};if(L.type==="pawn"&&Math.abs(X.row-H.row)===2){const G=L.color==="white"?H.row-1:H.row+1;J.enPassantTarget={row:G,col:H.col}}else J.enPassantTarget=null;L.type==="king"?(J.canCastleKingside[L.color]=!1,J.canCastleQueenside[L.color]=!1):L.type==="rook"&&(H.col===0?J.canCastleQueenside[L.color]=!1:H.col===7&&(J.canCastleKingside[L.color]=!1)),K&&K.type==="rook"&&(X.col===0?J.canCastleQueenside[K.color]=!1:X.col===7&&(J.canCastleKingside[K.color]=!1));const tt=Mo(J,P);return c(tt),s(null),!0},[i]),m=C.useCallback(H=>{if(!d)return;const{from:X,to:B}=d;y(X,B,H),h(null)},[d,y]),g=C.useCallback(()=>{h(null)},[]),R=C.useCallback(()=>{c(ev()),s(null),h(null)},[]),M=C.useCallback(()=>{if(i.moveHistory.length===0)return;const H=i.moveHistory.slice(0,-1);c({...i,moveHistory:H})},[i]),q=C.useCallback(()=>i.isCheckmate?{status:"checkmate",winner:i.currentPlayer==="white"?"black":"white"}:i.isStalemate?{status:"stalemate"}:i.isCheck?{status:"check",player:i.currentPlayer}:i.halfMoveClock>=50?{status:"draw",reason:"50-move rule"}:{status:"playing",currentPlayer:i.currentPlayer},[i]);return{gameState:i,selectedPiece:o,promotionPending:d,makeMove:p,handlePromotion:m,cancelPromotion:g,resetGame:R,undoMove:M,getGameStatus:q,setSelectedPiece:s}};function Vg(){const{gameState:i,promotionPending:c,makeMove:o,handlePromotion:s,cancelPromotion:d,resetGame:h,undoMove:p,getGameStatus:y,setSelectedPiece:m}=Zg(),g=y();return $.jsxs("div",{className:"app",children:[$.jsxs("header",{className:"app-header",children:[$.jsx("h1",{children:"♔ Ajedrez con Drag & Drop ♛"}),$.jsx("p",{children:"Juego de ajedrez completo con todas las reglas implementadas"})]}),$.jsx("main",{className:"app-main",children:$.jsxs("div",{className:"game-container",children:[$.jsx("div",{className:"game-board-container",children:$.jsx(xg,{gameState:i,onMove:o,onPieceSelect:m,showValidMoves:!0,isInteractive:!c&&!["checkmate","stalemate"].includes(g.status)})}),$.jsx("div",{className:"game-sidebar",children:$.jsx(Hg,{gameStatus:g,onReset:h,onUndo:p,canUndo:i.moveHistory.length>0,moveCount:i.moveHistory.length})})]})}),$.jsx(qg,{color:c?.piece.color||"white",onSelect:s,onCancel:d,isVisible:!!c}),$.jsx("footer",{className:"app-footer",children:$.jsx("p",{children:"Desarrollado con React, TypeScript y dnd-kit • Incluye todas las reglas del ajedrez: enroque, captura al paso, promoción, jaque mate"})})]})}Wy.createRoot(document.getElementById("root")).render($.jsx(C.StrictMode,{children:$.jsx(Vg,{})}));
