import type {
  ChessPiece,
  GameState,
  Position,
  PieceColor,
  PieceType,
} from "../types/chess";

// Crear una pieza nueva
export const createPiece = (
  type: PieceType,
  color: PieceColor,
  position: Position,
  id?: string
): ChessPiece => ({
  id: id || `${color}-${type}-${position.row}-${position.col}`,
  type,
  color,
  position,
  hasMoved: false,
});

// Inicializar el tablero en posición inicial
export const initializeBoard = (): (ChessPiece | null)[][] => {
  const board: (ChessPiece | null)[][] = Array(8)
    .fill(null)
    .map(() => Array(8).fill(null));

  // Piezas blancas (fila 7 y 6)
  const whitePieces: PieceType[] = [
    "rook",
    "knight",
    "bishop",
    "queen",
    "king",
    "bishop",
    "knight",
    "rook",
  ];

  // Colocar piezas principales blancas
  whitePieces.forEach((type, col) => {
    board[7][col] = createPiece(type, "white", { row: 7, col });
  });

  // Colocar peones blancos
  for (let col = 0; col < 8; col++) {
    board[6][col] = createPiece("pawn", "white", { row: 6, col });
  }

  // Piezas negras (fila 0 y 1)
  const blackPieces: PieceType[] = [
    "rook",
    "knight",
    "bishop",
    "queen",
    "king",
    "bishop",
    "knight",
    "rook",
  ];

  // Colocar piezas principales negras
  blackPieces.forEach((type, col) => {
    board[0][col] = createPiece(type, "black", { row: 0, col });
  });

  // Colocar peones negros
  for (let col = 0; col < 8; col++) {
    board[1][col] = createPiece("pawn", "black", { row: 1, col });
  }

  return board;
};

// Crear estado inicial del juego
export const createInitialGameState = (): GameState => ({
  board: initializeBoard(),
  currentPlayer: "white",
  moveHistory: [],
  isCheck: false,
  isCheckmate: false,
  isStalemate: false,
  canCastleKingside: { white: true, black: true },
  canCastleQueenside: { white: true, black: true },
  enPassantTarget: null,
  halfMoveClock: 0,
  fullMoveNumber: 1,
});

// Obtener pieza en una posición
export const getPieceAt = (
  board: (ChessPiece | null)[][],
  position: Position
): ChessPiece | null => {
  if (
    position.row < 0 ||
    position.row >= 8 ||
    position.col < 0 ||
    position.col >= 8
  ) {
    return null;
  }
  return board[position.row][position.col];
};

// Colocar pieza en el tablero
export const setPieceAt = (
  board: (ChessPiece | null)[][],
  position: Position,
  piece: ChessPiece | null
): (ChessPiece | null)[][] => {
  const newBoard = board.map((row) => [...row]);
  newBoard[position.row][position.col] = piece;
  return newBoard;
};

// Encontrar el rey de un color específico
export const findKing = (
  board: (ChessPiece | null)[][],
  color: PieceColor
): Position | null => {
  for (let row = 0; row < 8; row++) {
    for (let col = 0; col < 8; col++) {
      const piece = board[row][col];
      if (piece && piece.type === "king" && piece.color === color) {
        return { row, col };
      }
    }
  }
  return null;
};

// Obtener todas las piezas de un color
export const getPiecesOfColor = (
  board: (ChessPiece | null)[][],
  color: PieceColor
): ChessPiece[] => {
  const pieces: ChessPiece[] = [];
  for (let row = 0; row < 8; row++) {
    for (let col = 0; col < 8; col++) {
      const piece = board[row][col];
      if (piece && piece.color === color) {
        pieces.push(piece);
      }
    }
  }
  return pieces;
};

// Clonar el estado del juego
export const cloneGameState = (gameState: GameState): GameState => ({
  ...gameState,
  board: gameState.board.map((row) => [...row]),
  moveHistory: [...gameState.moveHistory],
  canCastleKingside: { ...gameState.canCastleKingside },
  canCastleQueenside: { ...gameState.canCastleQueenside },
  enPassantTarget: gameState.enPassantTarget
    ? { ...gameState.enPassantTarget }
    : null,
});

// Cambiar turno
export const switchPlayer = (color: PieceColor): PieceColor => {
  return color === "white" ? "black" : "white";
};

// Esta función se implementa en moveValidation.ts para evitar dependencias circulares

// Convertir posición de tablero a notación algebraica
export const positionToAlgebraic = (position: Position): string => {
  const files = "abcdefgh";
  const ranks = "87654321";
  return `${files[position.col]}${ranks[position.row]}`;
};

// Convertir notación algebraica a posición de tablero
export const algebraicToPosition = (algebraic: string): Position => {
  const files = "abcdefgh";
  const ranks = "87654321";
  const col = files.indexOf(algebraic[0]);
  const row = ranks.indexOf(algebraic[1]);
  return { row, col };
};
