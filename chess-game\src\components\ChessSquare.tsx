import React from "react";
import { useDroppable } from "@dnd-kit/core";
import type { Chess<PERSON>iece, Position } from "../types/chess";
import ChessPieceComponent from "./ChessPiece";
import "./ChessSquare.css";

interface ChessSquareProps {
  position: Position;
  piece: ChessPiece | null;
  isLight: boolean;
  isSelected?: boolean;
  isValidMove?: boolean;
  isLastMove?: boolean;
  isCheck?: boolean;
  onClick: () => void;
  isInteractive?: boolean;
}

const ChessSquare: React.FC<ChessSquareProps> = ({
  position,
  piece,
  isLight,
  isSelected = false,
  isValidMove = false,
  isLastMove = false,
  isCheck = false,
  onClick,
  isInteractive = true,
}) => {
  const { setNodeRef, isOver } = useDroppable({
    id: `${position.row}-${position.col}`,
  });

  // Generar clases CSS para la casilla
  const getSquareClasses = () => {
    const classes = ["chess-square"];

    if (isLight) {
      classes.push("chess-square--light");
    } else {
      classes.push("chess-square--dark");
    }

    if (isSelected) {
      classes.push("chess-square--selected");
    }

    if (isValidMove) {
      classes.push("chess-square--valid-move");
    }

    if (isLastMove) {
      classes.push("chess-square--last-move");
    }

    if (isCheck) {
      classes.push("chess-square--check");
    }

    if (isOver) {
      classes.push("chess-square--drag-over");
    }

    if (isInteractive) {
      classes.push("chess-square--interactive");
    }

    return classes.join(" ");
  };

  // Obtener las coordenadas algebraicas para mostrar en el tablero
  const getCoordinateLabel = () => {
    const files = "abcdefgh";
    const ranks = "87654321";

    let label = "";

    // Mostrar letra de columna en la fila inferior
    if (position.row === 7) {
      label += files[position.col];
    }

    // Mostrar número de fila en la columna izquierda
    if (position.col === 0) {
      label += ranks[position.row];
    }

    return label;
  };

  return (
    <div
      ref={setNodeRef}
      className={getSquareClasses()}
      onClick={onClick}
      data-position={`${position.row}-${position.col}`}
    >
      {/* Etiquetas de coordenadas */}
      <div className="chess-square__coordinates">{getCoordinateLabel()}</div>

      {/* Indicador de movimiento válido */}
      {isValidMove && !piece && (
        <div className="chess-square__valid-move-indicator" />
      )}

      {/* Indicador de captura válida */}
      {isValidMove && piece && (
        <div className="chess-square__capture-indicator" />
      )}

      {/* Pieza de ajedrez */}
      {piece && (
        <ChessPieceComponent piece={piece} isInteractive={isInteractive} />
      )}
    </div>
  );
};

export default ChessSquare;
