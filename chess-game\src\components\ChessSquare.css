.chess-square {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  cursor: pointer;
  user-select: none;
  transition: all 0.2s ease-in-out;
}

/* Colores de las casillas */
.chess-square--light {
  background-color: #f0d9b5;
}

.chess-square--dark {
  background-color: #b58863;
}

/* Estados de interacción */
.chess-square--interactive:hover {
  filter: brightness(1.1);
}

.chess-square--selected {
  background-color: #f7ec74 !important;
  box-shadow: inset 0 0 0 3px #f7d117;
}

.chess-square--valid-move {
  background-color: rgba(255, 255, 0, 0.3);
}

.chess-square--last-move {
  background-color: rgba(255, 255, 0, 0.5);
}

.chess-square--check {
  background-color: #ff6b6b !important;
  animation: check-pulse 1s infinite alternate;
}

.chess-square--drag-over {
  background-color: rgba(0, 255, 0, 0.3);
  transform: scale(1.05);
}

/* Coordenadas del tablero */
.chess-square__coordinates {
  position: absolute;
  font-size: 12px;
  font-weight: bold;
  color: rgba(0, 0, 0, 0.6);
  pointer-events: none;
  z-index: 1;
}

.chess-square--light .chess-square__coordinates {
  color: #b58863;
}

.chess-square--dark .chess-square__coordinates {
  color: #f0d9b5;
}

/* Posicionar coordenadas */
.chess-square__coordinates {
  top: 2px;
  left: 2px;
  line-height: 1;
}

/* Indicadores de movimientos válidos */
.chess-square__valid-move-indicator {
  position: absolute;
  width: 30%;
  height: 30%;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  pointer-events: none;
  z-index: 2;
}

.chess-square__capture-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 3px solid rgba(255, 0, 0, 0.6);
  border-radius: 50%;
  pointer-events: none;
  z-index: 2;
  animation: capture-pulse 1s infinite alternate;
}

/* Animaciones */
@keyframes check-pulse {
  0% {
    background-color: #ff6b6b;
  }
  100% {
    background-color: #ff4757;
  }
}

@keyframes capture-pulse {
  0% {
    border-color: rgba(255, 0, 0, 0.6);
    transform: scale(1);
  }
  100% {
    border-color: rgba(255, 0, 0, 0.9);
    transform: scale(0.95);
  }
}

/* Responsividad */
@media (max-width: 768px) {
  .chess-square__coordinates {
    font-size: 10px;
  }
  
  .chess-square__valid-move-indicator {
    width: 25%;
    height: 25%;
  }
}

@media (max-width: 480px) {
  .chess-square__coordinates {
    font-size: 8px;
  }
  
  .chess-square__valid-move-indicator {
    width: 20%;
    height: 20%;
  }
}
