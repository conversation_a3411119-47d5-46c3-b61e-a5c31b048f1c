{"version": 3, "file": "utilities.cjs.production.min.js", "sources": ["../src/execution-context/canUseDOM.ts", "../src/type-guards/isWindow.ts", "../src/type-guards/isNode.ts", "../src/execution-context/getWindow.ts", "../src/type-guards/isDocument.ts", "../src/type-guards/isHTMLElement.ts", "../src/type-guards/isSVGElement.ts", "../src/hooks/useIsomorphicLayoutEffect.ts", "../src/hooks/useEvent.ts", "../src/hooks/useUniqueId.ts", "../src/adjustment.ts", "../src/event/hasViewportRelativeCoordinates.ts", "../src/event/isTouchEvent.ts", "../src/css.ts", "../src/focus/findFirstFocusableNode.ts", "../src/coordinates/getEventCoordinates.ts", "../src/execution-context/getOwnerDocument.ts", "../src/event/isKeyboardEvent.ts", "../src/hooks/useCombinedRefs.ts", "../src/hooks/useInterval.ts", "../src/hooks/useLatestValue.ts", "../src/hooks/useLazyMemo.ts", "../src/hooks/useNodeRef.ts", "../src/hooks/usePrevious.ts"], "sourcesContent": ["// https://github.com/facebook/react/blob/master/packages/shared/ExecutionEnvironment.js\nexport const canUseDOM =\n  typeof window !== 'undefined' &&\n  typeof window.document !== 'undefined' &&\n  typeof window.document.createElement !== 'undefined';\n", "export function isWindow(element: Object): element is typeof window {\n  const elementString = Object.prototype.toString.call(element);\n  return (\n    elementString === '[object Window]' ||\n    // In Electron context the Window object serializes to [object global]\n    elementString === '[object global]'\n  );\n}\n", "export function isNode(node: Object): node is Node {\n  return 'nodeType' in node;\n}\n", "import {isWindow} from '../type-guards/isWindow';\nimport {isNode} from '../type-guards/isNode';\n\nexport function getWindow(target: Event['target']): typeof window {\n  if (!target) {\n    return window;\n  }\n\n  if (isWindow(target)) {\n    return target;\n  }\n\n  if (!isNode(target)) {\n    return window;\n  }\n\n  return target.ownerDocument?.defaultView ?? window;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isDocument(node: Node): node is Document {\n  const {Document} = getWindow(node);\n\n  return node instanceof Document;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nimport {isWindow} from './isWindow';\n\nexport function isHTMLElement(node: Node | Window): node is HTMLElement {\n  if (isWindow(node)) {\n    return false;\n  }\n\n  return node instanceof getWindow(node).HTMLElement;\n}\n", "import {getWindow} from '../execution-context/getWindow';\n\nexport function isSVGElement(node: Node): node is SVGElement {\n  return node instanceof getWindow(node).SVGElement;\n}\n", "import {useEffect, useLayoutEffect} from 'react';\n\nimport {canUseDOM} from '../execution-context';\n\n/**\n * A hook that resolves to useEffect on the server and useLayoutEffect on the client\n * @param callback {function} Callback function that is invoked when the dependencies of the hook change\n */\nexport const useIsomorphicLayoutEffect = canUseDOM\n  ? useLayoutEffect\n  : useEffect;\n", "import {useCallback, useRef} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useEvent<T extends Function>(handler: T | undefined) {\n  const handlerRef = useRef<T | undefined>(handler);\n\n  useIsomorphicLayoutEffect(() => {\n    handlerRef.current = handler;\n  });\n\n  return useCallback(function (...args: any) {\n    return handlerRef.current?.(...args);\n  }, []);\n}\n", "import {useMemo} from 'react';\n\nlet ids: Record<string, number> = {};\n\nexport function useUniqueId(prefix: string, value?: string) {\n  return useMemo(() => {\n    if (value) {\n      return value;\n    }\n\n    const id = ids[prefix] == null ? 0 : ids[prefix] + 1;\n    ids[prefix] = id;\n\n    return `${prefix}-${id}`;\n  }, [prefix, value]);\n}\n", "function createAdjustmentFn(modifier: number) {\n  return <T extends Record<U, number>, U extends string>(\n    object: T,\n    ...adjustments: Partial<T>[]\n  ): T => {\n    return adjustments.reduce<T>(\n      (accumulator, adjustment) => {\n        const entries = Object.entries(adjustment) as [U, number][];\n\n        for (const [key, valueAdjustment] of entries) {\n          const value = accumulator[key];\n\n          if (value != null) {\n            accumulator[key] = (value + modifier * valueAdjustment) as T[U];\n          }\n        }\n\n        return accumulator;\n      },\n      {\n        ...object,\n      }\n    );\n  };\n}\n\nexport const add = createAdjustmentFn(1);\nexport const subtract = createAdjustmentFn(-1);\n", "export function hasViewportRelativeCoordinates(\n  event: Event\n): event is Event & Pick<PointerEvent, 'clientX' | 'clientY'> {\n  return 'clientX' in event && 'clientY' in event;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isTouchEvent(\n  event: Event | undefined | null\n): event is TouchEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {TouchEvent} = getWindow(event.target);\n\n  return TouchEvent && event instanceof TouchEvent;\n}\n", "export type Transform = {\n  x: number;\n  y: number;\n  scaleX: number;\n  scaleY: number;\n};\n\nexport interface Transition {\n  property: string;\n  easing: string;\n  duration: number;\n}\n\nexport const CSS = Object.freeze({\n  Translate: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {x, y} = transform;\n\n      return `translate3d(${x ? Math.round(x) : 0}px, ${\n        y ? Math.round(y) : 0\n      }px, 0)`;\n    },\n  },\n  Scale: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      const {scaleX, scaleY} = transform;\n\n      return `scaleX(${scaleX}) scaleY(${scaleY})`;\n    },\n  },\n  Transform: {\n    toString(transform: Transform | null) {\n      if (!transform) {\n        return;\n      }\n\n      return [\n        CSS.Translate.toString(transform),\n        CSS.Scale.toString(transform),\n      ].join(' ');\n    },\n  },\n  Transition: {\n    toString({property, duration, easing}: Transition) {\n      return `${property} ${duration}ms ${easing}`;\n    },\n  },\n});\n", "const SELECTOR =\n  'a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]';\n\nexport function findFirstFocusableNode(\n  element: HTMLElement\n): HTMLElement | null {\n  if (element.matches(SELECTOR)) {\n    return element;\n  }\n\n  return element.querySelector(SELECTOR);\n}\n", "import type {Coordinates} from './types';\nimport {isTouchEvent, hasViewportRelativeCoordinates} from '../event';\n\n/**\n * Returns the normalized x and y coordinates for mouse and touch events.\n */\nexport function getEventCoordinates(event: Event): Coordinates | null {\n  if (isTouchEvent(event)) {\n    if (event.touches && event.touches.length) {\n      const {clientX: x, clientY: y} = event.touches[0];\n\n      return {\n        x,\n        y,\n      };\n    } else if (event.changedTouches && event.changedTouches.length) {\n      const {clientX: x, clientY: y} = event.changedTouches[0];\n\n      return {\n        x,\n        y,\n      };\n    }\n  }\n\n  if (hasViewportRelativeCoordinates(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY,\n    };\n  }\n\n  return null;\n}\n", "import {\n  isWindow,\n  isHTMLElement,\n  isDocument,\n  isNode,\n  isSVGElement,\n} from '../type-guards';\n\nexport function getOwnerDocument(target: Event['target']): Document {\n  if (!target) {\n    return document;\n  }\n\n  if (isWindow(target)) {\n    return target.document;\n  }\n\n  if (!isNode(target)) {\n    return document;\n  }\n\n  if (isDocument(target)) {\n    return target;\n  }\n\n  if (isHTMLElement(target) || isSVGElement(target)) {\n    return target.ownerDocument;\n  }\n\n  return document;\n}\n", "import {getWindow} from '../execution-context';\n\nexport function isKeyboardEvent(\n  event: Event | undefined | null\n): event is KeyboardEvent {\n  if (!event) {\n    return false;\n  }\n\n  const {KeyboardEvent} = getWindow(event.target);\n\n  return KeyboardEvent && event instanceof KeyboardEvent;\n}\n", "import {useMemo} from 'react';\n\nexport function useCombinedRefs<T>(\n  ...refs: ((node: T) => void)[]\n): (node: T) => void {\n  return useMemo(\n    () => (node: T) => {\n      refs.forEach((ref) => ref(node));\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    refs\n  );\n}\n", "import {useCallback, useRef} from 'react';\n\nexport function useInterval() {\n  const intervalRef = useRef<number | null>(null);\n\n  const set = useCallback((listener: Function, duration: number) => {\n    intervalRef.current = setInterval(listener, duration);\n  }, []);\n\n  const clear = useCallback(() => {\n    if (intervalRef.current !== null) {\n      clearInterval(intervalRef.current);\n      intervalRef.current = null;\n    }\n  }, []);\n\n  return [set, clear] as const;\n}\n", "import {useRef} from 'react';\nimport type {DependencyList} from 'react';\n\nimport {useIsomorphicLayoutEffect} from './useIsomorphicLayoutEffect';\n\nexport function useLatestValue<T extends any>(\n  value: T,\n  dependencies: DependencyList = [value]\n) {\n  const valueRef = useRef<T>(value);\n\n  useIsomorphicLayoutEffect(() => {\n    if (valueRef.current !== value) {\n      valueRef.current = value;\n    }\n  }, dependencies);\n\n  return valueRef;\n}\n", "import {useMemo, useRef} from 'react';\n\nexport function useLazyMemo<T>(\n  callback: (prevValue: T | undefined) => T,\n  dependencies: any[]\n) {\n  const valueRef = useRef<T>();\n\n  return useMemo(\n    () => {\n      const newValue = callback(valueRef.current);\n      valueRef.current = newValue;\n\n      return newValue;\n    },\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    [...dependencies]\n  );\n}\n", "import {useRef, useCallback} from 'react';\n\nimport {useEvent} from './useEvent';\n\nexport function useNodeRef(\n  onChange?: (\n    newElement: HTMLElement | null,\n    previousElement: HTMLElement | null\n  ) => void\n) {\n  const onChangeHandler = useEvent(onChange);\n  const node = useRef<HTMLElement | null>(null);\n  const setNodeRef = useCallback(\n    (element: HTMLElement | null) => {\n      if (element !== node.current) {\n        onChangeHandler?.(element, node.current);\n      }\n\n      node.current = element;\n    },\n    //eslint-disable-next-line\n    []\n  );\n\n  return [node, setNodeRef] as const;\n}\n", "import {useRef, useEffect} from 'react';\n\nexport function usePrevious<T>(value: T) {\n  const ref = useRef<T>();\n\n  useEffect(() => {\n    ref.current = value;\n  }, [value]);\n\n  return ref.current;\n}\n"], "names": ["canUseDOM", "window", "document", "createElement", "isWindow", "element", "elementString", "Object", "prototype", "toString", "call", "isNode", "node", "getWindow", "target", "ownerDocument", "_target$ownerDocument2", "defaultView", "isDocument", "Document", "isHTMLElement", "HTMLElement", "isSVGElement", "SVGElement", "useIsomorphicLayoutEffect", "useLayoutEffect", "useEffect", "useEvent", "handler", "handler<PERSON>ef", "useRef", "current", "useCallback", "args", "ids", "createAdjustmentFn", "modifier", "object", "adjustments", "reduce", "accumulator", "adjustment", "entries", "key", "valueAdjustment", "value", "add", "subtract", "hasViewportRelativeCoordinates", "event", "isTouchEvent", "TouchEvent", "CSS", "freeze", "Translate", "transform", "x", "y", "Math", "round", "Scale", "scaleX", "scaleY", "Transform", "join", "Transition", "property", "duration", "easing", "SELECTOR", "matches", "querySelector", "touches", "length", "clientX", "clientY", "changedTouches", "KeyboardEvent", "refs", "useMemo", "for<PERSON>ach", "ref", "intervalRef", "listener", "setInterval", "clearInterval", "dependencies", "valueRef", "callback", "newValue", "onChange", "onChangeHandler", "setNodeRef", "prefix", "id"], "mappings": "2FACA,MAAaA,EACO,oBAAXC,aACoB,IAApBA,OAAOC,eAC2B,IAAlCD,OAAOC,SAASC,uBCJTC,EAASC,GACvB,MAAMC,EAAgBC,OAAOC,UAAUC,SAASC,KAAKL,GACrD,MACoB,oBAAlBC,GAEkB,oBAAlBA,WCLYK,EAAOC,GACrB,MAAO,aAAcA,WCEPC,EAAUC,WACxB,OAAKA,EAIDV,EAASU,GACJA,EAGJH,EAAOG,sBAILA,EAAOC,sBAAPC,EAAsBC,eAHpBhB,OARAA,gBCHKiB,EAAWN,GACzB,MAAMO,SAACA,GAAYN,EAAUD,GAE7B,OAAOA,aAAgBO,WCDTC,EAAcR,GAC5B,OAAIR,EAASQ,IAINA,aAAgBC,EAAUD,GAAMS,qBCPzBC,EAAaV,GAC3B,OAAOA,aAAgBC,EAAUD,GAAMW,iBCK5BC,EAA4BxB,EACrCyB,kBACAC,qBCNYC,EAA6BC,GAC3C,MAAMC,EAAaC,SAAsBF,GAMzC,OAJAJ,EAA0B,KACxBK,EAAWE,QAAUH,IAGhBI,eAAY,sCAAaC,2BAAAA,kBAC9B,aAAOJ,EAAWE,eAAXF,EAAWE,WAAaE,KAC9B,ICXL,IAAIC,EAA8B,GCFlC,SAASC,EAAmBC,GAC1B,OAAO,SACLC,8BACGC,mCAAAA,oBAEH,OAAOA,EAAYC,OACjB,CAACC,EAAaC,KACZ,MAAMC,EAAUnC,OAAOmC,QAAQD,GAE/B,IAAK,MAAOE,EAAKC,KAAoBF,EAAS,CAC5C,MAAMG,EAAQL,EAAYG,GAEb,MAATE,IACFL,EAAYG,GAAQE,EAAQT,EAAWQ,GAI3C,OAAOJ,GAET,IACKH,WAMES,EAAMX,EAAmB,GACzBY,EAAWZ,GAAoB,YC3B5Ba,EACdC,GAEA,MAAO,YAAaA,GAAS,YAAaA,WCD5BC,EACdD,GAEA,IAAKA,EACH,OAAO,EAGT,MAAME,WAACA,GAActC,EAAUoC,EAAMnC,QAErC,OAAOqC,GAAcF,aAAiBE,QCE3BC,EAAM7C,OAAO8C,OAAO,CAC/BC,UAAW,CACT7C,SAAS8C,GACP,IAAKA,EACH,OAGF,MAAMC,EAACA,EAADC,EAAIA,GAAKF,EAEf,sBAAsBC,EAAIE,KAAKC,MAAMH,GAAK,WACxCC,EAAIC,KAAKC,MAAMF,GAAK,cAI1BG,MAAO,CACLnD,SAAS8C,GACP,IAAKA,EACH,OAGF,MAAMM,OAACA,EAADC,OAASA,GAAUP,EAEzB,gBAAiBM,cAAkBC,QAGvCC,UAAW,CACTtD,SAAS8C,GACP,GAAKA,EAIL,MAAO,CACLH,EAAIE,UAAU7C,SAAS8C,GACvBH,EAAIQ,MAAMnD,SAAS8C,IACnBS,KAAK,OAGXC,WAAY,CACVxD,gBAASyD,SAACA,EAADC,SAAWA,EAAXC,OAAqBA,KAC5B,OAAUF,MAAYC,QAAcC,MCpDpCC,EACJ,iOAGAhE,GAEA,OAAIA,EAAQiE,QAAQD,GACXhE,EAGFA,EAAQkE,cAAcF,yCCJKpB,GAClC,GAAIC,EAAaD,GAAQ,CACvB,GAAIA,EAAMuB,SAAWvB,EAAMuB,QAAQC,OAAQ,CACzC,MAAOC,QAASlB,EAAGmB,QAASlB,GAAKR,EAAMuB,QAAQ,GAE/C,MAAO,CACLhB,EAAAA,EACAC,EAAAA,GAEG,GAAIR,EAAM2B,gBAAkB3B,EAAM2B,eAAeH,OAAQ,CAC9D,MAAOC,QAASlB,EAAGmB,QAASlB,GAAKR,EAAM2B,eAAe,GAEtD,MAAO,CACLpB,EAAAA,EACAC,EAAAA,IAKN,OAAIT,EAA+BC,GAC1B,CACLO,EAAGP,EAAMyB,QACTjB,EAAGR,EAAM0B,SAIN,wCCxBwB7D,GAC/B,OAAKA,EAIDV,EAASU,GACJA,EAAOZ,SAGXS,EAAOG,GAIRI,EAAWJ,GACNA,EAGLM,EAAcN,IAAWQ,EAAaR,GACjCA,EAAOC,cAGTb,SAXEA,SARAA,qJCPT+C,GAEA,IAAKA,EACH,OAAO,EAGT,MAAM4B,cAACA,GAAiBhE,EAAUoC,EAAMnC,QAExC,OAAO+D,GAAiB5B,aAAiB4B,sKCRtCC,2BAAAA,kBAEH,OAAOC,UACL,IAAOnE,IACLkE,EAAKE,QAASC,GAAQA,EAAIrE,KAG5BkE,sDCPF,MAAMI,EAAcpD,SAAsB,MAa1C,MAAO,CAXKE,cAAY,CAACmD,EAAoBhB,KAC3Ce,EAAYnD,QAAUqD,YAAYD,EAAUhB,IAC3C,IAEWnC,cAAY,KACI,OAAxBkD,EAAYnD,UACdsD,cAAcH,EAAYnD,SAC1BmD,EAAYnD,QAAU,OAEvB,0ECRHc,EACAyC,YAAAA,IAAAA,EAA+B,CAACzC,IAEhC,MAAM0C,EAAWzD,SAAUe,GAQ3B,OANArB,EAA0B,KACpB+D,EAASxD,UAAYc,IACvB0C,EAASxD,QAAUc,IAEpByC,GAEIC,gCCdPC,EACAF,GAEA,MAAMC,EAAWzD,WAEjB,OAAOiD,UACL,KACE,MAAMU,EAAWD,EAASD,EAASxD,SAGnC,OAFAwD,EAASxD,QAAU0D,EAEZA,GAGT,IAAIH,iCCXNI,GAKA,MAAMC,EAAkBhE,EAAS+D,GAC3B9E,EAAOkB,SAA2B,MAClC8D,EAAa5D,cAChB3B,IACKA,IAAYO,EAAKmB,gBACnB4D,GAAAA,EAAkBtF,EAASO,EAAKmB,UAGlCnB,EAAKmB,QAAU1B,GAGjB,IAGF,MAAO,CAACO,EAAMgF,iCCtBe/C,GAC7B,MAAMoC,EAAMnD,WAMZ,OAJAJ,YAAU,KACRuD,EAAIlD,QAAUc,GACb,CAACA,IAEGoC,EAAIlD,sCdLe8D,EAAgBhD,GAC1C,OAAOkC,UAAQ,KACb,GAAIlC,EACF,OAAOA,EAGT,MAAMiD,EAAoB,MAAf5D,EAAI2D,GAAkB,EAAI3D,EAAI2D,GAAU,EAGnD,OAFA3D,EAAI2D,GAAUC,EAEJD,MAAUC,GACnB,CAACD,EAAQhD"}