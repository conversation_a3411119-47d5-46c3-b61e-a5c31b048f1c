{"version": 3, "file": "modifiers.cjs.production.min.js", "sources": ["../src/utilities/restrictToBoundingRect.ts", "../src/createSnapModifier.ts", "../src/restrictToFirstScrollableAncestor.ts", "../src/restrictToHorizontalAxis.ts", "../src/restrictToParentElement.ts", "../src/restrictToVerticalAxis.ts", "../src/restrictToWindowEdges.ts", "../src/snapCenterToCursor.ts"], "sourcesContent": ["import type {ClientRect} from '@dnd-kit/core';\nimport type {Transform} from '@dnd-kit/utilities';\n\nexport function restrictToBoundingRect(\n  transform: Transform,\n  rect: ClientRect,\n  boundingRect: ClientRect\n): Transform {\n  const value = {\n    ...transform,\n  };\n\n  if (rect.top + transform.y <= boundingRect.top) {\n    value.y = boundingRect.top - rect.top;\n  } else if (\n    rect.bottom + transform.y >=\n    boundingRect.top + boundingRect.height\n  ) {\n    value.y = boundingRect.top + boundingRect.height - rect.bottom;\n  }\n\n  if (rect.left + transform.x <= boundingRect.left) {\n    value.x = boundingRect.left - rect.left;\n  } else if (\n    rect.right + transform.x >=\n    boundingRect.left + boundingRect.width\n  ) {\n    value.x = boundingRect.left + boundingRect.width - rect.right;\n  }\n\n  return value;\n}\n", "import type {Modifier} from '@dnd-kit/core';\n\nexport function createSnapModifier(gridSize: number): Modifier {\n  return ({transform}) => ({\n    ...transform,\n    x: Math.ceil(transform.x / gridSize) * gridSize,\n    y: Math.ceil(transform.y / gridSize) * gridSize,\n  });\n}\n", "import type {Modifier} from '@dnd-kit/core';\nimport {restrictToBoundingRect} from './utilities';\n\nexport const restrictToFirstScrollableAncestor: Modifier = ({\n  draggingNodeRect,\n  transform,\n  scrollableAncestorRects,\n}) => {\n  const firstScrollableAncestorRect = scrollableAncestorRects[0];\n\n  if (!draggingNodeRect || !firstScrollableAncestorRect) {\n    return transform;\n  }\n\n  return restrictToBoundingRect(\n    transform,\n    draggingNodeRect,\n    firstScrollableAncestorRect\n  );\n};\n", "import type {Modifier} from '@dnd-kit/core';\n\nexport const restrictToHorizontalAxis: Modifier = ({transform}) => {\n  return {\n    ...transform,\n    y: 0,\n  };\n};\n", "import type {Modifier} from '@dnd-kit/core';\nimport {restrictToBoundingRect} from './utilities';\n\nexport const restrictToParentElement: Modifier = ({\n  containerNodeRect,\n  draggingNodeRect,\n  transform,\n}) => {\n  if (!draggingNodeRect || !containerNodeRect) {\n    return transform;\n  }\n\n  return restrictToBoundingRect(transform, draggingNodeRect, containerNodeRect);\n};\n", "import type {Modifier} from '@dnd-kit/core';\n\nexport const restrictToVerticalAxis: Modifier = ({transform}) => {\n  return {\n    ...transform,\n    x: 0,\n  };\n};\n", "import type {Modifier} from '@dnd-kit/core';\n\nimport {restrictToBoundingRect} from './utilities';\n\nexport const restrictToWindowEdges: Modifier = ({\n  transform,\n  draggingNodeRect,\n  windowRect,\n}) => {\n  if (!draggingNodeRect || !windowRect) {\n    return transform;\n  }\n\n  return restrictToBoundingRect(transform, draggingNodeRect, windowRect);\n};\n", "import type {Modifier} from '@dnd-kit/core';\nimport {getEventCoordinates} from '@dnd-kit/utilities';\n\nexport const snapCenterToCursor: Modifier = ({\n  activatorEvent,\n  draggingNodeRect,\n  transform,\n}) => {\n  if (draggingNodeRect && activatorEvent) {\n    const activatorCoordinates = getEventCoordinates(activatorEvent);\n\n    if (!activatorCoordinates) {\n      return transform;\n    }\n\n    const offsetX = activatorCoordinates.x - draggingNodeRect.left;\n    const offsetY = activatorCoordinates.y - draggingNodeRect.top;\n\n    return {\n      ...transform,\n      x: transform.x + offsetX - draggingNodeRect.width / 2,\n      y: transform.y + offsetY - draggingNodeRect.height / 2,\n    };\n  }\n\n  return transform;\n};\n"], "names": ["restrictToBoundingRect", "transform", "rect", "boundingRect", "value", "top", "y", "bottom", "height", "left", "x", "right", "width", "gridSize", "_ref", "Math", "ceil", "draggingNodeRect", "scrollableAncestorRects", "firstScrollableAncestorRect", "containerNodeRect", "windowRect", "activatorEvent", "activatorCoordinates", "getEventCoordinates", "offsetX", "offsetY"], "mappings": "iHAGgBA,EACdC,EACAC,EACAC,GAEA,MAAMC,EAAQ,IACTH,GAqBL,OAlBIC,EAAKG,IAAMJ,EAAUK,GAAKH,EAAaE,IACzCD,EAAME,EAAIH,EAAaE,IAAMH,EAAKG,IAElCH,EAAKK,OAASN,EAAUK,GACxBH,EAAaE,IAAMF,EAAaK,SAEhCJ,EAAME,EAAIH,EAAaE,IAAMF,EAAaK,OAASN,EAAKK,QAGtDL,EAAKO,KAAOR,EAAUS,GAAKP,EAAaM,KAC1CL,EAAMM,EAAIP,EAAaM,KAAOP,EAAKO,KAEnCP,EAAKS,MAAQV,EAAUS,GACvBP,EAAaM,KAAON,EAAaS,QAEjCR,EAAMM,EAAIP,EAAaM,KAAON,EAAaS,MAAQV,EAAKS,OAGnDP,sCC5B0BS,GACjC,OAAOC,IAAA,IAACb,UAACA,KAAF,MAAkB,IACpBA,EACHS,EAAGK,KAAKC,KAAKf,EAAUS,EAAIG,GAAYA,EACvCP,EAAGS,KAAKC,KAAKf,EAAUK,EAAIO,GAAYA,+CCHgBC,QAACG,iBAC1DA,EAD0DhB,UAE1DA,EAF0DiB,wBAG1DA,KAEA,MAAMC,EAA8BD,EAAwB,GAE5D,OAAKD,GAAqBE,EAInBnB,EACLC,EACAgB,EACAE,GANOlB,oCCTuCa,QAACb,UAACA,KAClD,MAAO,IACFA,EACHK,EAAG,oCCF0CQ,QAACM,kBAChDA,EADgDH,iBAEhDA,EAFgDhB,UAGhDA,KAEA,OAAKgB,GAAqBG,EAInBpB,EAAuBC,EAAWgB,EAAkBG,GAHlDnB,kCCPqCa,QAACb,UAACA,KAChD,MAAO,IACFA,EACHS,EAAG,kCCDwCI,QAACb,UAC9CA,EAD8CgB,iBAE9CA,EAF8CI,WAG9CA,KAEA,OAAKJ,GAAqBI,EAInBrB,EAAuBC,EAAWgB,EAAkBI,GAHlDpB,8BCPiCa,QAACQ,eAC3CA,EAD2CL,iBAE3CA,EAF2ChB,UAG3CA,KAEA,GAAIgB,GAAoBK,EAAgB,CACtC,MAAMC,EAAuBC,sBAAoBF,GAEjD,IAAKC,EACH,OAAOtB,EAGT,MAAMwB,EAAUF,EAAqBb,EAAIO,EAAiBR,KACpDiB,EAAUH,EAAqBjB,EAAIW,EAAiBZ,IAE1D,MAAO,IACFJ,EACHS,EAAGT,EAAUS,EAAIe,EAAUR,EAAiBL,MAAQ,EACpDN,EAAGL,EAAUK,EAAIoB,EAAUT,EAAiBT,OAAS,GAIzD,OAAOP"}