.promotion-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  animation: fadeIn 0.3s ease-in-out;
}

.promotion-dialog {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 400px;
  width: 90%;
  animation: slideIn 0.3s ease-out;
}

.promotion-dialog__header {
  text-align: center;
  margin-bottom: 24px;
}

.promotion-dialog__header h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.5em;
}

.promotion-dialog__header p {
  margin: 0;
  color: #666;
  font-size: 1em;
}

.promotion-dialog__options {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.promotion-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  border: 2px solid #ddd;
  border-radius: 8px;
  background: #f9f9f9;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  min-height: 100px;
}

.promotion-option:hover {
  border-color: #007acc;
  background: #f0f8ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 122, 204, 0.2);
}

.promotion-option:active {
  transform: translateY(0);
}

.promotion-option__symbol {
  font-size: 3em;
  line-height: 1;
  margin-bottom: 8px;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

.promotion-option--white .promotion-option__symbol {
  color: #ffffff;
  text-shadow: 
    1px 1px 2px rgba(0, 0, 0, 0.8),
    0 0 4px rgba(0, 0, 0, 0.5);
}

.promotion-option--black .promotion-option__symbol {
  color: #2c2c2c;
  text-shadow: 
    1px 1px 2px rgba(255, 255, 255, 0.3),
    0 0 4px rgba(0, 0, 0, 0.8);
}

.promotion-option__name {
  font-size: 0.9em;
  font-weight: 600;
  color: #333;
  text-align: center;
}

.promotion-dialog__actions {
  display: flex;
  justify-content: center;
}

.promotion-dialog__cancel {
  padding: 10px 20px;
  border: 2px solid #ccc;
  border-radius: 6px;
  background: white;
  color: #666;
  cursor: pointer;
  font-size: 1em;
  transition: all 0.2s ease-in-out;
}

.promotion-dialog__cancel:hover {
  border-color: #999;
  color: #333;
  background: #f5f5f5;
}

/* Animaciones */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsividad */
@media (max-width: 480px) {
  .promotion-dialog {
    padding: 20px;
    margin: 20px;
  }
  
  .promotion-dialog__options {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .promotion-option {
    padding: 16px;
    min-height: 80px;
  }
  
  .promotion-option__symbol {
    font-size: 2.5em;
  }
  
  .promotion-option__name {
    font-size: 0.8em;
  }
}

@media (max-width: 320px) {
  .promotion-dialog__options {
    grid-template-columns: 1fr;
  }
  
  .promotion-option {
    flex-direction: row;
    justify-content: flex-start;
    text-align: left;
    min-height: 60px;
  }
  
  .promotion-option__symbol {
    font-size: 2em;
    margin-right: 12px;
    margin-bottom: 0;
  }
}
