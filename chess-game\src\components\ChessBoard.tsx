import React, { useState, useCallback } from "react";
import {
  Dnd<PERSON>ontext,
  DragOverlay,
  useSensor,
  useSensors,
  PointerSensor,
  KeyboardSensor,
} from "@dnd-kit/core";
import type { DragEndEvent, DragStartEvent } from "@dnd-kit/core";
import { snapCenterToCursor } from "@dnd-kit/modifiers";
import type { ChessPiece, GameState, Position } from "../types/chess";
import { isMoveLegal, getValidMoves } from "../utils/moveValidation";
import ChessSquare from "./ChessSquare";
import ChessPieceComponent from "./ChessPiece";
import "./ChessBoard.css";

interface ChessBoardProps {
  gameState: GameState;
  onMove: (from: Position, to: Position) => void;
  onPieceSelect?: (piece: ChessPiece | null) => void;
  showValidMoves?: boolean;
  isInteractive?: boolean;
}

const ChessBoard: React.FC<ChessBoardProps> = ({
  gameState,
  onMove,
  onPieceSelect,
  showValidMoves = true,
  isInteractive = true,
}) => {
  const [activePiece, setActivePiece] = useState<ChessPiece | null>(null);
  const [selectedPiece, setSelectedPiece] = useState<ChessPiece | null>(null);
  const [validMoves, setValidMoves] = useState<Position[]>([]);

  // Configurar sensores para drag and drop
  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 8,
      },
    }),
    useSensor(KeyboardSensor)
  );

  // Manejar inicio del arrastre
  const handleDragStart = useCallback(
    (event: DragStartEvent) => {
      const { active } = event;
      const pieceId = active.id as string;

      // Encontrar la pieza que se está arrastrando
      const piece = findPieceById(gameState.board, pieceId);
      if (piece && piece.color === gameState.currentPlayer) {
        setActivePiece(piece);
        setSelectedPiece(piece);

        if (showValidMoves) {
          const moves = getValidMoves(gameState, piece);
          setValidMoves(moves);
        }

        onPieceSelect?.(piece);
      }
    },
    [gameState, showValidMoves, onPieceSelect]
  );

  // Manejar fin del arrastre
  const handleDragEnd = useCallback(
    (event: DragEndEvent) => {
      const { over } = event;

      setActivePiece(null);
      setValidMoves([]);

      if (!over || !activePiece) {
        return;
      }

      // Parsear la posición de destino del ID del droppable
      const [row, col] = (over.id as string).split("-").map(Number);
      const to: Position = { row, col };

      // Validar el movimiento
      const validation = isMoveLegal(gameState, activePiece, to);
      if (validation.isValid) {
        onMove(activePiece.position, to);
      }
    },
    [activePiece, gameState, onMove]
  );

  // Manejar clic en casilla (para dispositivos táctiles o preferencia de clic)
  const handleSquareClick = useCallback(
    (position: Position) => {
      if (!isInteractive) return;

      const piece = gameState.board[position.row][position.col];

      if (selectedPiece) {
        // Si hay una pieza seleccionada, intentar mover
        if (
          piece &&
          piece.color === gameState.currentPlayer &&
          piece.id === selectedPiece.id
        ) {
          // Clic en la misma pieza - deseleccionar
          setSelectedPiece(null);
          setValidMoves([]);
          onPieceSelect?.(null);
        } else {
          // Intentar mover a la posición clickeada
          const validation = isMoveLegal(gameState, selectedPiece, position);
          if (validation.isValid) {
            onMove(selectedPiece.position, position);
            setSelectedPiece(null);
            setValidMoves([]);
            onPieceSelect?.(null);
          } else if (piece && piece.color === gameState.currentPlayer) {
            // Seleccionar nueva pieza
            setSelectedPiece(piece);
            const moves = showValidMoves ? getValidMoves(gameState, piece) : [];
            setValidMoves(moves);
            onPieceSelect?.(piece);
          }
        }
      } else if (piece && piece.color === gameState.currentPlayer) {
        // Seleccionar pieza
        setSelectedPiece(piece);
        const moves = showValidMoves ? getValidMoves(gameState, piece) : [];
        setValidMoves(moves);
        onPieceSelect?.(piece);
      }
    },
    [
      gameState,
      selectedPiece,
      showValidMoves,
      onMove,
      onPieceSelect,
      isInteractive,
    ]
  );

  // Verificar si una posición es un movimiento válido
  const isValidMove = useCallback(
    (position: Position): boolean => {
      return validMoves.some(
        (move) => move.row === position.row && move.col === position.col
      );
    },
    [validMoves]
  );

  // Verificar si una posición es la última jugada
  const isLastMove = useCallback(
    (position: Position): boolean => {
      const lastMove = gameState.moveHistory[gameState.moveHistory.length - 1];
      if (!lastMove) return false;

      return (
        (lastMove.from.row === position.row &&
          lastMove.from.col === position.col) ||
        (lastMove.to.row === position.row && lastMove.to.col === position.col)
      );
    },
    [gameState.moveHistory]
  );

  // Renderizar el tablero
  const renderBoard = () => {
    const squares = [];

    for (let row = 0; row < 8; row++) {
      for (let col = 0; col < 8; col++) {
        const position = { row, col };
        const piece = gameState.board[row][col];
        const isLight = (row + col) % 2 === 0;
        const isSelected = !!(
          selectedPiece &&
          selectedPiece.position.row === row &&
          selectedPiece.position.col === col
        );

        squares.push(
          <ChessSquare
            key={`${row}-${col}`}
            position={position}
            piece={piece}
            isLight={isLight}
            isSelected={isSelected}
            isValidMove={isValidMove(position)}
            isLastMove={isLastMove(position)}
            isCheck={
              gameState.isCheck &&
              piece?.type === "king" &&
              piece.color === gameState.currentPlayer
            }
            onClick={() => handleSquareClick(position)}
            isInteractive={isInteractive}
          />
        );
      }
    }

    return squares;
  };

  return (
    <DndContext
      sensors={sensors}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
      modifiers={[snapCenterToCursor]}
    >
      <div className="chess-board">{renderBoard()}</div>

      <DragOverlay>
        {activePiece && (
          <ChessPieceComponent piece={activePiece} isDragging={true} />
        )}
      </DragOverlay>
    </DndContext>
  );
};

// Función auxiliar para encontrar una pieza por ID
const findPieceById = (
  board: (ChessPiece | null)[][],
  id: string
): ChessPiece | null => {
  for (let row = 0; row < 8; row++) {
    for (let col = 0; col < 8; col++) {
      const piece = board[row][col];
      if (piece && piece.id === id) {
        return piece;
      }
    }
  }
  return null;
};

export default ChessBoard;
