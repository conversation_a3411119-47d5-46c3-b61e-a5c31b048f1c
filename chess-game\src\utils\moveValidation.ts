import type {
  Chess<PERSON>ie<PERSON>,
  Position,
  GameState,
  PieceColor,
  MoveValidation,
} from "../types/chess";
import { getPieceAt, findKing, cloneGameState } from "./gameUtils";

// Verificar si una posición está dentro del tablero
export const isInBounds = (position: Position): boolean => {
  return (
    position.row >= 0 &&
    position.row < 8 &&
    position.col >= 0 &&
    position.col < 8
  );
};

// Verificar si el camino entre dos posiciones está libre (para torres, alfiles, reinas)
export const isPathClear = (
  board: (ChessPiece | null)[][],
  from: Position,
  to: Position
): boolean => {
  const rowDiff = to.row - from.row;
  const colDiff = to.col - from.col;

  // Calcular la dirección del movimiento
  const rowStep = rowDiff === 0 ? 0 : rowDiff / Math.abs(rowDiff);
  const colStep = colDiff === 0 ? 0 : colDiff / Math.abs(colDiff);

  let currentRow = from.row + rowStep;
  let currentCol = from.col + colStep;

  // Verificar cada casilla en el camino (excluyendo origen y destino)
  while (currentRow !== to.row || currentCol !== to.col) {
    if (board[currentRow][currentCol] !== null) {
      return false;
    }
    currentRow += rowStep;
    currentCol += colStep;
  }

  return true;
};

// Validar movimiento de peón
export const isValidPawnMove = (
  board: (ChessPiece | null)[][],
  piece: ChessPiece,
  to: Position,
  gameState: GameState
): boolean => {
  const from = piece.position;
  const direction = piece.color === "white" ? -1 : 1; // Blancas suben (-1), negras bajan (+1)
  const startRow = piece.color === "white" ? 6 : 1;

  const rowDiff = to.row - from.row;
  const colDiff = Math.abs(to.col - from.col);

  // Movimiento hacia adelante
  if (colDiff === 0) {
    // Un paso hacia adelante
    if (rowDiff === direction && !getPieceAt(board, to)) {
      return true;
    }
    // Dos pasos desde posición inicial
    if (
      rowDiff === 2 * direction &&
      from.row === startRow &&
      !getPieceAt(board, to)
    ) {
      return true;
    }
  }

  // Captura diagonal
  if (colDiff === 1 && rowDiff === direction) {
    const targetPiece = getPieceAt(board, to);
    if (targetPiece && targetPiece.color !== piece.color) {
      return true;
    }

    // Captura al paso
    if (
      gameState.enPassantTarget &&
      to.row === gameState.enPassantTarget.row &&
      to.col === gameState.enPassantTarget.col
    ) {
      return true;
    }
  }

  return false;
};

// Validar movimiento de torre
export const isValidRookMove = (
  board: (ChessPiece | null)[][],
  piece: ChessPiece,
  to: Position
): boolean => {
  const from = piece.position;

  // La torre se mueve en línea recta (horizontal o vertical)
  if (from.row !== to.row && from.col !== to.col) {
    return false;
  }

  // Verificar que el camino esté libre
  return isPathClear(board, from, to);
};

// Validar movimiento de alfil
export const isValidBishopMove = (
  board: (ChessPiece | null)[][],
  piece: ChessPiece,
  to: Position
): boolean => {
  const from = piece.position;
  const rowDiff = Math.abs(to.row - from.row);
  const colDiff = Math.abs(to.col - from.col);

  // El alfil se mueve en diagonal
  if (rowDiff !== colDiff) {
    return false;
  }

  // Verificar que el camino esté libre
  return isPathClear(board, from, to);
};

// Validar movimiento de caballo
export const isValidKnightMove = (piece: ChessPiece, to: Position): boolean => {
  const from = piece.position;
  const rowDiff = Math.abs(to.row - from.row);
  const colDiff = Math.abs(to.col - from.col);

  // El caballo se mueve en L: 2 en una dirección y 1 en la perpendicular
  return (rowDiff === 2 && colDiff === 1) || (rowDiff === 1 && colDiff === 2);
};

// Validar movimiento de reina
export const isValidQueenMove = (
  board: (ChessPiece | null)[][],
  piece: ChessPiece,
  to: Position
): boolean => {
  // La reina combina movimientos de torre y alfil
  return (
    isValidRookMove(board, piece, to) || isValidBishopMove(board, piece, to)
  );
};

// Validar movimiento de rey
export const isValidKingMove = (piece: ChessPiece, to: Position): boolean => {
  const from = piece.position;
  const rowDiff = Math.abs(to.row - from.row);
  const colDiff = Math.abs(to.col - from.col);

  // El rey se mueve una casilla en cualquier dirección
  return rowDiff <= 1 && colDiff <= 1 && (rowDiff !== 0 || colDiff !== 0);
};

// Verificar si una casilla está siendo atacada por el color especificado
export const isSquareAttacked = (
  board: (ChessPiece | null)[][],
  position: Position,
  byColor: PieceColor,
  gameState: GameState
): boolean => {
  for (let row = 0; row < 8; row++) {
    for (let col = 0; col < 8; col++) {
      const piece = board[row][col];
      if (piece && piece.color === byColor) {
        // Crear una copia temporal de la pieza con la nueva posición para validar
        const tempPiece = { ...piece, position: { row, col } };

        switch (piece.type) {
          case "pawn":
            if (isValidPawnMove(board, tempPiece, position, gameState)) {
              return true;
            }
            break;
          case "rook":
            if (isValidRookMove(board, tempPiece, position)) {
              return true;
            }
            break;
          case "bishop":
            if (isValidBishopMove(board, tempPiece, position)) {
              return true;
            }
            break;
          case "knight":
            if (isValidKnightMove(tempPiece, position)) {
              return true;
            }
            break;
          case "queen":
            if (isValidQueenMove(board, tempPiece, position)) {
              return true;
            }
            break;
          case "king":
            if (isValidKingMove(tempPiece, position)) {
              return true;
            }
            break;
        }
      }
    }
  }
  return false;
};

// Verificar si el rey está en jaque
export const isInCheck = (
  board: (ChessPiece | null)[][],
  color: PieceColor,
  gameState: GameState
): boolean => {
  const kingPosition = findKing(board, color);
  if (!kingPosition) return false;

  const opponentColor = color === "white" ? "black" : "white";
  return isSquareAttacked(board, kingPosition, opponentColor, gameState);
};

// Validar si un movimiento es legal (no deja al rey en jaque)
export const isMoveLegal = (
  gameState: GameState,
  piece: ChessPiece,
  to: Position
): MoveValidation => {
  const { board } = gameState;
  const from = piece.position;

  // Verificar que la posición de destino esté dentro del tablero
  if (!isInBounds(to)) {
    return { isValid: false, reason: "Posición fuera del tablero" };
  }

  // Verificar que no se mueva a la misma posición
  if (from.row === to.row && from.col === to.col) {
    return { isValid: false, reason: "No se puede mover a la misma posición" };
  }

  // Verificar que no capture una pieza propia
  const targetPiece = getPieceAt(board, to);
  if (targetPiece && targetPiece.color === piece.color) {
    return { isValid: false, reason: "No se puede capturar pieza propia" };
  }

  // Validar el movimiento según el tipo de pieza
  let isValidMove = false;
  switch (piece.type) {
    case "pawn":
      isValidMove = isValidPawnMove(board, piece, to, gameState);
      break;
    case "rook":
      isValidMove = isValidRookMove(board, piece, to);
      break;
    case "bishop":
      isValidMove = isValidBishopMove(board, piece, to);
      break;
    case "knight":
      isValidMove = isValidKnightMove(piece, to);
      break;
    case "queen":
      isValidMove = isValidQueenMove(board, piece, to);
      break;
    case "king":
      isValidMove = isValidKingMove(piece, to);
      break;
  }

  if (!isValidMove) {
    return { isValid: false, reason: "Movimiento inválido para esta pieza" };
  }

  // Simular el movimiento para verificar si deja al rey en jaque
  const testGameState = cloneGameState(gameState);
  const testBoard = testGameState.board;

  // Realizar el movimiento en el tablero de prueba
  testBoard[from.row][from.col] = null;
  testBoard[to.row][to.col] = { ...piece, position: to };

  // Verificar si el rey queda en jaque después del movimiento
  if (isInCheck(testBoard, piece.color, testGameState)) {
    return {
      isValid: false,
      reason: "El movimiento deja al rey en jaque",
      wouldBeInCheck: true,
    };
  }

  return { isValid: true };
};

// Obtener todos los movimientos válidos para una pieza
export const getValidMoves = (
  gameState: GameState,
  piece: ChessPiece
): Position[] => {
  const validMoves: Position[] = [];

  // Probar todas las posiciones posibles en el tablero
  for (let row = 0; row < 8; row++) {
    for (let col = 0; col < 8; col++) {
      const to = { row, col };
      const validation = isMoveLegal(gameState, piece, to);
      if (validation.isValid) {
        validMoves.push(to);
      }
    }
  }

  return validMoves;
};

// Verificar si hay movimientos válidos para un color (para detectar jaque mate/ahogado)
export const hasValidMoves = (
  gameState: GameState,
  color: PieceColor
): boolean => {
  const { board } = gameState;

  for (let row = 0; row < 8; row++) {
    for (let col = 0; col < 8; col++) {
      const piece = board[row][col];
      if (piece && piece.color === color) {
        const validMoves = getValidMoves(gameState, piece);
        if (validMoves.length > 0) {
          return true;
        }
      }
    }
  }

  return false;
};
