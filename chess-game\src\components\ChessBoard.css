.chess-board {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: repeat(8, 1fr);
  width: 640px;
  height: 640px;
  border: 3px solid #8B4513;
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  background: #8B4513;
  margin: 20px auto;
  position: relative;
}

@media (max-width: 768px) {
  .chess-board {
    width: 90vw;
    height: 90vw;
    max-width: 480px;
    max-height: 480px;
  }
}

@media (max-width: 480px) {
  .chess-board {
    width: 95vw;
    height: 95vw;
    max-width: 360px;
    max-height: 360px;
  }
}

/* Animaciones para transiciones suaves */
.chess-board * {
  transition: all 0.2s ease-in-out;
}

/* Efectos de hover para el tablero completo */
.chess-board:hover {
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
}
