.game-controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  min-width: 280px;
}

/* Estado del juego */
.game-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-radius: 8px;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s ease-in-out;
}

.game-status--playing {
  background: linear-gradient(135deg, #e3f2fd, #bbdefb);
  color: #1565c0;
  border: 2px solid #2196f3;
}

.game-status--check {
  background: linear-gradient(135deg, #fff3e0, #ffcc02);
  color: #e65100;
  border: 2px solid #ff9800;
  animation: check-pulse 1s infinite alternate;
}

.game-status--checkmate {
  background: linear-gradient(135deg, #ffebee, #ffcdd2);
  color: #c62828;
  border: 2px solid #f44336;
  animation: checkmate-glow 2s infinite alternate;
}

.game-status--draw {
  background: linear-gradient(135deg, #f3e5f5, #e1bee7);
  color: #7b1fa2;
  border: 2px solid #9c27b0;
}

.game-status__message {
  font-size: 1.1em;
  flex: 1;
}

.game-status__turn-indicator {
  margin-left: 12px;
}

.turn-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 2px solid;
  transition: all 0.3s ease-in-out;
}

.turn-indicator--white {
  background: #ffffff;
  border-color: #333;
  color: #333;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.turn-indicator--black {
  background: #333;
  border-color: #ffffff;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.turn-indicator__piece {
  font-size: 1.5em;
  line-height: 1;
}

/* Información del juego */
.game-info {
  display: flex;
  justify-content: space-around;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.game-info__item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.game-info__label {
  font-size: 0.85em;
  color: #666;
  font-weight: 500;
}

.game-info__value {
  font-size: 1.2em;
  font-weight: 700;
  color: #333;
}

/* Botones de control */
.game-controls__buttons {
  display: flex;
  gap: 12px;
}

.game-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  border-radius: 6px;
  font-size: 1em;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease-in-out;
  flex: 1;
}

.game-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
}

.game-button:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.game-button:not(:disabled):active {
  transform: translateY(0);
}

.game-button--primary {
  background: linear-gradient(135deg, #2196f3, #1976d2);
  color: white;
}

.game-button--primary:not(:disabled):hover {
  background: linear-gradient(135deg, #1976d2, #1565c0);
}

.game-button--secondary {
  background: linear-gradient(135deg, #f5f5f5, #e0e0e0);
  color: #333;
  border: 1px solid #ccc;
}

.game-button--secondary:not(:disabled):hover {
  background: linear-gradient(135deg, #e0e0e0, #d5d5d5);
}

.game-button--large {
  padding: 16px 24px;
  font-size: 1.1em;
}

.game-button__icon {
  font-size: 1.2em;
  line-height: 1;
}

/* Mensaje de fin de juego */
.game-over-message {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1500;
  animation: fadeIn 0.5s ease-in-out;
}

.game-over-message__content {
  background: white;
  padding: 40px;
  border-radius: 16px;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  animation: slideIn 0.5s ease-out;
  max-width: 400px;
  width: 90%;
}

.game-over-message__content h3 {
  margin: 0 0 16px 0;
  font-size: 1.8em;
  color: #333;
}

.game-over-message__content p {
  margin: 0 0 24px 0;
  font-size: 1.2em;
  color: #666;
}

/* Animaciones */
@keyframes check-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 152, 0, 0.4);
  }
  100% {
    box-shadow: 0 0 0 8px rgba(255, 152, 0, 0);
  }
}

@keyframes checkmate-glow {
  0% {
    box-shadow: 0 0 0 0 rgba(244, 67, 54, 0.4);
  }
  100% {
    box-shadow: 0 0 0 8px rgba(244, 67, 54, 0);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsividad */
@media (max-width: 768px) {
  .game-controls {
    min-width: auto;
    width: 100%;
  }
  
  .game-status {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
  
  .game-status__turn-indicator {
    margin-left: 0;
  }
  
  .game-controls__buttons {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .game-controls {
    padding: 16px;
  }
  
  .game-over-message__content {
    padding: 24px;
  }
  
  .game-over-message__content h3 {
    font-size: 1.5em;
  }
  
  .game-over-message__content p {
    font-size: 1em;
  }
}
