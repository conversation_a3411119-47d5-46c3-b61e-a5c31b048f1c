.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.app-header {
  text-align: center;
  padding: 20px;
  color: white;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-header h1 {
  margin: 0 0 8px 0;
  font-size: 2.5em;
  font-weight: 700;
  letter-spacing: -0.02em;
}

.app-header p {
  margin: 0;
  font-size: 1.1em;
  opacity: 0.9;
  font-weight: 300;
}

.app-main {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  padding: 20px;
  gap: 20px;
}

.game-container {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  max-width: 1200px;
  width: 100%;
}

.game-board-container {
  flex: 0 0 auto;
}

.game-sidebar {
  flex: 0 0 300px;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.app-footer {
  text-align: center;
  padding: 20px;
  color: white;
  font-size: 0.9em;
  opacity: 0.8;
  background: rgba(0, 0, 0, 0.1);
}

.app-footer p {
  margin: 0;
  line-height: 1.5;
}

/* Responsividad */
@media (max-width: 1024px) {
  .game-container {
    flex-direction: column;
    align-items: center;
  }

  .game-sidebar {
    flex: none;
    width: 100%;
    max-width: 640px;
  }
}

@media (max-width: 768px) {
  .app-header h1 {
    font-size: 2em;
  }

  .app-header p {
    font-size: 1em;
  }

  .app-main {
    padding: 10px;
  }

  .game-container {
    gap: 15px;
  }

  .game-sidebar {
    max-width: 90vw;
  }
}

@media (max-width: 480px) {
  .app-header {
    padding: 15px;
  }

  .app-header h1 {
    font-size: 1.8em;
  }

  .app-header p {
    font-size: 0.9em;
  }

  .app-main {
    padding: 5px;
  }

  .app-footer {
    padding: 15px;
    font-size: 0.8em;
  }
}
